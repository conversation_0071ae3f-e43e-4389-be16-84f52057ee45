name: Build and Deploy Docker Image to the NOS Artifactory

on:
  push:
    tags:
      - '[0-9]+.[0-9]+.[0-9]+' # matches tags like 1.2.3
      - '[0-9]+.[0-9]+.[0-9]+-[ab][0-9]+' # matches tags like 1.2.3-a0 or 1.2.3-b1

  # this only triggers docker build, but not push - see passed push: ${{ github.event_name == 'push' }}
  # pull_request:
  # branches:
  #   - main

env:
  NOS_REGISTRY: nosartifactory.jfrog.io
  NOS_IMAGE_NAME: docker-gen-os-backend-local/gen-os-am
  DOCKER_BUILDKIT: 1
  DOCKER_CLI_EXPERIMENTAL: enabled
  BUILDKIT_INLINE_CACHE: 1
  BUILDKIT_PROGRESS: plain

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to JFrog Artifactory
        uses: docker/login-action@v3
        with:
          registry: ${{ env.NOS_REGISTRY }}
          username: ${{ secrets.ARTIFACTORY_USERNAME }}
          password: ${{ secrets.ARTIFACTORY_PASSWORD }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          tags: ${{ env.NOS_REGISTRY }}/${{ env.NOS_IMAGE_NAME }}:${{ github.ref_name }}
