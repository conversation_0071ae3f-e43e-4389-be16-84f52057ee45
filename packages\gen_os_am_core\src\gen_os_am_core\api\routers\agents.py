# ruff: noqa: I001, E402
"""Agents router for the Agent Manager API."""
import logging
import httpx
from typing import Literal

from a2a.types import Agent<PERSON>ard
from fastapi import APIRouter, HTTPException, status
from gen_os_sdk_emulator.core.database.crud import get_basemodel_from_table
from pydantic import BaseModel, ValidationError
from sqlalchemy import select
from sqlalchemy.exc import IntegrityError

from gen_os_am_core.api.schemas import AgentCreateRequest, AgentUpdateRequest
from gen_os_am_core.database.models.agents import Agent
from gen_os_am_core.database.session import SessionManager
from gen_os_am_core.models.response import StatusResponse
class AgentsRouter:
    """Agents router for the Agent Manager API."""

    def __init__(self, logger: logging.Logger | None = None):
        """Initialize the agents router.

        Args:
            logger: Optional logger instance

        """
        self.logger = logger or logging.getLogger(__name__)
        self.router = APIRouter()
        self._setup_routes()

    class AgentStatus(BaseModel):
        """Agent status."""

        status: Literal[
            "agent is live",
            "agent is not live",
            "agent is not following the A2A protocol",
        ]
        """The status of the agent."""

        agent_card: dict | None = None
        """The agent card of the agent."""

    async def _check_agent_status(self, agent_url: str) -> AgentStatus:
        """Check the status of an agent.

        Args:
            agent_url: URL of the agent to check

        Returns:
            Status message indicating agent readiness

        """
        try:
            # Check if agent is alive by pinging base URL
            async with httpx.AsyncClient() as client:
                response = await client.head(agent_url)
                if response.status_code not in (200, 404, 405):
                    return self.AgentStatus(status="agent is not live")

                # Check well-known agent.json endpoint
                well_known_url = f"{agent_url.rstrip('/')}/.well-known/agent.json"
                response = await client.get(well_known_url)
                if response.status_code != 200:
                    return self.AgentStatus(
                        status="agent is not following the A2A protocol"
                    )
                agent_card = response.json()
                # validate agent card
                try:
                    AgentCard(**agent_card)
                except ValidationError:
                    return self.AgentStatus(
                        status="agent is not following the A2A protocol"
                    )

                # Return well-known endpoint content
                return self.AgentStatus(status="agent is live", agent_card=agent_card)

        except httpx.RequestError:
            return self.AgentStatus(status="agent is not live")
        except httpx.HTTPStatusError:
            return self.AgentStatus(status="agent is not live")

    def _setup_routes(self):
        """Set up the agent routes."""

        @self.router.post(
            "/agents",
            tags=["Agents"],
            status_code=status.HTTP_201_CREATED,
            response_model=StatusResponse,
        )
        async def create_agent(agent: AgentCreateRequest) -> StatusResponse:
            """Create a new agent.

            Raises:
                HTTPException: If an agent with the same ID already exists

            """
            try:
                async with SessionManager.get_session() as db:
                    # Check if agent with same ID exists
                    stmt = select(Agent).where(Agent.id == agent.id)
                    result = await db.execute(stmt)
                    existing_agent = result.scalar_one_or_none()

                    if existing_agent:
                        raise HTTPException(
                            status_code=status.HTTP_409_CONFLICT,
                            detail=f"Agent with ID '{agent.id}' already exists",
                        )

                    agent_status = await self._check_agent_status(agent.url)

                    # Create new agent
                    new_agent = Agent(
                        id=agent.id,
                        name=agent.name,
                        url=agent.url,
                        description=agent.description,
                    )
                    db.add(new_agent)
                    await db.commit()

                    self.logger.info(f"Created new agent: {agent.id}")
                    return StatusResponse(
                        status="ok",
                        message=f"Agent {agent.id} created with status: "
                        f"{agent_status.status}",
                    )

            except IntegrityError as e:
                self.logger.error(
                    f"Database integrity error creating agent: {e}", exc_info=True
                )
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail="Agent with this ID already exists",
                ) from e
            except Exception as e:
                self.logger.error(
                    f"Unexpected error creating agent: {e}", exc_info=True
                )
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"An unexpected error occurred: {str(e)}",
                ) from e

        @self.router.patch(
            "/agents/{agent_id}",
            tags=["Agents"],
            response_model=StatusResponse,
        )
        async def update_agent(
            agent_id: str, agent: AgentUpdateRequest
        ) -> StatusResponse:
            """Update an agent by ID.

            Args:
                agent_id: ID of the agent to update
                agent: New agent data

            Raises:
                HTTPException: If agent not found

            """
            try:
                async with SessionManager.get_session() as db:
                    # Get the existing agent
                    stmt = select(Agent).where(Agent.id == agent_id)
                    result = await db.execute(stmt)
                    existing_agent = result.scalar_one_or_none()

                    if not existing_agent:
                        raise HTTPException(
                            status_code=status.HTTP_404_NOT_FOUND,
                            detail=f"Agent with ID '{agent_id}' not found",
                        )

                    # Update agent fields
                    if agent.name is not None:
                        existing_agent.name = agent.name
                    if agent.url is not None:
                        existing_agent.url = agent.url
                    if agent.description is not None:
                        existing_agent.description = agent.description

                    await db.commit()

                    self.logger.info(f"Updated agent: {agent_id}")
                    return StatusResponse(status="ok")

            except HTTPException:
                raise
            except Exception as e:
                self.logger.error(
                    f"Unexpected error updating agent: {e}", exc_info=True
                )
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"An unexpected error occurred: {str(e)}",
                ) from e

        @self.router.get(
            "/agents",
            tags=["Agents"],
            response_model=list[get_basemodel_from_table(Agent)],
        )
        async def get_agents():
            """Get all agents."""
            try:
                async with SessionManager.get_session() as db:
                    stmt = select(Agent)
                    result = await db.execute(stmt)
                    agents = result.scalars().all()

                    return agents

            except Exception as e:
                self.logger.error(
                    f"Unexpected error getting agents: {e}", exc_info=True
                )
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"An unexpected error occurred: {str(e)}",
                ) from e

        @self.router.delete(
            "/agents/{agent_id}", tags=["Agents"], response_model=StatusResponse
        )
        async def delete_agent(agent_id: str) -> StatusResponse:
            """Delete an agent by ID."""
            try:
                async with SessionManager.get_session() as db:
                    # Get the agent to delete
                    stmt = select(Agent).where(Agent.id == agent_id)
                    result = await db.execute(stmt)
                    agent = result.scalar_one_or_none()

                    if not agent:
                        raise HTTPException(
                            status_code=status.HTTP_404_NOT_FOUND,
                            detail=f"Agent with ID '{agent_id}' not found",
                        )

                    await db.delete(agent)
                    await db.commit()

                    self.logger.info(f"Deleted agent: {agent_id}")
                    return StatusResponse(status="ok")

            except HTTPException:
                raise
            except Exception as e:
                self.logger.error(
                    f"Unexpected error deleting agent: {e}", exc_info=True
                )
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"An unexpected error occurred: {str(e)}",
                ) from e

    def get_router(self) -> APIRouter:
        """Get the FastAPI router instance.

        Returns:
            The configured FastAPI router.

        """
        return self.router
