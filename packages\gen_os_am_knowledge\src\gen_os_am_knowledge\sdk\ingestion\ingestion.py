"""Module for ingestion operations."""

import asyncio
import logging
import traceback
from datetime import datetime, timezone
from typing import Any, Literal
from uuid import UUID

from pydantic import BaseModel, ConfigDict
from pydantic.dataclasses import dataclass

from gen_os_am_knowledge.config.logger_config import get_sdk_logger
from gen_os_am_knowledge.config.settings import KnowledgeManagementIngestionSettings
from gen_os_am_knowledge.sdk.filesystem_connectors import BaseFileSystemConnector
from gen_os_am_knowledge.sdk.models import Folder, Ingestion, Source
from gen_os_am_knowledge.sdk.models.common import DEFAULT_USER
from gen_os_am_knowledge.sdk.operations import (
    DocumentService,
    FolderService,
    IngestionService,
    SourceService,
)
from gen_os_am_knowledge.sdk.operations.utils import NOT_PROVIDED
from gen_os_am_knowledge.sdk.utils.hash import calculate_hash
from gen_os_am_knowledge.sdk.utils.protocols import NamedFileLike
from gen_os_am_knowledge.utils.ctt.ctt_utils.logging.logging_utils import (
    error_event_log,
    log_decorator,
)


class IngestionDocument(BaseModel):
    """Represent a document to be ingested.

    Attributes:
        file: A NamedFileLike object that represents the file to be ingested.
            The name of the file is taken from this object.
        storage_folder_path: Path to the storage folder where the
            file should be stored, i.e "1/2/3/4/", excluding root folder of ingestion.
        original_path: Optional string that represents the original path of the file.
        meta: Optional dictionary that represents the metadata of the file.
        tags: Optional list of strings that represents the tags of the file.
        hash: xxhash64 in bigint format: hash of the file content,
            file name and source name
        database_id: UUID that represents the id of the document in the database
        state: String that represents the state of the document, can be "new",
            "created", "updated", "unchanged", "failed"
        updated_values: Optional dictionary that represents the updated values
            of the document. Only set for "updated" state.
            If the hash is in updates_values, it means
            that the file content was changed.

    """

    file: NamedFileLike  # name is taken from here, so must be set in advance
    storage_folder_path: (
        str  # i.e. 1/2/3/4/ so we could check if it exists. If not, create it.
    )
    original_path: str | None = None
    meta: dict[str, Any] | None = None
    tags: list[str] | None = None
    # Fields below are set during ingestion
    hash: int | None = None
    database_id: UUID | None = None
    draft: bool | None = None
    state: Literal["new", "created", "updated", "unchanged", "failed"] = "new"
    updated_values: dict[str, tuple[Any, Any]] | None = (
        None  # Only set for updated state.
        # If hash was updated, file content was changed
    )

    model_config = ConfigDict(arbitrary_types_allowed=True)


@dataclass
class IngestionResult:
    """Represent the result of an ingestion operation.

    Attributes:
        ingested_documents: List of IngestionDocument objects that successfully
            went through ingestion.
        not_ingested_documents: List of tuples, where each tuple contains
            an IngestionDocument object
            and a string that represents the reason why the document was not ingested.

    """

    ingested_documents: list[IngestionDocument]
    not_ingested_documents: list[
        tuple[IngestionDocument, str]
    ]  # list of documents and reason why they were not ingested


class KnowledgeManagementIngestion:
    """Represent an ingestion operation.

    Args:
    - ingestion_description: a string that represents the description of the ingestion.
    - source_name: a string that represents the name of the source.
    - folder_id: a UUID that represents the id of the root folder for the ingestion.
    - fs_connector: a BaseFileSystemConnector object that represents the file
        system connector.
    - ingestion_meta: an optional dictionary that represents the metadata
        of the ingestion.
    - draft_mode: an optional boolean that represents whether the ingestion is in
        draft mode or not. Under draft mode, all the dcouments are ingested
        with draft flag set to True. Default is False.

    """

    def __init__(
        self,
        ingestion_description: str,
        source_name: str,  # should exist in db
        folder_id: UUID,  # should exist in db
        fs_connector: BaseFileSystemConnector,
        ingestion_meta: dict[str, Any] | None = None,
        draft_mode: bool | None = None,
        logger: logging.Logger | None = None,
    ):
        """Initialize the KnowledgeManagementIngestion class.

        Args:
            ingestion_description: The description of the ingestion.
            source_name: The name of the source.
            folder_id: The id of the folder.
            fs_connector: The file system connector.
            ingestion_meta: The metadata of the ingestion.
            draft_mode: Whether the ingestion is in draft mode.
            logger: The logger.

        """
        self.logger = logger or get_sdk_logger()
        self.sdk_logger = self.logger.getChild("am_ingestion_sdk")
        self.doc_service = DocumentService(fs_connector, logger=self.sdk_logger)
        self.ingestion_description = ingestion_description
        self.source_name = source_name
        self.ingestion_meta = ingestion_meta
        self.folder_service = FolderService(fs_connector, logger=self.sdk_logger)
        self.ingestion_service = IngestionService(logger=self.sdk_logger)
        self.folder_id = folder_id

        settings = KnowledgeManagementIngestionSettings.get_settings()
        self.draft_mode = draft_mode if draft_mode else settings.INGESTION_DRAFT_MODE

    @log_decorator()
    async def _init_ingestion(self):
        # cannot be done in __init__ as it is async
        self.logger.debug(
            f"Initializing ingestion for source {self.source_name} "
            f"and description {self.ingestion_description}"
        )
        start = datetime.now(timezone.utc)

        self.source = await self._check_source(start)

        self.folder = await self._check_folder(start)

        self.ingestion = await self._check_ingestion()

    @log_decorator(category=["database", "file"])
    async def ingest(
        self, documents: list[IngestionDocument] | None = None, overwrite: bool = False
    ):
        """Ingest a list of IngestionDocument objects.

        Args:
            documents: A list of IngestionDocument objects to be ingested.
            overwrite: A boolean that represents whether to overwrite existing documents
                with new contents (on hash match) or other meta fields
                (on filename match).

        Returns:
            IngestionResult: An object that represents the result of
            the ingestion operation.

        """
        self.logger.info(
            f"Starting ingestion of {len(documents)} documents "
            f"from source {self.source_name}"
        )
        await self._init_ingestion()

        # 0. Put to ongoing status and clear error if any
        await self.ingestion_service.update_ingestion(
            ingestion_id=self.ingestion.id, status="Ongoing", error=None
        )

        documents = self._calculate_hash(documents)

        ingested_documents = []
        not_ingested_documents = []

        for doc in documents:
            try:
                # create folders if needed and get ids at the same time
                folders = await self.folder_service.create_folders_from_path(
                    self.folder.name + "/" + doc.storage_folder_path,
                    user_id=DEFAULT_USER.id,
                )
                # check hash in that folder
                existing_doc = await self.doc_service.search_documents(
                    hash_=doc.hash, folder_id=folders[-1].id, draft=True
                )
                # create/update/skip document
                if len(existing_doc) == 1 and not overwrite:
                    doc.state = "unchanged"
                    doc.database_id = existing_doc[0].id
                    doc.draft = existing_doc[0].draft
                    not_ingested_documents.append(
                        (
                            doc,
                            "Document with the same hash already exists in the folder",
                        )
                    )
                    doc.file.seek(0)
                    continue
                elif len(existing_doc) == 1 and overwrite:
                    # we update only fields, not the file as its hash is the same
                    update_result = await self.doc_service.update_document(
                        document_id=existing_doc[0].id,
                        last_modified_by_id=DEFAULT_USER.id,
                        original_path=doc.original_path
                        if doc.original_path is not None
                        else NOT_PROVIDED,
                        meta=doc.meta if doc.meta is not None else NOT_PROVIDED,
                        tags=doc.tags if doc.tags is not None else NOT_PROVIDED,
                        draft=self.draft_mode,  # TODO: check if there is use case
                        # to update document to become draft.
                    )
                    document = update_result.updated_instance
                    doc.draft = document.draft

                    if update_result.updated_values:
                        doc.state = "updated"
                        doc.updated_values = update_result.updated_values
                    else:
                        doc.state = "unchanged"

                    # TODO: check if there is use case to update document to
                    # become draft or published in ingestion.
                    # if self.draft_mode and not existing_doc[0].draft:
                    #     update_result = await self.doc_service.draft_document(
                    #         existing_doc[0].id, user_id=DEFAULT_USER.id
                    #     )
                    #     document = update_result.updated_instance
                    #     doc.state = "updated"
                    #     doc.draft = True
                    #     if doc.updated_values is None:
                    #         doc.updated_values = {}
                    #     doc.updated_values["draft"] = (False, True)
                    # elif not self.draft_mode and existing_doc[0].draft:
                    #     update_result = await self.doc_service.publish_document(
                    #         existing_doc[0].id, user_id=DEFAULT_USER.id
                    #     )
                    #     document = update_result.updated_instance
                    #     doc.state = "updated"
                    #     if doc.updated_values is None:
                    #         doc.updated_values = {}
                    #     doc.updated_values["draft"] = (True, False)
                    #     doc.draft = False
                    # else:
                    #     doc.draft = existing_doc[0].draft
                else:
                    # if there is no document with the same hash,
                    # we need to search by filename to overwrite content
                    same_name_doc = await self.doc_service.search_documents(
                        folder_id=folders[-1].id, filename=doc.file.name
                    )

                    if len(same_name_doc) == 1 and overwrite:
                        update_result = await self.doc_service.update_document(
                            document_id=same_name_doc[0].id,
                            last_modified_by_id=DEFAULT_USER.id,
                            original_path=doc.original_path
                            if doc.original_path is not None
                            else NOT_PROVIDED,
                            meta=doc.meta if doc.meta is not None else NOT_PROVIDED,
                            tags=doc.tags if doc.tags is not None else NOT_PROVIDED,
                            file=doc.file,
                            draft=self.draft_mode,  # TODO: check if there is use case
                            # to update document to become draft.
                        )
                        document = update_result.updated_instance
                        doc.draft = document.draft

                        if update_result.updated_values:
                            doc.state = "updated"
                            doc.updated_values = update_result.updated_values
                        else:
                            doc.state = "unchanged"

                        # TODO: check if there is use case to update document to
                        # become draft or published in ingestion.
                        # if not self.draft_mode and same_name_doc[0].draft:
                        #     update_result = await self.doc_service.publish_document(
                        #         same_name_doc[0].id, user_id=DEFAULT_USER.id
                        #     )
                        #     document = update_result.updated_instance
                        #     doc.state = "updated"
                        #     doc.draft = False
                        #     if doc.updated_values is None:
                        #         doc.updated_values = {}
                        #     doc.updated_values["draft"] = (True, False)
                        # else:
                        #     doc.draft = same_name_doc[0].draft

                    elif len(same_name_doc) == 1 and not overwrite:
                        doc.state = "unchanged"
                        doc.database_id = same_name_doc[0].id
                        doc.draft = same_name_doc[0].draft
                        not_ingested_documents.append(
                            (
                                doc,
                                f"Document with the same name already "
                                f"exists in the folder",
                            )
                        )
                        doc.file.seek(0)
                        continue
                    else:
                        document = await self.doc_service.create_document(
                            file=doc.file,
                            folder_id=folders[-1].id,
                            original_path=doc.original_path,
                            meta=doc.meta,
                            tags=doc.tags,
                            hash_=doc.hash,
                            ingestion_id=self.ingestion.id,
                            draft=self.draft_mode,
                        )
                        doc.state = "created"
                        doc.draft = self.draft_mode
                doc.file.seek(0)
                doc.database_id = document.id
                ingested_documents.append(doc)
                if doc.state != "unchanged":
                    await asyncio.sleep(2)
            except Exception:
                doc.state = "failed"
                not_ingested_documents.append((doc, traceback.format_exc()))

        return IngestionResult(ingested_documents, not_ingested_documents)

    def _calculate_hash(self, documents: list[IngestionDocument]):
        for doc in documents:
            doc.hash = calculate_hash(doc.file)
        return documents

    async def _check_source(self, start: datetime) -> Source:
        # Check if source exists
        source = await SourceService(logger=self.sdk_logger).search_sources(
            source_name=self.source_name
        )
        if len(source) == 0:
            message = (
                f"Source {self.source_name} not found in DB. "
                "It has to be created before ingestion"
            )
            error_event_log(
                msg=message,
                start=start,
                error=Exception(message),
                logger=self.sdk_logger,
            )

            raise Exception(message)
        else:
            return source[0]

    async def _check_folder(self, start: datetime) -> Folder:
        # Check if folder exists
        folder = await self.folder_service.get_folder(folder_id=self.folder_id)
        if not folder:
            message = (
                f"Folder with id {self.folder_id} not found in DB. "
                "It has to be created before ingestion"
            )
            error_event_log(
                msg=message,
                start=start,
                error=Exception(message),
                logger=self.sdk_logger,
            )

            raise Exception(message)
        else:
            return folder

    async def _check_ingestion(self) -> Ingestion:
        # Check if ingestion exists
        ingestion = await self.ingestion_service.search_ingestions(
            description=self.ingestion_description
        )
        if len(ingestion) == 1:
            return ingestion[0]
        else:
            return await self.ingestion_service.create_ingestion(
                source_id=self.source.id,
                description=self.ingestion_description,
                meta=self.ingestion_meta,
                folder_id=self.folder_id,
            )
