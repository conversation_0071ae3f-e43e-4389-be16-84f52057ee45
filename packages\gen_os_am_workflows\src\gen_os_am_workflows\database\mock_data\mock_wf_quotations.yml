workflow:
  name: 'workflow'
  description: 'A workflow that processes quotation requests via email, places orders and sends emails back'
  updated_at: now
  created_at: now
  steps:
    - name: 'extraction'
      description: 'Extract the product names, descripiton and amount from the request'
      step_type: 'gen_ai'
      created_at: now
      order_number: 1
      inputs:
        - step_block_type: 'text_unstructured'
          status: 'non_editable'
          step_block_name: 'filepath'
          order_number: 1
          previous_step_fed: {}
      outputs:
        - step_block_type: 'dataset'
          status: 'editable'
          step_block_name: 'extracted_products'
          order_number: 1

    - name: 'product_match'
      description: 'Find best matches for the requested products with the ones on the the products database'
      step_type: 'gen_ai'
      created_at: now
      order_number: 2
      inputs:
        - step_block_type: 'dataset'
          status: 'non_editable'
          step_block_name: 'extracted_products'
          order_number: 1
          previous_step_fed:
            step_name: 'extraction'
            step_block_name: 'extracted_products'
      outputs:
        - step_block_type: 'dataset'
          status: 'editable'
          step_block_name: 'selected_products'
          order_number: 1

    - name: 'create_email'
      description: 'Create email based on the product match'
      step_type: 'gen_ai'
      created_at: now
      order_number: 2
      inputs:
        - step_block_type: 'dataset'
          status: 'non_editable'
          step_block_name: 'selected_products'
          order_number: 1
          previous_step_fed:
            step_name: 'product_match'
            step_block_name: 'selected_products'
        - step_block_type: 'text_unstructured'
          status: 'non_editable'
          step_block_name: 'customer_email'
          order_number: 2
          previous_step_fed: {}
        - step_block_type: 'text_unstructured'
          status: 'non_editable'
          step_block_name: 'customer_name'
          order_number: 3
          previous_step_fed: {}
      outputs:
        - step_block_type: 'text_structured'
          status: 'editable'
          step_block_name: 'email'
          order_number: 1

    - name: 'send_email_place_order'
      description: 'Send email with quotation and possibly place the order'
      step_type: 'action'
      created_at: now
      order_number: 3
      inputs:
        - step_block_type: 'dataset'
          status: 'non_editable'
          step_block_name: 'selected_products'
          order_number: 1
          previous_step_fed:
            step_name: 'product_match'
            step_block_name: 'selected_products'
        - step_block_type: 'email'
          status: 'non_editable'
          step_block_name: 'email'
          order_number: 1
          previous_step_fed:
            step_name: 'create_email'
            step_block_name: 'email'
