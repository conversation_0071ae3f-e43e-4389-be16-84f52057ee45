"""Module for the LocalFSConnector."""

import os
import tempfile
from pathlib import Path
from typing import override

import fsspec

from gen_os_am_knowledge.config.settings import BaseKmSettings
from gen_os_am_knowledge.sdk.filesystem_connectors.base import (
    BaseFileSystemConnector,
    FSEntity,
)


class LocalFSConnector(BaseFileSystemConnector):
    """Connector for interacting with the local filesystem.

    Args:
        root_folder : str : The root folder of the filesystem
        local_storage_path : str | None : The path to the local storage. If None,
        the path is taken from the settings

    """

    def __init__(self, root_folder: str, local_storage_path: str | None = None):
        """Initialize the LocalFSConnector."""

        class LocalSettings(BaseKmSettings):
            LOCAL_STORAGE_PATH: str

        settings = LocalSettings.get_settings()
        self.local_storage_path = (
            local_storage_path if local_storage_path else settings.LOCAL_STORAGE_PATH
        )

        self.root_folder = root_folder

        self.filesystem = fsspec.filesystem("file")
        self.prefix = f"{self.local_storage_path}/{self.root_folder}/"

    @override
    @BaseFileSystemConnector.ensure_folder_path(param_names=["folder_path"])
    def create_folder(
        self, folder_path: str, create_parent_folders_if_not_exist: bool = True
    ) -> FSEntity:
        self.filesystem.mkdir(
            f"{self.prefix}{folder_path}",
            create_parents=create_parent_folders_if_not_exist,
        )
        return FSEntity(path=folder_path, is_folder=True, is_file=False)

    @override
    @BaseFileSystemConnector.ensure_folder_path(param_names=["folder_path"])
    def ls(self, folder_path: str):
        abs_path = Path(self.prefix + folder_path).resolve()
        return [
            Path(file["name"]).relative_to(abs_path).as_posix() + "/"
            if file["type"] == "directory"
            else Path(file["name"]).relative_to(abs_path).as_posix()
            for file in self.filesystem.ls(f"{self.prefix}{folder_path}", detail=True)
        ]

    @override
    @BaseFileSystemConnector.ensure_folder_path(param_names=["folder_path"])
    def delete_folder(self, folder_path: str):
        if self.filesystem.isdir(
            f"{self.prefix}{folder_path}"
        ) and self.filesystem.exists(f"{self.prefix}{folder_path}"):
            self.filesystem.rm(f"{self.prefix}{folder_path}", recursive=True)
            return FSEntity(path=folder_path, is_folder=True, is_file=False)
        else:
            raise Exception(f"Folder {folder_path} does not exist")

    @override
    @BaseFileSystemConnector.ensure_folder_path(param_names=["folder_path"])
    def download_file(self, folder_path: str, filename: str):
        return self.filesystem.open(f"{self.prefix}{folder_path}{filename}", mode="rb")

    @override
    @BaseFileSystemConnector.ensure_folder_path(param_names=["folder_path"])
    def upload_file(
        self,
        folder_path,
        filename,
        file,
        create_parent_folders_if_not_exists: bool = True,
        overwrite: bool = False,
    ):
        temp_file = tempfile.NamedTemporaryFile(delete=False)
        temp_file_path = temp_file.name
        with open(temp_file_path, "wb") as temp_storage:
            temp_storage.write(file.read())

        try:
            if not self.filesystem.exists(f"{self.prefix}{folder_path}"):
                if create_parent_folders_if_not_exists:
                    self.create_folder(folder_path, create_parent_folders_if_not_exists)
                else:
                    raise Exception(f"Folder {folder_path} does not exist")

            if overwrite:
                self.filesystem.put_file(
                    temp_file_path,
                    f"{self.prefix}{folder_path}{filename}",
                    mode="overwrite",
                )
            else:
                if self.filesystem.exists(f"{self.prefix}{folder_path}{filename}"):
                    raise Exception(f"File {filename} already exists")
                self.filesystem.put_file(
                    temp_file_path, f"{self.prefix}{folder_path}{filename}"
                )

            return FSEntity(
                path=f"{folder_path}{filename}", is_folder=False, is_file=True
            )
        finally:
            os.remove(temp_file_path)

    @override
    @BaseFileSystemConnector.ensure_folder_path(param_names=["folder_path"])
    def delete_file(self, folder_path, filename):
        if self.filesystem.isfile(f"{self.prefix}{folder_path}{filename}"):
            self.filesystem.rm(f"{self.prefix}{folder_path}{filename}")
            return FSEntity(
                path=f"{folder_path}{filename}", is_folder=False, is_file=True
            )
        else:
            raise Exception(f"File {folder_path}{filename} does not exist")

    @override
    def copy_file(
        self, original_file_path, destination_file_path, overwrite: bool = False
    ):
        if overwrite:
            self.filesystem.cp(
                f"{self.prefix}{original_file_path}",
                f"{self.prefix}{destination_file_path}",
            )
        else:
            if self.filesystem.exists(f"{self.prefix}{destination_file_path}"):
                raise Exception(f"File {destination_file_path} already exists")
            self.filesystem.cp(
                f"{self.prefix}{original_file_path}",
                f"{self.prefix}{destination_file_path}",
            )

        return FSEntity(path=destination_file_path, is_folder=False, is_file=True)

    @override
    def move_file(
        self, original_file_path, destination_file_path, overwrite: bool = False
    ):
        if overwrite:
            self.filesystem.mv(
                f"{self.prefix}{original_file_path}",
                f"{self.prefix}{destination_file_path}",
            )
        else:
            if self.filesystem.exists(f"{self.prefix}{destination_file_path}"):
                raise Exception(f"File {destination_file_path} already exists")
            self.filesystem.mv(
                f"{self.prefix}{original_file_path}",
                f"{self.prefix}{destination_file_path}",
            )

    @override
    @BaseFileSystemConnector.ensure_folder_path(
        param_names=["original_folder_path", "destination_folder_path"]
    )
    def move_folder(
        self,
        original_folder_path,
        destination_folder_path,
        overwrite: bool = False,
        with_folder: bool = True,
    ):
        if not overwrite and with_folder:
            if self.filesystem.exists(
                f"{self.prefix}{destination_folder_path}{self._separate_last_segment(original_folder_path)[1]}/"
            ):
                raise Exception(f"Folder {destination_folder_path} already exists")
        if with_folder:
            self.filesystem.cp(
                f"{self.prefix}{original_folder_path.rstrip('/')}",
                f"{self.prefix}{destination_folder_path}",
                recursive=True,
            )
            self.filesystem.rm(
                f"{self.prefix}{original_folder_path}",
                recursive=True,
            )
        else:
            self.filesystem.cp(
                f"{self.prefix}{original_folder_path}",
                f"{self.prefix}{destination_folder_path}",
                recursive=True,
            )
            self.filesystem.rm(
                f"{self.prefix}{original_folder_path}",
                recursive=True,
            )

    @override
    @BaseFileSystemConnector.ensure_folder_path(
        param_names=["original_folder_path", "destination_folder_path"]
    )
    def copy_folder(
        self,
        original_folder_path,
        destination_folder_path,
        overwrite: bool = False,
        with_folder: bool = True,
    ):
        if not overwrite and with_folder:
            if self.filesystem.exists(
                f"{self.prefix}{destination_folder_path}{self._separate_last_segment(original_folder_path)[1]}/"
            ):
                raise Exception(f"Folder {destination_folder_path} already exists")
        if with_folder:
            self.filesystem.cp(
                f"{self.prefix}{original_folder_path.rstrip('/')}",
                f"{self.prefix}{destination_folder_path}",
                recursive=True,
            )
        else:
            self.filesystem.cp(
                f"{self.prefix}{original_folder_path}",
                f"{self.prefix}{destination_folder_path}",
                recursive=True,
            )

    @override
    @BaseFileSystemConnector.ensure_folder_path(param_names=["folder_path"])
    def generate_presigned_url(self, folder_path: str, filename: str) -> str:
        """Generate a presigned URL for a file in the local filesystem.

        This is a placeholder implementation since local filesystems do not support
        presigned URLs.

        Args:
            folder_path : str : The path of the folder containing the file.
            filename : str : The name of the file for which to generate the URL.

        Returns:
            str : A placeholder URL.

        """
        return f"{self.prefix}{folder_path}{filename}"

    @staticmethod
    def _separate_last_segment(path: str) -> str:
        head, sep, tail = path.rstrip("/").rpartition("/")
        return (head + "/" if head else "", tail)
