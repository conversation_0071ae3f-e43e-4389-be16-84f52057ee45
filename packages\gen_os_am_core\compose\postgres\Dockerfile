ARG PG_MAJOR=17
ARG PG_MINOR=5
FROM postgres:$PG_MAJOR.$PG_MINOR-bookworm
ARG PG_MAJOR
ARG PG_MINOR

RUN apt-get update

RUN	apt-mark hold locales

RUN	apt-get install -y --no-install-recommends build-essential postgresql-server-dev-$PG_MAJOR git ca-certificates

RUN git clone --branch v0.7.4 https://github.com/pgvector/pgvector.git /tmp/pgvector

RUN	cd /tmp/pgvector && \
		make clean && \
		make OPTFLAGS="" && \
		make install && \
		mkdir /usr/share/doc/pgvector && \
		cp LICENSE README.md /usr/share/doc/pgvector && \
		rm -r /tmp/pgvector && \
		apt-get remove -y build-essential postgresql-server-dev-$PG_MAJOR git && \
		apt-get autoremove -y && \
		apt-mark unhold locales && \
		rm -rf /var/lib/apt/lists/*
