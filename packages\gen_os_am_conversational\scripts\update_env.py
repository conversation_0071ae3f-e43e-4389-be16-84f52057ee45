"""Update the .env file with the correct database host for the specified environment."""

import argparse
from pathlib import Path
from typing import Literal

from dotenv import dotenv_values

EnvironmentType = Literal["local", "docker"]


def get_host(env_type: EnvironmentType) -> str:
    """Get the database host for the specified environment.

    Args:
        env_type: The type of environment to get the host for.

    Returns:
        The database host for the specified environment.

    """
    return "conversational-postgres" if env_type == "docker" else "localhost"


def update_env_file(env_type: EnvironmentType) -> None:
    """Update the .env file with the correct database host for the specified environment.

    Args:
        env_type: The type of environment to update for.

    """
    print(f"Updating environment file for {env_type} environment...")  # noqa: T201
    env_path = Path(__file__).parent.parent / ".env"

    if not env_path.exists():
        print(f"Error: {env_path} not found")  # noqa: T201
        return

    # Load current env vars
    config = dotenv_values(env_path)

    # Update only the DATABASE_HOST
    config["DATABASE_HOST"] = get_host(env_type)

    # Write back to .env
    with open(env_path, "w") as f:
        for key, value in config.items():
            f.write(f"{key}={value}\n")

    print(f"Environment file updated successfully for {env_type} environment!")  # noqa: T201


def main() -> None:
    """Entry point for the script."""
    parser = argparse.ArgumentParser(
        description="Update database host for local or docker environment."
    )
    parser.add_argument(
        "environment",
        choices=["local", "docker"],
        help="The environment to update for (local or docker)",
    )

    args = parser.parse_args()
    update_env_file(args.environment)  # type: ignore


if __name__ == "__main__":
    main()
