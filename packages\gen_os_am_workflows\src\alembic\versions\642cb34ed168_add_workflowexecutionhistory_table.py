"""Add WorkflowExecutionHistory table.

Revision ID: 642cb34ed168
Revises: fe9b2089eab1
Create Date: 2025-06-05 18:53:14.003442

"""

from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "642cb34ed168"
down_revision: str | None = "fe9b2089eab1"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Add the workflow_execution_history table."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "workflow_execution_history",
        sa.Column("id", sa.Uuid(), nullable=False),
        sa.Column("workflow_execution_id", sa.Uuid(), nullable=False),
        sa.Column("event", sa.String(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("termination_message", sa.String(), nullable=True),
        sa.Column("error_message", sa.String(), nullable=True),
        sa.Column("step_name", sa.String(), nullable=True),
        sa.ForeignKeyConstraint(
            ["workflow_execution_id"],
            ["workflow_execution.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Drop the workflow_execution_history table."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("workflow_execution_history")
    # ### end Alembic commands ###
