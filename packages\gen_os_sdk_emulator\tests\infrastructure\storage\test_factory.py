"""Unit tests for the storage factory module."""

import pytest
from gen_os_sdk_emulator.infrastructure.storage.factory import StorageFactory
from gen_os_sdk_emulator.infrastructure.storage.storage import GCPStorageService


class TestStorageFactory:
    """Tests for the StorageFactory class."""

    def test_create_storage_service_with_default_provider(self):
        """Test creating a storage service with the default provider."""
        # Act
        service = StorageFactory.create_storage_service(
            bucket_name="test-bucket", project_name="test-project"
        )

        # Assert
        assert isinstance(service, GCPStorageService)
        assert service.bucket_name == "test-bucket"
        assert service.project_name == "test-project"

    def test_create_storage_service_with_explicit_provider(self):
        """Test creating a storage service with an explicitly specified provider."""
        # Act
        service = StorageFactory.create_storage_service(
            provider="gcp", bucket_name="custom-bucket", project_name="test-project"
        )

        # Assert
        assert isinstance(service, GCPStorageService)
        assert service.bucket_name == "custom-bucket"
        assert service.project_name == "test-project"

    def test_create_storage_service_with_missing_bucket(self):
        """Test that an error is raised when no bucket name is provided."""
        # Act and Assert
        with pytest.raises(
            ValueError, match="Couldn't resolve bucket name for provider `gcp`."
        ):
            StorageFactory.create_storage_service(
                provider="gcp", project_name="test-project"
            )

    def test_create_storage_service_with_missing_project(self):
        """Test that an error is raised when no project name is provided."""
        # Act and Assert
        with pytest.raises(
            ValueError, match="Couldn't resolve project name for provider `gcp`."
        ):
            StorageFactory.create_storage_service(
                provider="gcp", bucket_name="test-bucket"
            )

    def test_create_storage_service_with_invalid_provider(self):
        """Test that an error is raised for an unsupported storage provider."""
        # Act and Assert
        with pytest.raises(ValueError, match="Unsupported storage provider: invalid"):
            StorageFactory.create_storage_service(
                provider="invalid",
                bucket_name="test-bucket",
                project_name="test-project",
            )
