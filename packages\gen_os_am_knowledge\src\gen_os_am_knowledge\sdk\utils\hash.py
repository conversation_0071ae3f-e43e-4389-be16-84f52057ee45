"""Utility functions for hashing file contents using xxhash."""

import xxhash

from gen_os_am_knowledge.sdk.utils.protocols import NamedFileLike


def calculate_hash(file: NamedFileLike) -> int:
    """Calculate the hash of a file content using xxhash.

    Args:
        file (NamedFileLike): A file-like object with a `read` method.

    Returns:
        hash (int): The xxhash64 of the file content.

    """
    signed_correction = 2**64
    boundary = 2**63
    file_hash = xxhash.xxh64(file.read()).intdigest()
    file.seek(0)
    return file_hash if file_hash < boundary else file_hash - signed_correction
