"""Remove Email references.

Revision ID: 0dff7f4fe4f7
Revises: 855e0f41096b
Create Date: 2025-06-04 10:58:08.950169

"""

from collections.abc import Sequence

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "0dff7f4fe4f7"
down_revision: str | None = "855e0f41096b"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Remove email references."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("file", schema=None) as batch_op:
        batch_op.drop_constraint("file_email_id_fkey", type_="foreignkey")
        batch_op.drop_column("email_id")

    op.drop_table("email")

    with op.batch_alter_table("step_run", schema=None) as batch_op:
        batch_op.alter_column("edited", existing_type=sa.BOOLEAN(), nullable=False)

    # ### end Alembic commands ###


def downgrade() -> None:
    """Add email references."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("step_run", schema=None) as batch_op:
        batch_op.alter_column("edited", existing_type=sa.BOOLEAN(), nullable=True)

    with op.batch_alter_table("file", schema=None) as batch_op:
        batch_op.add_column(sa.Column("email_id", sa.UUID(), autoincrement=False, nullable=True))
        batch_op.create_foreign_key("file_email_id_fkey", "email", ["email_id"], ["id"])

    op.create_table(
        "email",
        sa.Column("id", sa.UUID(), autoincrement=False, nullable=False),
        sa.Column("message_id", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("subject", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("body", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("sender", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("received_time", postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
        sa.Column("created_at", postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
        sa.PrimaryKeyConstraint("id", name="email_pkey"),
    )
    # ### end Alembic commands ###
