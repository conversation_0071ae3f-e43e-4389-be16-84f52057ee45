"""Rename input/output to inputs/outputs (plural).

Revision ID: fe9b2089eab1
Revises: 0dff7f4fe4f7
Create Date: 2025-06-05 08:45:03.816711

"""

from collections.abc import Sequence

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "fe9b2089eab1"
down_revision: str | None = "0dff7f4fe4f7"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Rename input/output to inputs/outputs (plural)."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("step_run", schema=None) as batch_op:
        batch_op.add_column(sa.Column("inputs", sa.JSON(), nullable=False))
        batch_op.add_column(sa.Column("outputs", sa.JSON(), nullable=False))
        batch_op.drop_column("output")
        batch_op.drop_column("input")

    # ### end Alembic commands ###


def downgrade() -> None:
    """Rename inputs/outputs to input/output (singular)."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("step_run", schema=None) as batch_op:
        batch_op.add_column(
            sa.Column(
                "input", postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=False
            )
        )
        batch_op.add_column(
            sa.Column(
                "output",
                postgresql.JSON(astext_type=sa.Text()),
                autoincrement=False,
                nullable=False,
            )
        )
        batch_op.drop_column("outputs")
        batch_op.drop_column("inputs")

    # ### end Alembic commands ###
