"""Add message column to Occurrence.

Revision ID: 0bf0185ad232
Revises: 642cb34ed168
Create Date: 2025-06-05 23:32:19.521121

"""

from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "0bf0185ad232"
down_revision: str | None = "642cb34ed168"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Add the message column to the occurrence table."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("occurrence", schema=None) as batch_op:
        batch_op.add_column(sa.Column("message", sa.String(), nullable=True))

    # ### end Alembic commands ###


def downgrade() -> None:
    """Drop the message column from the occurrence table."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("occurrence", schema=None) as batch_op:
        batch_op.drop_column("message")

    # ### end Alembic commands ###
