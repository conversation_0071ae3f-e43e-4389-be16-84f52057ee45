"""Case management client."""

import base64
import json
from datetime import datetime

import httpx
from gen_os_sdk_emulator.agent_manager_client.common.auth_provider import AuthProvider
from gen_os_sdk_emulator.agent_manager_client.common.base_client import BaseClient


class CaseManagementClient(BaseClient):
    """Case management client."""

    def __init__(
        self,
        auth_provider: AuthProvider,
        service_url: str,
    ):
        """Initialize the case management client.

        Args:
            auth_provider: Provider for authentication
            service_url: Base URL of the case management service

        """
        super().__init__(auth_provider)
        self.service_url = service_url
        self._aclient = httpx.AsyncClient()

    async def create_workflow(
        self,
        name: str,
        description: str,
        trace_id: str | None = None,
        app_caller: str | None = None,
    ):
        """Create a workflow."""
        path = "/workflows"

        headers = await self._handle_headers(trace_id=trace_id, app_caller=app_caller)

        result = await self._aclient.post(
            f"{self.service_url}{path}",
            json={"name": name, "description": description},
            headers=headers,
        )
        result.raise_for_status()
        return result.json()

    async def download_file(
        self,
        file_id: str,
        output_path: str,
        trace_id: str | None = None,
        app_caller: str | None = None,
    ) -> tuple[dict, str]:
        """Download a file from the case management service.

        Args:
            file_id: ID of the file to download
            output_path: Path where the file should be saved
            trace_id: Trace ID for request tracking
            app_caller: Name of the calling application

        Returns:
            tuple: File metadata and base64 encoded file content

        """
        path = f"/files/{file_id}"

        headers = await self._handle_headers(trace_id=trace_id, app_caller=app_caller)

        # Get signed URL from Case Management service
        result = await self._aclient.get(
            f"{self.service_url}{path}",
            headers=headers,
        )
        result.raise_for_status()
        file_data = result.json()

        # Download file content from the signed URL
        download_result = await self._aclient.get(file_data["url"])
        download_result.raise_for_status()
        content = download_result.read()

        file_content = base64.b64encode(content).decode("utf-8")

        # Save the file
        with open(output_path, "wb") as f:
            f.write(content)

        return file_data, file_content

    async def create_file(
        self,
        file_path: str,
        file_name: str,
        content_type: str,
        trace_id: str | None = None,
        app_caller: str | None = None,
        email_id: str = None,
    ):
        """Create a file record, get a signed URL, and upload the file content.

        Args:
            file_path: Path to the file to upload
            file_name: Name of the file
            content_type: MIME type of the file
            trace_id: Trace ID for request tracking
            app_caller: Name of the calling application
            email_id: Optional ID of associated email

        Returns:
            Response containing the file ID and storage path

        """
        path = "/files"

        headers = await self._handle_headers(trace_id=trace_id, app_caller=app_caller)

        # Get signed URL from Case Management service
        result = await self._aclient.post(
            f"{self.service_url}{path}",
            json={
                "file_name": file_name,
                "content_type": content_type,
                "email_id": email_id,
            },
            headers=headers,
        )
        result.raise_for_status()
        file_data = result.json()

        # Upload file content to the signed URL
        with open(file_path, "rb") as f:
            upload_headers = await self._handle_headers(
                content_type=content_type,
                with_content_type=True,
                trace_id=trace_id,
                app_caller=app_caller,
            )
            upload_result = await self._aclient.put(
                file_data["upload_url"],
                data=f,
                headers=upload_headers,
            )
            upload_result.raise_for_status()

        return result

    async def start_workflow_run(
        self,
        workflow_id: str,
        input_data: json,
        trace_id: str | None = None,
        app_caller: str | None = None,
    ):
        """Start a new workflow run.

        Args:
            workflow_id: ID of the workflow to run
            input_data: Input data for the workflow
            trace_id: Trace ID for request tracking
            app_caller: Name of the calling application

        Returns:
            Response containing the workflow run details

        """
        path = f"/workflows/{workflow_id}/workflow-runs"

        headers = await self._handle_headers(trace_id=trace_id, app_caller=app_caller)

        result = await self._aclient.post(
            f"{self.service_url}{path}",
            json={"input": input_data, "status": "running"},
            headers=headers,
        )
        result.raise_for_status()
        return result

    async def start_step_run(
        self,
        workflow_id: str,
        workflow_run_id: str,
        step_name: str,
        trace_id: str | None = None,
        app_caller: str | None = None,
    ):
        """Start a new step run within a workflow run.

        Args:
            workflow_id: ID of the workflow
            workflow_run_id: ID of the workflow run
            step_name: Name of the step to run
            trace_id: Trace ID for request tracking
            app_caller: Name of the calling application

        Returns:
            Response containing the step run details

        """
        path = f"/workflows/{workflow_id}/workflow-runs/{workflow_run_id}/step-runs"

        headers = await self._handle_headers(trace_id=trace_id, app_caller=app_caller)

        result = await self._aclient.post(
            f"{self.service_url}{path}",
            json={"step_name": step_name, "status": "running"},
            headers=headers,
        )
        result.raise_for_status()
        return result

    async def end_step_run(
        self,
        workflow_id: str,
        workflow_run_id: str,
        step_run_id: str,
        output: dict,
        trace_id: str | None = None,
        app_caller: str | None = None,
    ):
        """Complete a step run.

        Args:
            workflow_id: ID of the workflow
            workflow_run_id: ID of the workflow run
            step_run_id: ID of the step run
            output: Output data from the step
            trace_id: Trace ID for request tracking
            app_caller: Name of the calling application

        Returns:
            Response containing the updated step run details

        """
        path = (
            f"/workflows/{workflow_id}/workflow-runs/{workflow_run_id}/step-runs"
            f"/{step_run_id}"
        )

        headers = await self._handle_headers(trace_id=trace_id, app_caller=app_caller)

        result = await self._aclient.patch(
            f"{self.service_url}{path}",
            json={
                "status": "completed",
                "output": output,
                "completed_at": datetime.now().isoformat(),
            },
            headers=headers,
        )
        result.raise_for_status()
        return result

    async def end_workflow_run(
        self,
        workflow_id: str,
        workflow_run_id: str,
        trace_id: str | None = None,
        app_caller: str | None = None,
    ):
        """Complete a workflow run.

        Args:
            workflow_id: ID of the workflow
            workflow_run_id: ID of the workflow run
            trace_id: Trace ID for request tracking
            app_caller: Name of the calling application

        Returns:
            Response containing the updated workflow run details

        """
        path = f"/workflows/{workflow_id}/workflow-runs/{workflow_run_id}"

        headers = await self._handle_headers(trace_id=trace_id, app_caller=app_caller)

        result = await self._aclient.patch(
            f"{self.service_url}{path}",
            json={"status": "completed"},
            headers=headers,
        )
        result.raise_for_status()
        return result

    async def create_occurrence(
        self,
        workflow_id: str,
        workflow_run_id: str,
        step_run_id: str,
        reason: str,
        trace_id: str | None = None,
        app_caller: str | None = None,
    ):
        """Create an occurrence record for a step run.

        Args:
            workflow_id: ID of the workflow
            workflow_run_id: ID of the workflow run
            step_run_id: ID of the step run
            reason: Reason for the occurrence
            trace_id: Trace ID for request tracking
            app_caller: Name of the calling application

        Returns:
            Response containing the occurrence details

        """
        path = (
            f"/workflows/{workflow_id}/workflow-runs/{workflow_run_id}/step-runs"
            f"/{step_run_id}/occurrences"
        )

        headers = await self._handle_headers(trace_id=trace_id, app_caller=app_caller)

        result = await self._aclient.post(
            f"{self.service_url}{path}",
            headers=headers,
            json={"reason": reason},
        )
        result.raise_for_status()
        return result

    async def create_email(
        self,
        email_data: dict,
        trace_id: str | None = None,
        app_caller: str | None = None,
    ):
        """Create an email record.

        Args:
            email_data: Email data to store
            trace_id: Trace ID for request tracking
            app_caller: Name of the calling application

        Returns:
            Response containing the email record details

        """
        path = "/emails"

        headers = await self._handle_headers(trace_id=trace_id, app_caller=app_caller)

        result = await self._aclient.post(
            f"{self.service_url}{path}",
            json=email_data,
            headers=headers,
        )
        result.raise_for_status()
        return result

    async def check_email_exists(
        self,
        message_id: str,
        trace_id: str | None = None,
        app_caller: str | None = None,
    ) -> bool:
        """Check if an email with the given message ID exists.

        Args:
            message_id: Message ID to check
            trace_id: Trace ID for request tracking
            app_caller: Name of the calling application

        Returns:
            bool: True if email exists, False otherwise

        """
        path = f"/emails/{message_id}/exists"

        headers = await self._handle_headers(trace_id=trace_id, app_caller=app_caller)

        result = await self._aclient.get(
            f"{self.service_url}{path}",
            headers=headers,
        )
        result.raise_for_status()

        data = result.json()
        return data["exists"]

    async def get_email_message_ids(
        self,
        trace_id: str | None = None,
        app_caller: str | None = None,
        start_date: datetime | None = None,
        end_date: datetime | None = None,
    ) -> list[str]:
        """Get list of email message IDs within a date range.

        Args:
            trace_id: Trace ID for request tracking
            app_caller: Name of the calling application
            start_date: Optional start date filter
            end_date: Optional end date filter

        Returns:
            list[str]: List of message IDs

        """
        path = "/emails/message-ids"

        headers = await self._handle_headers(trace_id=trace_id, app_caller=app_caller)

        params = {}
        if start_date:
            params["start_date"] = start_date.isoformat()
        if end_date:
            params["end_date"] = end_date.isoformat()

        result = await self._aclient.get(
            f"{self.service_url}{path}",
            headers=headers,
            params=params,
        )
        result.raise_for_status()
        data = result.json()
        return data["message_ids"]

    async def update_workflow_run(
        self,
        workflow_id: str,
        workflow_run_id: str,
        workflow_run_data: dict,
        trace_id: str | None = None,
        app_caller: str | None = None,
    ):
        """Update a workflow run.

        Args:
            workflow_id: ID of the workflow
            workflow_run_id: ID of the workflow run
            workflow_run_data: Updated workflow run data
            trace_id: Trace ID for request tracking
            app_caller: Name of the calling application

        Returns:
            Response containing the updated workflow run details

        """
        path = f"/workflows/{workflow_id}/workflow-runs/{workflow_run_id}"

        headers = await self._handle_headers(trace_id=trace_id, app_caller=app_caller)

        result = await self._aclient.patch(
            f"{self.service_url}{path}",
            json=workflow_run_data,
            headers=headers,
        )
        result.raise_for_status()
        return result

    async def get_step_run_by_name(
        self,
        workflow_id: str,
        workflow_run_id: str,
        step_name: str,
        trace_id: str | None = None,
        app_caller: str | None = None,
    ):
        """Get a step run by step name.

        Args:
            workflow_id: ID of the workflow
            workflow_run_id: ID of the workflow run
            step_name: Name of the step
            trace_id: Trace ID for request tracking
            app_caller: Name of the calling application

        Returns:
            dict: Step run details

        """
        path = (
            f"/workflows/{workflow_id}/workflow-runs/{workflow_run_id}/step-runs"
            f"/by-step-name/{step_name}"
        )

        headers = await self._handle_headers(trace_id=trace_id, app_caller=app_caller)

        result = await self._aclient.get(
            f"{self.service_url}{path}",
            headers=headers,
        )
        result.raise_for_status()
        return result.json()

    async def get_workflow_run(
        self,
        workflow_id: str,
        workflow_run_id: str,
        trace_id: str | None = None,
        app_caller: str | None = None,
    ):
        """Get workflow run details.

        Args:
            workflow_id: ID of the workflow
            workflow_run_id: ID of the workflow run
            trace_id: Trace ID for request tracking
            app_caller: Name of the calling application

        Returns:
            dict: Workflow run details

        """
        path = f"/workflows/{workflow_id}/workflow-runs/{workflow_run_id}"

        headers = await self._handle_headers(trace_id=trace_id, app_caller=app_caller)

        result = await self._aclient.get(
            f"{self.service_url}{path}",
            headers=headers,
        )
        result.raise_for_status()
        return result.json()
