"""Google PubSub Broker."""

import json
from typing import Any

from gen_os_sdk_emulator.infrastructure.messaging.base import MessageBroker


class GooglePubSubBroker(MessageBroker):
    """Google PubSub Broker."""

    def __init__(self, project_id: str, topic: str):
        """Initialize the Google PubSub Broker."""
        from google.cloud import pubsub_v1

        self.publisher = pubsub_v1.PublisherClient()
        self.subscriber = pubsub_v1.SubscriberClient()
        self.project_id = project_id
        self.topic = topic

    async def publish(
        self, topic: str | None = None, message: dict[str, Any] = None
    ) -> None:
        """Publish a message to a topic.

        Args:
            topic: The topic to publish to
            message: The message to publish as a dictionary

        """
        from google.api_core.exceptions import GoogleAPIError

        topic = self.topic or topic
        topic_path = self.publisher.topic_path(self.project_id, topic)

        try:
            # Convert message to JSON bytes
            message_bytes = json.dumps(message).encode("utf-8")

            # Publish the message
            future = self.publisher.publish(topic_path, message_bytes)
            future.result()  # Wait for the publish to complete

        except GoogleAPIError as e:
            raise Exception(
                f"Failed to publish message to topic {topic}: {str(e)}"
            ) from e

    async def subscribe(
        self, topic: str | None = None, callback: callable = None
    ) -> None:
        """Subscribe to a topic.

        Args:
            topic: The topic to subscribe to
            callback: The callback function to handle received messages

        """
        from google.api_core.exceptions import GoogleAPIError

        topic = self.topic or topic
        topic_path = self.subscriber.topic_path(self.project_id, topic)
        subscription_path = self.subscriber.subscription_path(
            self.project_id, f"{topic}-subscription"
        )

        try:
            # Create subscription if it doesn't exist
            try:
                self.subscriber.create_subscription(
                    name=subscription_path, topic=topic_path
                )
            except GoogleAPIError:
                # Subscription might already exist, which is fine
                pass

            def message_callback(message):
                try:
                    # Decode and parse the message
                    data = json.loads(message.data.decode("utf-8"))
                    # Call the user's callback
                    callback(data)
                    # Acknowledge the message
                    message.ack()
                except Exception as e:
                    # If processing fails, nack the message
                    message.nack()
                    raise e

            # Start the subscriber
            streaming_pull_future = self.subscriber.subscribe(
                subscription_path, message_callback
            )

            # Keep the subscription running
            streaming_pull_future.result()

        except GoogleAPIError as e:
            raise Exception(f"Failed to subscribe to topic {topic}: {str(e)}") from e
