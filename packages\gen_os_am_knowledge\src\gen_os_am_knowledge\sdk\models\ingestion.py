"""Module containing the Ingestion model for managing document ingestion processes."""

import datetime
import uuid
from typing import Any, ClassVar, Literal

from sqlalchemy.orm import validates
from sqlmodel import JSON, TEXT, Column, DateTime, Field, Relationship, SQLModel


class Ingestion(SQLModel, table=True):
    """The Ingestion Table that holds information about Ingestions.

    These represent the logical processes that update documents according to a Source.

    Args:
        id (uuid.UUID): The Internal Database Id.
        description (str): The Description of this Ingestion.
        status (Literal["Pending","Ongoing","Error","Done"]):
            The Status of this Ingestion.
        source_id (uuid.UUID): The Original Source Of This File.
        folder_id (uuid.UUID | None): The Root Folder For This Ingestion.
        last_run_date (datetime.datetime | None): The date when this Ingestion last ran.
        last_success_date (datetime.datetime | None): The date when this
            Ingestion last ran successfully.
        meta (dict[str, Any] | None): The Json Metadata For This Source.
        error (str | None): The Error Description if the last ran was an error.

        documents (list[Document]):
            The list of Documents that are assigned to this Ingestion.
            A 1 Ingestion to N Documents relationship, meaning a join
            with another table on access.

        source (Source):
            The Source for this Ingestion. A 1 Source to N Ingestion relationship,
            meaning a join with another table on access.
            Use for access or to create a Ingestion with a Source object.

    """

    __tablename__ = "ingestion"  # type: ignore
    __table_args__ = {"extend_existing": True}

    _ING_STATUS_TYPE: ClassVar[str] = Literal[
        "Pending",
        "Ongoing",
        "Done",
        "Error",
    ]

    ING_STATUS_PENDING: ClassVar[_ING_STATUS_TYPE] = "Pending"
    ING_STATUS_ONGOING: ClassVar[_ING_STATUS_TYPE] = "Ongoing"
    ING_STATUS_DONE: ClassVar[_ING_STATUS_TYPE] = "Done"
    ING_STATUS_ERROR: ClassVar[_ING_STATUS_TYPE] = "Error"

    _valid_sync_statuses: ClassVar[set[_ING_STATUS_TYPE]] = {
        ING_STATUS_PENDING,
        ING_STATUS_ONGOING,
        ING_STATUS_DONE,
        ING_STATUS_ERROR,
    }

    id: uuid.UUID = Field(
        default_factory=uuid.uuid4,
        primary_key=True,
        nullable=False,
        description="The Internal Database Id. "
        "Autogenerated if not passed, which is the recommended way",
    )
    """The Internal Database Id.
    Autogenerated if not passed, which is the recommended way"""

    description: str = Field(
        nullable=False, description="The Description of this Ingestion."
    )
    """The Description of this Ingestion."""

    status: _ING_STATUS_TYPE = Field(
        sa_column=Column(TEXT, default=ING_STATUS_PENDING, nullable=False),
        description=f"The Status in {_valid_sync_statuses}",
    )
    f"""The Status in {_valid_sync_statuses}"""

    @validates("status")
    def validate_status(self, _, v):
        """Check status against the valid statuses."""
        if v not in Ingestion._valid_sync_statuses:
            raise ValueError(
                f"Invalid value for field Ingestion.status: {v}. "
                f"Must be one of {Ingestion._valid_sync_statuses}"
            )

    source_id: uuid.UUID = Field(
        foreign_key="source.id",
        nullable=False,
        description="The Original Source Of This File",
    )
    """The Original Source Of This File"""

    last_run_date: datetime.datetime | None = Field(
        default=None,
        sa_column=Column(
            DateTime,
            nullable=True,
        ),
        description="The date when this Ingestion last ran",
    )
    """The date when this Ingestion last ran"""

    last_success_date: datetime.datetime | None = Field(
        default=None,
        sa_column=Column(
            DateTime,
            nullable=True,
        ),
        description="The date when this Ingestion last ran successfully",
    )
    """The date when this Ingestion last ran successfully"""

    folder_id: uuid.UUID | None = Field(
        foreign_key="folder.id",
        nullable=False,
        description="The Root Folder For This Ingestion",
    )
    """The Root Folder For This Ingestion"""

    meta: dict[str, Any] | None = Field(
        sa_column=Column(
            JSON,
            default=None,
            nullable=True,
        ),
        description="Json Metadata For This Source",
    )
    """The Json Metadata For This Source"""

    error: str | None = Field(
        default=None,
        nullable=True,
        description="The Error Description if the last ran was an error",
    )
    """The Error Description if the last ran was an error"""

    documents: list["Document"] = Relationship(back_populates="ingestion")  # type: ignore since pylance doesn't do forward references
    """The list of Documents that are assigned to this Ingestion.
    A 1 Ingestion to N Documents relationship,
    meaning a join with another table on access."""
    source: "Source" = Relationship(back_populates="ingestions")  # type: ignore since pylance doesn't do forward references
    """The Source for this Ingestion. A 1 Source
    to N Ingestion relationship, meaning a join with another table on access.
    Use for access or to create a Ingestion with a Source object."""

    folder: "Folder" = Relationship(back_populates="ingestion")  # type: ignore since pylance doesn't do forward references
    """The Root Folder For This Ingestion"""
