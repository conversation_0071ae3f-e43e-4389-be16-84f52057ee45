"""Base class for all message brokers."""

from abc import ABC, abstractmethod


class MessageBroker(ABC):
    """Base class for all message brokers."""

    @abstractmethod
    async def publish(self, topic: str | None = None, message: dict = None) -> None:
        """Publish a message to a topic."""
        pass

    @abstractmethod
    async def subscribe(
        self, topic: str | None = None, callback: callable = None
    ) -> None:
        """Subscribe to a topic."""
        pass
