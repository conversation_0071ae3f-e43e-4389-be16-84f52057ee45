"""Add step_block_name to StepInput and StepOutput tables.

Revision ID: dbd896cf34f6
Revises: 40fc64fbd4e3
Create Date: 2025-05-29 07:00:46.015975

"""

from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "dbd896cf34f6"
down_revision: str | None = "40fc64fbd4e3"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Add step_block_name to StepInput and StepOutput tables."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("step_input", schema=None) as batch_op:
        batch_op.add_column(sa.Column("step_block_name", sa.String(), nullable=False))

    with op.batch_alter_table("step_output", schema=None) as batch_op:
        batch_op.add_column(sa.Column("step_block_name", sa.String(), nullable=False))

    # ### end Alembic commands ###


def downgrade() -> None:
    """Remove step_block_name from StepInput and StepOutput tables."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("step_output", schema=None) as batch_op:
        batch_op.drop_column("step_block_name")

    with op.batch_alter_table("step_input", schema=None) as batch_op:
        batch_op.drop_column("step_block_name")

    # ### end Alembic commands ###
