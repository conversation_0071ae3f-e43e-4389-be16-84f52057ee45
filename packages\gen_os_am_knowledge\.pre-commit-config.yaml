default_language_version:
  python: python3
default_stages: [pre-commit]

repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: check-builtin-literals
      - id: check-case-conflict
      - id: check-docstring-first
      - id: check-json
      - id: check-toml
      - id: check-xml
      - id: check-yaml
        exclude: (^|/)(Taskfile\.yml|mkdocs\.yml)$
      - id: debug-statements
      - id: detect-private-key
      - id: mixed-line-ending
        args: [--fix=lf]
      - id: trailing-whitespace
      - id: end-of-file-fixer
        exclude_types: ['plain-text', 'tsv', 'csv']
        exclude: postgresql.conf

  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v4.0.0-alpha.8
    hooks:
      - id: prettier
        types_or: [css, javascript, json, yaml]
        args: ['--write', '--tab-width', '2', '--single-quote']

  - repo: https://github.com/asottile/pyupgrade
    rev: v3.19.0
    hooks:
      - id: pyupgrade
        # Update to the Python version you are using
        args: [--py312-plus]

  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.7.3
    hooks:
      - id: ruff
        args: [--fix]
      - id: ruff-format

  - repo: https://github.com/psf/black-pre-commit-mirror
    rev: 24.8.0
    hooks:
      - id: black-jupyter
        language_version: python3.12
        types_or: [jupyter]
        # args: ['--config=./pyproject.toml']
        # Using the config created in CI
        # No explicit config path needed as we create it in the workflow

# sets up .pre-commit-ci.yaml to ensure pre-commit dependencies stay up to date
ci:
  autoupdate_schedule: weekly
  skip: []
  submodules: false
