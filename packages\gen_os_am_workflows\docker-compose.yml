services:
  web-postgres:
    image: postgres:17.5
    environment:
      POSTGRES_DB: ${DATABASE_NAME}
      POSTGRES_USER: ${DATABASE_USER}
      POSTGRES_PASSWORD: ${DATABASE_PASSWORD}
    ports:
      - '5432:5432'
    volumes:
      - postgres_data:/var/lib/postgresql/data

  case_management_backend:
    build:
      context: ./
      dockerfile: Dockerfile
    ports:
      - '8000:8000'
    env_file:
      - .env
    environment:
      - GOOGLE_APPLICATION_CREDENTIALS=/credentials/application_default_credentials.json
    depends_on:
      - web-postgres
    volumes:
      - ./src:/app/src
      - ${GOOGLE_APPLICATION_CREDENTIALS_LOCAL_PATH}:/credentials/application_default_credentials.json:ro

volumes:
  postgres_data:
