"""Remove input column from WorkflowExecution.

Revision ID: 837514154190
Revises: b5a1b37e8cbe
Create Date: 2025-05-30 09:23:27.267675

"""

from collections.abc import Sequence

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "837514154190"
down_revision: str | None = "b5a1b37e8cbe"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Remove input column from WorkflowExecution."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("workflow_execution", schema=None) as batch_op:
        batch_op.drop_column("input")

    # ### end Alembic commands ###


def downgrade() -> None:
    """Add input column to WorkflowExecution."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("workflow_execution", schema=None) as batch_op:
        batch_op.add_column(
            sa.Column(
                "input", postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=False
            )
        )

    # ### end Alembic commands ###
