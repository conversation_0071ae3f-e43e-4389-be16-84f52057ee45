import logging
import traceback
from datetime import datetime, timezone

from gen_os_am_knowledge.utils.ctt.ctt_utils.logging.ctt_log_models import (
    BaseLog,
    ErrorLog,
    EventModel,
    HTTPLog,
    HTTPRequest,
    HTTPResponse,
)
from pydantic_settings import BaseSettings
from starlette.concurrency import iterate_in_threadpool
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request
from starlette.responses import JSONResponse


class APILoggingMiddleware(BaseHTTPMiddleware):
    def __init__(self, app, logger: logging.Logger, settings: BaseSettings):
        super().__init__(app)
        self.logger = logger
        self.settings = settings.get_settings()

    async def dispatch(self, request: Request, call_next):
        error_log = None

        start_dt = datetime.now(timezone.utc)

        try:
            req_body = str(await request.json())
        except Exception:
            req_body = None

        try:
            response = await call_next(request)
            end_dt = datetime.now(timezone.utc)
        except Exception as e:
            end_dt = datetime.now(timezone.utc)
            error_log = ErrorLog(
                code=500,
                message=str(e),
                type=type(e).__name__,
                stack_trace=traceback.format_exc(),
            )
            response = None

        if request.headers.get("content-length") is not None:
            req_size = int(request.headers.get("content-length", 0))
        else:
            try:
                req_size = len(req_body)
            except Exception:
                req_size = None

        if response is not None and response.headers.get("content-length") is not None:
            res_size = int(response.headers.get("content-length", 0))
        else:
            res_size = None

        try:
            res_chunks = [section async for section in response.body_iterator]
            response.body_iterator = iterate_in_threadpool(iter(res_chunks))
            res_body = res_chunks[0].decode() if res_chunks else ""
            res_size = len(res_body)
        except Exception:
            res_body = None

        if response is not None and response.status_code >= 400:
            try:
                error_message = res_body.get(
                    "detail", f"Failed {request.method} call for {request.url}"
                )
            except Exception:
                error_message = f"Failed {request.method} call for {request.url}"
            error_log = ErrorLog(
                code=response.status_code,
                message=error_message,
                type="HTTPException",
                stack_trace=None,
            )

        http_req = HTTPRequest(
            method=request.method,
            bytes=req_size,
            mime_type=request.headers.get("content-type"),
            body_content=req_body,
        )
        http_res = HTTPResponse(
            status_code=response.status_code if response else 500,
            bytes=res_size,
            mime_type=response.headers.get("content-type") if response else None,
            body_content=res_body if response else None,
        )
        http_log = HTTPLog(
            version=str(request.scope.get("http_version")),
            request=http_req,
            response=http_res,
        )

        event = EventModel(
            kind="event",
            category=["api"],
            type=["access"]
            if response is not None and response.status_code < 400
            else ["error"],  # TODO: check if we neeed to change type dynamically
            outcome="success"
            if response is not None and response.status_code < 400
            else "failure",
            start=start_dt,
            end=end_dt,
        )

        base_log = BaseLog(
            environment=self.settings.ENVIRONMENT, event=event, http=http_log
        )

        if response is not None and response.status_code < 400:
            self.logger.info(
                msg=f"Successful {request.method} call for {request.url}",
                extra=base_log.model_dump(mode="json"),
            )
        else:
            base_log.error = error_log
            self.logger.error(
                msg=f"Failed {request.method} call for {request.url}",
                extra=base_log.model_dump(mode="json"),
            )
        if response is None:
            response = JSONResponse(
                content={
                    "detail": error_log.message
                    if error_log
                    else "Internal Server Error"
                },
                status_code=500,
            )

        return response
