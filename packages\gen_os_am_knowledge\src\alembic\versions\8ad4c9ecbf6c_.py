"""Update folder and document tables.

Revision ID: 8ad4c9ecbf6c
Revises: cd13c789811f
Create Date: 2025-04-04 13:11:57.599365

"""

from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "8ad4c9ecbf6c"
down_revision: str | None = "cd13c789811f"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Upgrade the database."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column("document", "hash", existing_type=sa.BIGINT(), nullable=False)
    op.add_column("folder", sa.Column("created_date", sa.DateTime(), nullable=False))
    op.add_column("folder", sa.Column("created_by_id", sa.Uuid(), nullable=False))
    op.add_column("folder", sa.Column("last_modified_by_id", sa.Uuid(), nullable=False))
    op.add_column(
        "folder", sa.Column("modification_date", sa.DateTime(), nullable=False)
    )
    op.add_column("folder", sa.Column("deleted_date", sa.DateTime(), nullable=True))
    op.create_index(
        op.f("ix_folder_deleted_date"), "folder", ["deleted_date"], unique=False
    )
    op.create_foreign_key(None, "folder", "user", ["created_by_id"], ["id"])
    op.create_foreign_key(None, "folder", "user", ["last_modified_by_id"], ["id"])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade the database."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "folder", type_="foreignkey")
    op.drop_constraint(None, "folder", type_="foreignkey")
    op.drop_index(op.f("ix_folder_deleted_date"), table_name="folder")
    op.drop_column("folder", "deleted_date")
    op.drop_column("folder", "modification_date")
    op.drop_column("folder", "last_modified_by_id")
    op.drop_column("folder", "created_by_id")
    op.drop_column("folder", "created_date")
    op.alter_column("document", "hash", existing_type=sa.BIGINT(), nullable=True)
    # ### end Alembic commands ###
