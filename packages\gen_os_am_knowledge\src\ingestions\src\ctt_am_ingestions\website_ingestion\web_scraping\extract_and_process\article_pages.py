"""Functions to extract and process the HTML content from the article pages."""

from bs4 import Tag
from ctt_am_ingestions.website_ingestion.web_scraping.results import Page, Section
from ctt_am_ingestions.website_ingestion.web_scraping.utils import (
    HEADER_PATTERN,
    PREDIFINED_HEADERS,
    add_text,
    extract_list_items,
    extract_table_data,
    get_highest_header,
    get_tabs_dict,
    prepare_text,
    remove_extra_spaces,
)


class HTMLParsingStatus:
    """Track the current state during HTML parsing and content extraction."""

    def __init__(
        self,
        is_tab_active: bool = False,
        is_description_active: bool = False,
        is_first_header: bool = True,
        is_first_description_elem: bool = False,
    ):
        """Initialize the HTML parsing status."""
        self.is_tab_active = is_tab_active
        self.is_description_active = is_description_active
        self.is_first_header = is_first_header
        self.is_first_description_elem = is_first_description_elem


def extract_article_element_data(
    elem: Tag,
    tabs_dict: dict | None,
    highest_header: str | None,
    status: HTMLParsingStatus,
) -> tuple[str | dict | None, HTMLParsingStatus]:
    r"""Extract data from an individual HTML element.

    Elements that need refining...
        ...by duplicated lines:
            - <ul>/<ol> with <ul>/<ol> childs
        ...by lack of new lines(\\n):
            - <div class="bordered-box centered-text">
            - <td>
            - <ul>/<ol>
        ...by other reason:
            - <div class="content" id=r"^tab[1-9][0-9]*$">
                ~ Some tab contents do not have a correspondent tab, which results in
                its text being incorrectly integrated into the previous section
    """
    # Is the first text element inside the description tag?
    if (
        status.is_first_description_elem
        and elem.find_parent("dd")
        and elem.name in [r"^h[1-6]$", "p", "li"]
    ):
        status.is_first_description_elem = False
        return None, status

    # Is a header element that doesn't have a highest header tag as child?
    if HEADER_PATTERN.fullmatch(elem.name) and not elem.find(highest_header):
        # Is the element tag equal to highest header or from the predifined headers,
        # while it doesn't have a description nor a tab tag as parent?
        if (
            (elem.name == highest_header or elem in PREDIFINED_HEADERS)
            and not status.is_description_active
            and not status.is_tab_active
        ):
            return {"headline": elem.get_text(strip=True)}, status
        return prepare_text(elem), status

    # Is a division element with content in class list, while there is a tab dictionary?
    if tabs_dict and elem.name == "div" and "content" in elem.attrs.get("class", []):
        tab_id = elem.attrs.get("id", "")
        # Does the tab ID starts with tab and that tab ID is present in the tab
        # dictionary?
        if isinstance(tab_id, str) and tab_id.startswith("tab") and tab_id in tabs_dict:
            status.is_first_header = False
            return {"headline": tabs_dict[tab_id]}, status

    # Is a paragraph element or a division element with ewa-rteLine in class list?
    if elem.name == "p" or (
        elem.name == "div" and "ewa-rteLine" in elem.attrs.get("class", [])
    ):
        return prepare_text(elem), status

    # Is a list element?
    if elem.name in ["ol", "ul"]:
        elem_classes = set(elem.attrs.get("class", []))
        # Is any of the options on the set below present in the element's class list?
        if (
            len({"tabs", "list-how-it-works", "entry-list"}.intersection(elem_classes))
            == 0
        ):
            return extract_list_items(elem), status

    # Is a table element?
    if elem.name == "table":
        return extract_table_data(elem), status

    # Is a description element?
    if elem.name == "dd":
        status.is_first_description_elem = True
        anchor = elem.find("a")
        description_headline = anchor.get_text(strip=True) if anchor else None
        # Does the element have a definition element as parent
        # with both accordion and accordion-info in class list?
        if elem.find_parent("dl", class_="accordion accordion-info"):
            return description_headline, status
        return {"headline": description_headline}, status

    return None, status


def extract_article_content(content: Tag, summary: str) -> tuple[list[Section], str]:
    """Extract the content of an article page."""
    sections = [Section()]
    tabs_dict = get_tabs_dict(content)
    highest_header = get_highest_header(content)
    status = HTMLParsingStatus()

    for elem in content.descendants:
        text = elem.get_text(strip=True)
        if not isinstance(elem, Tag) or not text or not elem.name:
            continue

        status.is_description_active = bool(elem.find_parent("dd"))
        status.is_tab_active = bool(elem.find_parent("div", class_="tabs-content"))

        data, status = extract_article_element_data(
            elem, tabs_dict, highest_header, status
        )

        if isinstance(data, dict):
            if status.is_first_header:
                status.is_first_header = False
                sections[-1].headline = data["headline"]
            else:
                sections.append(Section(headline=data["headline"]))
        elif data:
            data = remove_extra_spaces(data)
            if status.is_first_header:
                summary = add_text(summary, data)
            else:
                sections[-1].info = add_text(sections[-1].info, data)

    sections = [section for section in sections if len(section.info) > 0]

    return sections, summary


def process_article_page(page: Page, article: Tag) -> Page | None:
    """Process an article page and extract relevant data."""
    header = article.find("header", class_="article-header")
    headline = header.find("h1") if isinstance(header, Tag) else None
    claim = header.find("div", class_="claim") if isinstance(header, Tag) else None
    content = article.find("div", class_="article-content")
    if not isinstance(content, Tag):
        return None

    title = headline.get_text(strip=True) if headline else page.title
    summary = prepare_text(claim) if isinstance(claim, Tag) else ""
    sections, summary = extract_article_content(content, summary)
    if not summary and not sections:
        return None

    page.title, page.summary, page.sections = title, summary, sections
    return page
