"""Simplified Pydantic schemas for two-table agent conversation API."""

import uuid
from datetime import datetime
from typing import Any, Literal

from pydantic import BaseModel, ConfigDict, Field, field_validator

# ==================== Base Pagination and Filters ====================


class PaginationFilters(BaseModel):
    """Base pagination and sorting filters."""

    sort_field: str = "last_message_at"
    sort_order: str = "desc"
    offset: int = Field(0, ge=0)
    limit: int = Field(20, ge=1, le=100)

    @field_validator("sort_order")
    @classmethod
    def validate_sort_order(cls, v: str) -> str:
        """Validate that sort_order is either 'asc' or 'desc'."""
        if v.lower() not in ["asc", "desc"]:
            raise ValueError("Sort order must be 'asc' or 'desc'")
        return v.lower()


class AgentConversationFilters(PaginationFilters):
    """Filters for agent-specific conversation endpoints."""

    search: str = Field("", description="Search across conversation fields")

    search_column: Literal["id", "custom_fields", "agent_id"] | None = Field(
        None, description="Specific column to search in"
    )

    @field_validator("sort_field")
    @classmethod
    def validate_sort_field(cls, v: str) -> str:
        """Validate that sort_field is one of the allowed agent conversation fields."""
        allowed = [
            "id",
            "start_time",
            "last_message_at",
            "message_count",
            "created_at",
            "updated_at",
        ]
        if v not in allowed:
            raise ValueError(f"Invalid sort_field. Allowed: {', '.join(allowed)}")
        return v


class MessageFilters(BaseModel):
    """Filters for message listing endpoints."""

    offset: int = Field(0, ge=0)
    limit: int = Field(20, ge=1, le=100)


# ==================== Conversations Schemas ====================


class ConversationsBase(BaseModel):
    """Base schema for Conversations with common fields.

    Note: last_message_at and message_count are computed fields that are
    calculated dynamically from the MessageTurn table when queried.
    """

    model_config = ConfigDict(
        from_attributes=True,
        populate_by_name=True,
    )

    agent_id: str
    start_time: datetime
    end_time: datetime | None = None
    last_message_at: datetime | None = None
    message_count: int = Field(0, ge=0)
    custom_fields: dict[str, Any] | None = None


class ConversationsSchema(ConversationsBase):
    """Schema for returning conversation data in list responses."""

    id: uuid.UUID
    created_at: datetime
    updated_at: datetime


# ==================== MessageTurn Schemas ====================


class MessageTurnBase(BaseModel):
    """Base schema for MessageTurn with raw JSON message storage."""

    model_config = ConfigDict(
        from_attributes=True,
        populate_by_name=True,
    )

    conversation_id: uuid.UUID
    trace_id: uuid.UUID | None = None
    role: str
    content_text: str | None = None
    raw_message: dict | None = None


class MessageTurnCreate(MessageTurnBase):
    """Schema for creating a new message turn."""

    timestamp: datetime


class MessageTurnSchema(MessageTurnBase):
    """Schema for returning message turn data."""

    id: uuid.UUID
    timestamp: datetime
    created_at: datetime
    updated_at: datetime


# ==================== Response and Pagination Schemas ====================


class PaginationDetails(BaseModel):
    """Pydantic model for pagination metadata."""

    model_config = ConfigDict(
        populate_by_name=True,
    )

    offset: int = Field(ge=0)
    limit: int = Field(ge=1, le=100)
    total_items: int = Field(ge=0)


class PaginatedConversationsResponse(BaseModel):
    """Pydantic model for paginated conversation responses."""

    model_config = ConfigDict(
        populate_by_name=True,
    )

    items: list[ConversationsSchema]
    pagination: PaginationDetails


class PaginatedMessageTurnResponse(BaseModel):
    """Pydantic model for paginated message turn responses."""

    model_config = ConfigDict(
        populate_by_name=True,
    )

    items: list[MessageTurnSchema]
    pagination: PaginationDetails


class ConversationMetadataResponse(BaseModel):
    """Pydantic model for conversation metadata responses.

    Used for single conversation detail endpoints.
    Note: custom_fields is exposed as conversation_context for API consistency.
    """

    model_config = ConfigDict(
        from_attributes=True,
        populate_by_name=True,
    )

    id: uuid.UUID
    agent_id: str
    start_time: datetime
    end_time: datetime | None = None
    message_count: int = Field(ge=0)
    custom_fields: dict[str, Any] | None = Field(None, alias="conversation_context")


# ==================== OpenTelemetry Span Schemas ====================


class SpanData(BaseModel):
    """Pydantic model for processed OpenTelemetry span data.

    This model represents the cleaned and validated span data extracted
    from OpenTelemetry protobuf messages, with proper type validation
    and UUID conversion applied.
    """

    model_config = ConfigDict(
        from_attributes=True,
        populate_by_name=True,
    )

    span_id: uuid.UUID = Field(description="Unique identifier for this span")
    trace_id: uuid.UUID = Field(description="Trace identifier this span belongs to")
    parent_span_id: uuid.UUID | None = Field(
        None, description="Parent span identifier, None for root spans"
    )
    start_time: datetime = Field(description="When the span started")
    end_time: datetime = Field(description="When the span ended")
    attributes: dict[str, Any] = Field(
        default_factory=dict,
        description="Key-value attributes associated with the span",
    )
    events: dict[str, dict[str, Any]] = Field(
        default_factory=dict,
        description="Named events that occurred during the span with their attributes",
    )

    @field_validator("end_time")
    @classmethod
    def validate_end_time_after_start(cls, v: datetime, info) -> datetime:
        """Validate that end_time is after start_time."""
        if "start_time" in info.data and v < info.data["start_time"]:
            raise ValueError("end_time must be after start_time")
        return v
