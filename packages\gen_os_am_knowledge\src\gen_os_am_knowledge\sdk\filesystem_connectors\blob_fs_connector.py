"""Module for the BlobFSConnector."""

import os
import re
import tempfile
from datetime import datetime, timedelta, timezone
from enum import Enum
from pathlib import Path
from typing import override

import fsspec
from azure.storage.blob import BlobSasPermissions, BlobServiceClient, generate_blob_sas

from gen_os_am_knowledge.config.settings import BaseKmSettings
from gen_os_am_knowledge.sdk.filesystem_connectors.base import (
    BaseFileSystemConnector,
    FSEntity,
)


class StorageType(str, Enum):
    """Enum for the storage types."""

    S3 = "s3"
    AZURE = "az"
    GCS = "gcs"


class BlobFSConnector(BaseFileSystemConnector):
    """Connector class for Blob storage filesystems.

    Important conventions:

    - To create folder empty file named .keep is created in that
        folder and all parent folders if they do not exist.
    - Delete/move/copy folder operations are performed over all the
        files including .keep files down the hierarchy.
    - ls() will just ignore .keep files for clear interface.
    - move/copy folder means copying folder itself with all its contents
        into new folder.
    `move_folder(”1/2/3/”, “new/”)` → `ls(”new”)` → `[”new/3/”]`
        and all the contents will be under `3/`

    Args:
        root_bucket : str : The root bucket of the filesystem
        storage_type : StorageType :
            The type of the storage. Can be one of "s3", "az" or "gcs"
        gcs_credentials_path : str : The path to the GCS credentials file.
            Required if storage_type is "gcs"
        azure_account_name : str : The name of the Azure account.
            if storage_type is "az"
        azure_account_key : str : The key of the Azure account. if storage_type is "az"
        azure_account_url : str : The URL of the Azure account.
            if storage_type is "az" - only host part will be used
        azure_connection_string : str : The connection string
            for the Azure account. if storage_type is "az"
        s3_access_key_id : str : The access key ID for the S3 account.
            Required if storage_type is "s3"
        s3_secret_access_key : str : The secret access key for the S3 account.
            Required if storage_type is "s3"
        s3_endpoint_url : str : The endpoint URL for the S3 account.
            Required if storage_type is "s3"

    """

    def __init__(
        self,
        storage_type: StorageType,
        # Non required parameters that can be set up in environment variables
        root_bucket: str | None = None,
        presigned_url_expiry_minutes: int = 10,
        gcs_credentials_path: str = None,
        azure_account_name: str | None = None,
        azure_account_key: str | None = None,
        azure_account_url: str | None = None,
        azure_connection_string: str | None = None,
        s3_access_key_id: str | None = None,
        s3_secret_access_key: str | None = None,
        s3_endpoint_url: str | None = None,
    ):
        """Initialize the BlobFSConnector."""

        class BlobFSConnectorSettings(BaseKmSettings):
            ROOT_BUCKET: str
            PRESIGNED_URL_EXPIRY_MINUTES: int = 10

        self.root_bucket = (
            root_bucket or BlobFSConnectorSettings.get_settings().ROOT_BUCKET
        )
        self.storage_type = storage_type
        self.presigned_url_expiry_minutes = (
            presigned_url_expiry_minutes
            or BlobFSConnectorSettings.get_settings().PRESIGNED_URL_EXPIRY_MINUTES
        )

        if self.storage_type == StorageType.GCS:

            class GCSSettings(BlobFSConnectorSettings):
                GOOGLE_CLOUD_STORAGE_CREDENTIALS_PATH: str

            settings = GCSSettings.get_settings()
            self.gcs_credentials_path = (
                gcs_credentials_path
                if gcs_credentials_path
                else settings.GOOGLE_CLOUD_STORAGE_CREDENTIALS_PATH
            )

        if self.storage_type == StorageType.AZURE:

            class AzureSettings(BlobFSConnectorSettings):
                AZURE_STORAGE_ACCOUNT_NAME: str | None = None
                AZURE_STORAGE_ACCOUNT_KEY: str | None = None
                AZURE_STORAGE_ACCOUNT_URL: str | None = None
                AZURE_STORAGE_ACCOUNT_CONNECTION_STRING: str | None = None

            settings = AzureSettings.get_settings()
            self.azure_account_name = (
                azure_account_name
                if azure_account_name
                else settings.AZURE_STORAGE_ACCOUNT_NAME
            )
            self.azure_account_key = (
                azure_account_key
                if azure_account_key
                else settings.AZURE_STORAGE_ACCOUNT_KEY
            )
            self.azure_connection_string = (
                azure_connection_string
                if azure_connection_string
                else settings.AZURE_STORAGE_ACCOUNT_CONNECTION_STRING
            )
            self.azure_account_url = (
                azure_account_url
                if azure_account_url
                else settings.AZURE_STORAGE_ACCOUNT_URL
            )
        if self.storage_type == StorageType.S3:

            class S3Settings(BlobFSConnectorSettings):
                S3_ACCESS_KEY: str
                S3_SECRET_KEY: str
                S3_ENDPOINT_URL: str

            settings = S3Settings.get_settings()
            self.s3_access_key_id = (
                s3_access_key_id if s3_access_key_id else settings.S3_ACCESS_KEY
            )
            self.s3_secret_access_key = (
                s3_secret_access_key if s3_secret_access_key else settings.S3_SECRET_KEY
            )
            self.s3_endpoint_url = (
                s3_endpoint_url if s3_endpoint_url else settings.S3_ENDPOINT_URL
            )

        self.filesystem = self._setup_fs()
        self.prefix = f"{self.storage_type}://{self.root_bucket}/"

    def _setup_fs(self):
        if self.storage_type == StorageType.S3:
            return fsspec.filesystem(
                "s3",
                key=self.s3_access_key_id,
                secret=self.s3_secret_access_key,
                endpoint_url=self.s3_endpoint_url,
            )
        elif self.storage_type == StorageType.AZURE:
            if self.azure_connection_string:
                self.azure_account_name = re.search(
                    r"AccountName=([^;]+)", self.azure_connection_string
                ).group(1)
                return fsspec.filesystem(
                    "az",
                    account_name=self.azure_account_name,
                    connection_string=self.azure_connection_string,
                )
            if self.azure_account_url:
                return fsspec.filesystem(
                    "az",
                    account_host=self.azure_account_url.removeprefix(
                        "https://"
                    ).removeprefix("http://"),
                    account_name=self.azure_account_name,
                    account_key=self.azure_account_key,
                )
            else:
                return fsspec.filesystem(
                    "az",
                    account_name=self.azure_account_name,
                    account_key=self.azure_account_key,
                )
        elif self.storage_type == StorageType.GCS:
            return fsspec.filesystem("gcs", token=self.gcs_credentials_path)
        else:
            raise Exception(f"Storage type {self.storage_type} not supported")

    @override
    @BaseFileSystemConnector.ensure_folder_path(param_names=["folder_path"])
    def create_folder(
        self, folder_path: str, create_parent_folders_if_not_exist: bool = True
    ) -> FSEntity:
        if create_parent_folders_if_not_exist:
            self.filesystem.touch(f"{self.prefix}{folder_path}.keep")

            # This is a workaround to avoid automatic deletion
            # of the folders down the hierarchy for blob storages
            prev_folder = f"{folder_path}".rsplit("/", 2)[0] + "/"
            while True:
                if self.filesystem.exists(f"{self.prefix}{prev_folder}.keep"):
                    break
                else:
                    self.filesystem.touch(f"{self.prefix}{prev_folder}.keep")
                    prev_folder = prev_folder.rsplit("/", 2)[0] + "/"
        else:
            prev_folder = f"{folder_path}".rsplit("/", 2)[0] + "/"
            if self.filesystem.exists(f"{prev_folder}.keep"):
                self.filesystem.touch(f"{self.prefix}{folder_path}.keep")
            else:
                raise Exception(f"Parent folder {prev_folder} does not exist")

        return FSEntity(path=folder_path, is_folder=True, is_file=False)

    @override
    @BaseFileSystemConnector.ensure_folder_path(param_names=["folder_path"])
    def ls(self, folder_path: str):
        # This is a workaround to list the files without the .keep file
        return [
            f["name"].removeprefix(
                Path(f"{self.root_bucket}/{folder_path}").as_posix() + "/"
            )
            + "/"
            if f["type"] == "directory"
            else f["name"].removeprefix(
                Path(f"{self.root_bucket}/{folder_path}").as_posix() + "/"
            )
            for f in self.filesystem.ls(f"{self.prefix}{folder_path}", detail=True)
            if not f["name"].endswith(".keep")
        ]

    @override
    @BaseFileSystemConnector.ensure_folder_path(param_names=["folder_path"])
    def delete_folder(self, folder_path: str):
        # https://github.com/fsspec/s3fs/issues/931 until resolved aiobotocore stays on version 2.17.0 # noqa: E501
        if self.filesystem.isdir(
            f"{self.prefix}{folder_path}"
        ) and self.filesystem.exists(f"{self.prefix}{folder_path}"):
            self.filesystem.rm(f"{self.prefix}{folder_path}", recursive=True)
            return FSEntity(path=folder_path, is_folder=True, is_file=False)
        else:
            raise Exception(f"Folder {folder_path} does not exist")

    @override
    @BaseFileSystemConnector.ensure_folder_path(param_names=["folder_path"])
    def download_file(self, folder_path: str, filename: str):
        return self.filesystem.open(f"{self.prefix}{folder_path}{filename}", mode="rb")

    @override
    @BaseFileSystemConnector.ensure_folder_path(param_names=["folder_path"])
    def upload_file(
        self,
        folder_path,
        filename,
        file,
        create_parent_folders_if_not_exists: bool = True,
        overwrite: bool = False,
    ):
        temp_file = tempfile.NamedTemporaryFile(delete=False)
        temp_file_path = temp_file.name
        with open(temp_file_path, "wb") as temp_storage:
            temp_storage.write(file.read())

        try:
            if not self.filesystem.exists(f"{self.prefix}{folder_path}"):
                if create_parent_folders_if_not_exists:
                    self.create_folder(folder_path, create_parent_folders_if_not_exists)
                else:
                    raise Exception(f"Folder {folder_path} does not exist")

            if overwrite:
                self.filesystem.put_file(
                    temp_file_path,
                    f"{self.prefix}{folder_path}{filename}",
                    mode="overwrite",
                )
            else:
                if self.filesystem.exists(f"{self.prefix}{folder_path}{filename}"):
                    raise Exception(f"File {filename} already exists")
                self.filesystem.put_file(
                    temp_file_path, f"{self.prefix}{folder_path}{filename}"
                )

            return FSEntity(
                path=f"{folder_path}{filename}", is_folder=False, is_file=True
            )
        finally:
            os.remove(temp_file_path)

    @override
    @BaseFileSystemConnector.ensure_folder_path(param_names=["folder_path"])
    def delete_file(self, folder_path, filename):
        if self.filesystem.isfile(f"{self.prefix}{folder_path}{filename}"):
            self.filesystem.rm(f"{self.prefix}{folder_path}{filename}")
            return FSEntity(
                path=f"{folder_path}{filename}", is_folder=False, is_file=True
            )
        else:
            raise Exception(f"File {folder_path}{filename} does not exist")

    @override
    def copy_file(
        self, original_file_path, destination_file_path, overwrite: bool = False
    ):
        if overwrite:
            self.filesystem.cp(
                f"{self.prefix}{original_file_path}",
                f"{self.prefix}{destination_file_path}",
            )
        else:
            if self.filesystem.exists(f"{self.prefix}{destination_file_path}"):
                raise Exception(f"File {destination_file_path} already exists")
            self.filesystem.cp(
                f"{self.prefix}{original_file_path}",
                f"{self.prefix}{destination_file_path}",
            )

        return FSEntity(path=destination_file_path, is_folder=False, is_file=True)

    @override
    def move_file(
        self, original_file_path, destination_file_path, overwrite: bool = False
    ):
        if overwrite:
            self.filesystem.mv(
                f"{self.prefix}{original_file_path}",
                f"{self.prefix}{destination_file_path}",
            )
        else:
            if self.filesystem.exists(f"{self.prefix}{destination_file_path}"):
                raise Exception(f"File {destination_file_path} already exists")
            self.filesystem.mv(
                f"{self.prefix}{original_file_path}",
                f"{self.prefix}{destination_file_path}",
            )

    @override
    @BaseFileSystemConnector.ensure_folder_path(
        param_names=["original_folder_path", "destination_folder_path"]
    )
    def move_folder(
        self,
        original_folder_path,
        destination_folder_path,
        overwrite: bool = False,
        with_folder: bool = True,
    ):
        if not self.filesystem.exists(f"{self.prefix}{destination_folder_path}"):
            self.create_folder(
                destination_folder_path, create_parent_folders_if_not_exist=True
            )

        if not overwrite and with_folder:
            if self.filesystem.exists(
                f"{self.prefix}{destination_folder_path}{self._separate_last_segment(original_folder_path)[1]}/"
            ):
                raise Exception(
                    f"Folder {destination_folder_path}"
                    f"{self._separate_last_segment(original_folder_path)[1]}"
                    f"/ already exists"
                )

        all_file_paths = self.filesystem.find(f"{self.prefix}{original_folder_path}")
        for file_path in all_file_paths:
            if with_folder:
                cropped_path = file_path.removeprefix(
                    f"{self.root_bucket}/{self._separate_last_segment(original_folder_path)[0]}"
                )
            else:
                cropped_path = file_path.removeprefix(
                    f"{self.root_bucket}/{original_folder_path}"
                )
            self.filesystem.mv(
                file_path, f"{self.prefix}{destination_folder_path}{cropped_path}"
            )

    @override
    @BaseFileSystemConnector.ensure_folder_path(
        param_names=["original_folder_path", "destination_folder_path"]
    )
    def copy_folder(
        self,
        original_folder_path,
        destination_folder_path,
        overwrite: bool = False,
        with_folder: bool = True,
    ):
        if not self.filesystem.exists(f"{self.prefix}{destination_folder_path}"):
            self.create_folder(
                destination_folder_path, create_parent_folders_if_not_exist=True
            )

        if not overwrite and with_folder:
            if self.filesystem.exists(
                f"{self.prefix}{destination_folder_path}{self._separate_last_segment(original_folder_path)[1]}/"
            ):
                raise Exception(
                    f"Folder {destination_folder_path}"
                    f"{self._separate_last_segment(original_folder_path)[1]}"
                    f"/ already exists"
                )

        all_file_paths = self.filesystem.find(f"{self.prefix}{original_folder_path}")

        for file_path in all_file_paths:
            if with_folder:
                cropped_path = file_path.removeprefix(
                    f"{self.root_bucket}/{self._separate_last_segment(original_folder_path)[0]}"
                )
            else:
                cropped_path = file_path.removeprefix(
                    f"{self.root_bucket}/{original_folder_path}"
                )

            self.filesystem.cp(
                file_path, f"{self.prefix}{destination_folder_path}{cropped_path}"
            )

    @override
    @BaseFileSystemConnector.ensure_folder_path(param_names=["folder_path"])
    def generate_presigned_url(self, folder_path: str, filename: str) -> str:
        if self.storage_type != StorageType.AZURE:
            raise Exception(
                "Presigned URL generation is only supported for Azure Blob Storage."
            )
        if self.azure_connection_string:
            blob_service_client = BlobServiceClient.from_connection_string(
                self.azure_connection_string
            )
            self.azure_account_name = blob_service_client.account_name
            self.azure_account_key = blob_service_client.credential.account_key
        else:
            blob_service_client = BlobServiceClient(
                account_url=self.azure_account_url,
                credential=self.azure_account_key,
            )
        if "/" in self.root_bucket:
            container_name, blob_prefix = self.root_bucket.split("/", maxsplit=1)
        else:
            container_name = self.root_bucket
            blob_prefix = ""

        blob_name = Path(blob_prefix, folder_path, filename).as_posix()

        sas_token = generate_blob_sas(
            account_name=self.azure_account_name,
            container_name=container_name,
            blob_name=blob_name,
            account_key=self.azure_account_key,
            permission=BlobSasPermissions(read=True),
            expiry=datetime.now(timezone.utc)
            + timedelta(minutes=self.presigned_url_expiry_minutes),
        )

        presigned_url = (
            f"https://{self.azure_account_name}.blob.core.windows.net/"
            f"{container_name}/{blob_name}?{sas_token}"
        )
        return presigned_url

    @staticmethod
    def _separate_last_segment(path: str) -> str:
        head, sep, tail = path.rstrip("/").rpartition("/")
        return (head + "/" if head else "", tail)
