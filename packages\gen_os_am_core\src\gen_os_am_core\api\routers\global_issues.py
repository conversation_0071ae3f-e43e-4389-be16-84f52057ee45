"""Global Issues Router."""

import logging
import uuid
from typing import Any, get_args

from fastapi import APIRouter, HTTPException, Query, Response
from sqlalchemy import String, asc, desc, func, literal, or_, select, text
from sqlalchemy.ext.asyncio import AsyncSession

from gen_os_am_core.api.schemas import (
    IssueListFilters,
    IssueListItem,
    PaginatedIssuesResponse,
)
from gen_os_am_core.api.utils.issue_utils import (
    get_agent_issues_paginated,
    get_issue_summary,
)
from gen_os_am_core.database.session import SessionManager
from gen_os_am_core.models.enums import IssueCategory, IssueState


class GlobalIssuesRouter:
    """Global Issues Router."""

    def __init__(self, logger: logging.Logger | None = None):
        """Initialize the agent issues router.

        Args:
            logger: Optional logger instance

        """
        self.logger = logger or logging.getLogger(__name__)

        self.router = APIRouter(
            prefix="/issues/core/v1",
            tags=["Issues"],
        )

        self._setup_routes()

    def _setup_routes(self):
        @self.router.get(
            "/issue-state",
            response_model=list[str],
            summary="Get Possible Issue States",
            description="Retrieve a list of all possible states for an issue.",
        )
        async def get_issue_states() -> list[str]:
            """Return all possible states for an issue."""
            return list(get_args(IssueState))

        @self.router.get(
            "/issue-types",
            response_model=list[str],
            summary="Get Possible Issue Types",
            description="Retrieve a list of all possible types for an issue.",
        )
        async def get_issue_types() -> list[str]:
            """Return all possible types for an issue."""
            return list(get_args(IssueCategory))

        @self.router.get(
            "/issues",
            response_model=PaginatedIssuesResponse,
            summary="List Issues (All Agents)",
            description="""Retrieve a paginated list of issues across all
            agents with optional filtering, sorting and full-text search.""",
            response_model_by_alias=False,
        )
        async def list_issues(
            response: Response,
            filters: IssueListFilters = Query(),
        ):
            """List issues across all agents with advanced filtering and pagination."""
            try:
                async with SessionManager.get_session() as db:
                    issue_records, pagination = await get_agent_issues_paginated(
                        db=db,
                        agent_id=None,
                        filters=filters,
                    )

                    issue_items = [IssueListItem(**rec) for rec in issue_records]

                    # Include summary only when showing open issues
                    summary_payload = None
                    if filters.is_open:
                        summary_payload = await get_issue_summary(db, None)

                    response.headers["X-Total-Count"] = str(pagination.total_items)

                    return PaginatedIssuesResponse(
                        items=issue_items,
                        pagination=pagination,
                        summary=summary_payload,
                    )

            except HTTPException:
                raise
            except Exception as e:  # noqa: BLE001
                self.logger.error("Unexpected error in list_issues", exc_info=True)
                raise HTTPException(
                    status_code=500, detail=f"An unexpected error occurred: {str(e)}"
                ) from e

    def get_router(self):
        """Return the router."""
        return self.router
