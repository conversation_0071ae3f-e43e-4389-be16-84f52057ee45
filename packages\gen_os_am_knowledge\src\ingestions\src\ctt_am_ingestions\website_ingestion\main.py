"""Main module for the website ingestion."""

import argparse
import asyncio
from datetime import datetime, timezone

import numpy as np
from aiohttp import ClientSession

from ctt_am_ingestions.website_ingestion.config import (
    WEBSITE_DATA_PATH,
    get_website_ingestion_logger,
)
from ctt_am_ingestions.website_ingestion.web_scraping.read_and_save import read_urls
from ctt_am_ingestions.website_ingestion.web_scraping.results import Results
from ctt_am_ingestions.website_ingestion.web_scraping.scrape import scrape_url
from gen_os_am_knowledge.config.settings import BaseKmSettings
from gen_os_am_knowledge.sdk.filesystem_connectors.base import BaseFileSystemConnector
from gen_os_am_knowledge.sdk.ingestion.ingestion import (
    IngestionResult,
    KnowledgeManagementIngestion,
)
from gen_os_am_knowledge.sdk.models.common import _DEFAULT_UUID
from gen_os_am_knowledge.sdk.operations.folder_ops import FolderService
from gen_os_am_knowledge.sdk.operations.ingestion_ops import (
    IngestionService,
    UpdateResult,
)
from gen_os_am_knowledge.sdk.operations.source_ops import SourceService
from gen_os_am_knowledge.utils.ctt.ctt_utils.logging.ctt_log_models import (
    BaseLog,
    ErrorLog,
    EventModel,
)


class WebsiteIngestion(KnowledgeManagementIngestion):
    """Website ingestion class."""

    async def parse_website(
        self, url_file_path: str = WEBSITE_DATA_PATH, new_only: bool = False
    ) -> list:
        """Parse a list of websites asynchronously and return ingestion documents.

        Args:
            url_file_path (str): Path to the Excel file containing URLs.
            new_only (bool): If True, only scrape new URLs.

        Returns:
            ingestion_documents (list): List of ingestion documents.

        """
        start = datetime.now(timezone.utc)
        self.ingestion = await self._check_ingestion()

        if new_only:
            urls = np.array(self.ingestion.meta["new_urls"])
            # update new urls to be empty
            self.ingestion.meta["urls"] = list(
                set(self.ingestion.meta["urls"] + self.ingestion.meta["new_urls"])
            )
            self.ingestion.meta["new_urls"] = []
            self.ingestion = await IngestionService(
                logger=self.logger
            ).update_ingestion(ingestion_id=self.ingestion.id, meta=self.ingestion.meta)
        elif self.ingestion.meta is not None and "urls" in self.ingestion.meta.keys():
            urls = np.array(self.ingestion.meta["urls"])
        else:
            urls = read_urls(url_file_path)
            self.ingestion.meta = (
                {} if self.ingestion.meta is None else self.ingestion.meta
            )
            self.ingestion.meta["urls"] = urls.tolist()
            self.ingestion = await IngestionService(
                logger=self.logger
            ).update_ingestion(ingestion_id=self.ingestion.id, meta=self.ingestion.meta)
        visited_urls = set()
        scraping_results = Results()

        async with ClientSession() as session:
            tasks = [
                scrape_url(
                    url,
                    visited_urls,
                    scraping_results,
                    session,
                    logger=self.logger.getChild("website_scraping"),
                )
                for url in urls
                if url  # filtering out None or falsy values
            ]
            ingestion_documents = await asyncio.gather(*tasks)
            ingestion_documents = [
                doc for doc in ingestion_documents if doc is not None
            ]

        base_log = BaseLog(
            environment=BaseKmSettings.get_settings().ENVIRONMENT,
            event=EventModel(
                category=["web"],
                type=["access"],
                start=start,
                end=datetime.now(timezone.utc),
            ),
        )
        self.logger.info(
            "Succeful website scraping.\n"
            f"Number of urls: {len(urls)}, "
            f"number of scraped documents: {len(ingestion_documents)}",
            extra=base_log.model_dump(mode="json"),
        )

        return ingestion_documents

    async def update_ingestion_result(
        self, ingestion_result: IngestionResult, ingestion_start: datetime
    ) -> UpdateResult:
        """Update the ingestion result.

        Args:
            ingestion_result: The ingestion result.
            ingestion_start: The ingestion start time.

        Returns:
            update_result: The update result.

        """
        sdk_logger = self.logger.getChild("am_ingestion_sdk")
        if len(ingestion_result.not_ingested_documents) == 0:
            update_result = await IngestionService(logger=sdk_logger).update_ingestion(
                ingestion_id=self.ingestion.id,
                status="Done",
                last_run_date=datetime.now(),
                last_success_date=datetime.now(),
            )
            base_log = BaseLog(
                environment=BaseKmSettings.get_settings().ENVIRONMENT,
                event=EventModel(
                    category=["web", "database"],
                    type=["access"],
                    start=ingestion_start,
                    end=datetime.now(timezone.utc),
                ),
            )
            ingested_documents_len = len(ingestion_result.ingested_documents)
            not_ingested_documents_len = len(ingestion_result.not_ingested_documents)
            self.logger.info(
                "Successful ingestion. "
                f"Number of ingested documents: {ingested_documents_len}, "
                f"number of not ingested documents: {not_ingested_documents_len}",
                extra=base_log.model_dump(mode="json"),
            )
        else:
            update_result = await IngestionService(logger=sdk_logger).update_ingestion(
                ingestion_id=self.ingestion.id,
                status="Error",
                last_run_date=datetime.now(),
                error=ingestion_result.not_ingested_documents[0][1],
            )
            base_log = BaseLog(
                environment=BaseKmSettings.get_settings().ENVIRONMENT,
                event=EventModel(
                    category=["web", "database"],
                    type=["error"],
                    outcome="failure",
                    start=ingestion_start,
                    end=datetime.now(timezone.utc),
                ),
                error=ErrorLog(
                    code=0,
                    message=ingestion_result.not_ingested_documents[0][1],
                    type=type(ingestion_result.not_ingested_documents[0][1]).__name__,
                    stack_trace=None,
                ),
            )
            ingested_documents_len = len(ingestion_result.ingested_documents)
            not_ingested_documents_len = len(ingestion_result.not_ingested_documents)
            self.logger.error(
                "Error in ingestion. "
                f"Number of ingested documents: {ingested_documents_len}, "
                f"number of not ingested documents: {not_ingested_documents_len}",
                extra=base_log.model_dump(mode="json"),
            )
        return update_result


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser()
    parser.add_argument("--new-only", action="store_true", help="Only ingest new URLs")
    return parser.parse_args()


async def main(storage_connector: BaseFileSystemConnector, new_only: bool = False):
    """Main function."""  # noqa: D401
    logger = get_website_ingestion_logger()
    sdk_logger = logger.getChild("am_ingestion_sdk")

    # should be other storage connector in final version
    fs_connector = storage_connector

    # Check if source exists, if not create it
    source_service = SourceService(logger=sdk_logger)
    source = await source_service.search_sources(source_name="ctt_website")
    if source:
        source = source[0]
    else:
        source = await source_service.create_source(
            source_name="ctt_website", source_type="web_scraper"
        )

    # Check folder exists, if not create it
    folder_service = FolderService(logger=sdk_logger, fs_connector=fs_connector)
    folder = await folder_service.search_folders(folder_name="CTT Website")
    if folder:
        folder = folder[0]
    else:
        folder = await folder_service.create_folder(
            folder_name="CTT Website", parent_id=None, user_id=_DEFAULT_UUID
        )

    website_ingestion = WebsiteIngestion(
        ingestion_description="website_ingestion",
        source_name=source.name,
        fs_connector=fs_connector,
        logger=logger,
        folder_id=folder.id,
        ingestion_meta={"base_url": "https://www.ctt.pt", "schedule": "0 1 * * *"},
    )
    await website_ingestion._init_ingestion()

    # Put ingestion in ongoing state for better checking
    await website_ingestion.ingestion_service.update_ingestion(
        ingestion_id=website_ingestion.ingestion.id, status="Ongoing"
    )

    ingestion_documents = await website_ingestion.parse_website(new_only=new_only)

    start = datetime.now(timezone.utc)
    ingestion_result = await website_ingestion.ingest(
        ingestion_documents, overwrite=True
    )
    update_result = await website_ingestion.update_ingestion_result(
        ingestion_result, start
    )

    return ingestion_result, update_result.updated_instance


if __name__ == "__main__":
    args = parse_args()
    asyncio.run(main(new_only=args.new_only, storage_connector=args.storage_connector))
