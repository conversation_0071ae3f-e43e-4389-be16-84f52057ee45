"""General config and settings for Knowledge Management module."""

import ast
from typing import Literal

import dotenv
from pydantic import Field, field_validator, model_validator
from pydantic_settings import BaseSettings, SettingsConfigDict

dot_env = dotenv.find_dotenv(usecwd=True)  # type: ignore

KNOWLEDGE_MANAGEMENT_PREFIX = "AM_KM_"


class BaseKmSettings(BaseSettings):
    """Base settings for the Knowledge Management module."""

    model_config = SettingsConfigDict(
        case_sensitive=True,  ## don't ignore case in .env/env vars
        env_file=dot_env,  ## search current and parent
        frozen=True,  ## don't allow changes
        extra="allow",  ## allow extra configs
        str_strip_whitespace=True,
        env_prefix=KNOWLEDGE_MANAGEMENT_PREFIX,
    )
    ENVIRONMENT: Literal["DEV", "TST", "UAT", "PRD", "LOCAL", "QUA"] = "DEV"

    @classmethod
    def get_settings(cls):
        """Return the settings read from .env or environment."""
        return cls()


class DocumentServiceSettings(BaseKmSettings):
    """Settings for the Document Service."""

    AGENT_API_URL: str


class KnowledgeManagementIngestionSettings(BaseKmSettings):
    """Settings for the Knowledge Management Ingestion."""

    INGESTION_DRAFT_MODE: bool = False


class ApiSettings(BaseKmSettings):
    """The settings for the Knowledge Management application."""

    API_PORT: int = 8000
    API_HOST: str = "0.0.0.0"
    ALLOWED_ORIGINS: list[str] = ["*"]
    API_WORKERS: int = 1
    API_RELOAD: bool = False

    @field_validator("ALLOWED_ORIGINS", mode="before")
    @classmethod
    def validate_allowed_origins(cls, v: list[str]) -> list[str]:
        """Validate the allowed origins."""
        if isinstance(v, str):
            return ast.literal_eval(v)
        return v


class DatabaseSettings(BaseKmSettings):
    """The settings for the Knowledge Management database."""

    # Database connection settings
    DATABASE_TYPE: Literal["sqlite", "postgresql", "mysql", "sqlserver", "parquet"] = (
        "postgresql"
    )
    DATABASE_NAME: str
    DATABASE_USER: str
    DATABASE_PORT: int
    DATABASE_PASSWORD: str
    DATABASE_HOST: str

    # AsyncSessionManager specific settings
    USE_SQLALCHEMY_POOL: bool = True
    SQLALCHEMY_POOL_SIZE: int = 20


class LogSettings(BaseKmSettings):
    """Settings for the logging."""

    LOG_LEVEL: Literal["CRITICAL", "ERROR", "WARNING", "INFO", "DEBUG", "NOTSET"] = (
        "INFO"
    )
    DEBUG: bool = False
