"""Create first tables for the case management system.

Revision ID: 9e1222b76ef0
Revises:
Create Date: 2025-03-17 23:35:41.605842

This migration creates the initial database schema for the case management system,
including tables for workflows, steps, runs, occurrences, interactions, and files.
It also inserts initial data for the 'oficios' workflow and its steps.
"""

from collections.abc import Sequence
from datetime import datetime
from typing import Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "9e1222b76ef0"
down_revision: str | None = None
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Create all tables and insert initial data."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "file",
        sa.Column("id", sa.Uuid(), nullable=False),
        sa.Column("storage_path", sa.String(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "workflow",
        sa.Column("id", sa.String(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "step",
        sa.Column("id", sa.String(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("step_type", sa.String(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("workflow_id", sa.String(), nullable=False),
        sa.Column("order_number", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(["workflow_id"], ["workflow.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "workflow_run",
        sa.Column("id", sa.Uuid(), nullable=False),
        sa.Column("workflow_id", sa.String(), nullable=False),
        sa.Column("started_at", sa.DateTime(), nullable=False),
        sa.Column("input", sa.JSON(), nullable=True),
        sa.Column("status", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["workflow_id"],
            ["workflow.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "step_run",
        sa.Column("id", sa.Uuid(), nullable=False),
        sa.Column("workflow_run_id", sa.Uuid(), nullable=False),
        sa.Column("step_id", sa.String(), nullable=False),
        sa.Column("started_at", sa.DateTime(), nullable=False),
        sa.Column("completed_at", sa.DateTime(), nullable=True),
        sa.Column("output", sa.JSON(), nullable=True),
        sa.Column("status", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["step_id"],
            ["step.id"],
        ),
        sa.ForeignKeyConstraint(
            ["workflow_run_id"],
            ["workflow_run.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "occurrence",
        sa.Column("id", sa.Uuid(), nullable=False),
        sa.Column("step_run_id", sa.Uuid(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("reason", sa.String(), nullable=False),
        sa.Column("status", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["step_run_id"],
            ["step_run.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "interaction",
        sa.Column("id", sa.Uuid(), nullable=False),
        sa.Column("occurrence_id", sa.Uuid(), nullable=False),
        sa.Column("interacted_at", sa.DateTime(), nullable=False),
        sa.Column("edited_data", sa.JSON(), nullable=True),
        sa.Column("kind", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["occurrence_id"],
            ["occurrence.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.bulk_insert(
        sa.table(
            "workflow",
            sa.Column("id", sa.String()),
            sa.Column("name", sa.String()),
            sa.Column("created_at", sa.DateTime()),
        ),
        [
            {
                "id": "oficios",
                "name": "Workflow that processes oficios",
                "created_at": datetime(2025, 3, 7, 22, 3, 9, 568999),
            }
        ],
    )
    creation_time = datetime(2025, 3, 7, 22, 3, 9, 568999)
    op.bulk_insert(
        sa.table(
            "step",
            sa.Column("id", sa.String()),
            sa.Column("name", sa.String()),
            sa.Column("step_type", sa.String()),
            sa.Column("created_at", sa.DateTime()),
            sa.Column("workflow_id", sa.String()),
            sa.Column("order_number", sa.Integer()),
        ),
        [
            {
                "id": "extract",
                "name": "Leitura do documento e extração de detalhes do pedido",
                "step_type": "email_data_extraction",
                "created_at": creation_time,
                "workflow_id": "oficios",
                "order_number": 1,
            },
            {
                "id": "database_query",
                "name": "Consulta de dados SIAJ",
                "step_type": "database_query",
                "created_at": creation_time,
                "workflow_id": "oficios",
                "order_number": 2,
            },
            {
                "id": "document_composition",
                "name": "Criação de PDF",
                "step_type": "document_composition",
                "created_at": creation_time,
                "workflow_id": "oficios",
                "order_number": 3,
            },
            {
                "id": "send_email",
                "name": "Envio de email de resposta",
                "step_type": "send_email",
                "created_at": creation_time,
                "workflow_id": "oficios",
                "order_number": 4,
            },
        ],
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Drop all tables in reverse order."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("interaction")
    op.drop_table("occurrence")
    op.drop_table("step_run")
    op.drop_table("workflow_run")
    op.drop_table("step")
    op.drop_table("workflow")
    op.drop_table("file")
    # ### end Alembic commands ###
