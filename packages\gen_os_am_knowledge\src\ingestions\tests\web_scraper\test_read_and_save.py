"""Test the read and save function."""

import os

import pytest
from ctt_am_ingestions.website_ingestion.web_scraping.read_and_save import read_urls


def test_read_urls():
    """Test the read URLs function with a sample file."""
    # Call the function to test patching the input file
    path = os.path.join(
        os.path.join(os.path.dirname(__file__), "fixtures"), "urls_sample.xlsx"
    )
    result = read_urls(path)

    # Define the expected output
    base_url = "https://www.ctt.pt/page"
    expected = [f"{base_url}{i}" for i in range(1, 21)]

    # Assert the results
    assert sorted(result) == sorted(expected)
