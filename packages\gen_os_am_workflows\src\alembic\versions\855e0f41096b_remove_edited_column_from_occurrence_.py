"""Remove edited column from Occurrence and add it to StepRun instead.

Revision ID: 855e0f41096b
Revises: e2d6e4952334
Create Date: 2025-06-03 08:08:24.243102

"""

from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "855e0f41096b"
down_revision: str | None = "e2d6e4952334"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Upgrade the database."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("occurrence", schema=None) as batch_op:
        batch_op.drop_column("edited")

    with op.batch_alter_table("step_run", schema=None) as batch_op:
        batch_op.add_column(sa.Column("edited", sa.<PERSON>(), nullable=False))

    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade the database."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("step_run", schema=None) as batch_op:
        batch_op.drop_column("edited")

    with op.batch_alter_table("occurrence", schema=None) as batch_op:
        batch_op.add_column(sa.Column("edited", sa.BOOLEAN(), autoincrement=False, nullable=False))

    # ### end Alembic commands ###
