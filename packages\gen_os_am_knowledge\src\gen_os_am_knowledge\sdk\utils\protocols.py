"""Module containing protocol definitions for file-like objects used in the system."""

from typing import Protocol, runtime_checkable


@runtime_checkable
class NamedFileLike(Protocol):
    """Define a file-like object with a name attribute.

    A protocol for objects that have read and seek methods, plus a name attribute.
    Used to type hint buffer objects that can be used for file operations.
    """

    def read(self) -> bytes:
        """Read and return the entire contents of the file as bytes."""
        ...

    def seek(self, offset: int, whence: int = 0) -> int:
        """Change stream position and return the new absolute position.

        Args:
            offset: Number of bytes to offset from whence.
            whence: Position to offset from. 0=start, 1=current position, 2=end.

        Returns:
            The new absolute position.

        """
        ...

    name: str
