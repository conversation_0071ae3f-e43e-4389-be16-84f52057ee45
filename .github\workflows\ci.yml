name: CI

on:
  pull_request:
    branches: ['master', 'main']
    paths-ignore: ['docs/**']
  push:
    branches: ['master', 'main']
    paths-ignore: ['docs/**']
  workflow_dispatch:

concurrency:
  group: ${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

permissions:
  pull-requests: write
  issues: write
  repository-projects: write
  contents: read

jobs:
  linter:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v4
        with:
          python-version: '3.12'
          cache: pip
      - name: Run pre-commit
        uses: pre-commit/action@v3.0.0
        with:
          extra_args: '--files $(git ls-files)'

  tests:
    runs-on: ubuntu-latest
    env:
      CI: true
      SKIP_LLM_TESTS: true
    defaults:
      run:
        shell: bash

    services:
      postgres:
        image: ankane/pgvector
        ports:
          - 5432:5432
        env:
          POSTGRES_DB: somedb
          POSTGRES_USER: someuser
          POSTGRES_PASSWORD: somepass

    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v4
        with:
          python-version: '3.12'

      - name: Install PSQL client
        run: |
          sudo apt update && sudo apt install -y postgresql-client

      - name: Wait for PostgreSQL to be healthy
        run: |
          for i in {1..10}; do
            PGPASSWORD=somepass psql -U someuser -h localhost -p 5432 somedb -c '\q' && break || sleep 5
          done
          PGPASSWORD=somepass psql -U someuser -h localhost -p 5432 somedb -f packages/gen_os_am_knowledge/database/postgres/create_test_databases.sql

      - name: Setup PDM
        uses: pdm-project/setup-pdm@v4
        with:
          cache: false
          python-version: '3.12'
          allow-python-prereleases: false
          cache-dependency-path: |
            packages/gen_os_am_core/pdm.lock
            packages/gen_os_am_knowledge/pdm.lock
            packages/gen_os_am_workflows/pdm.lock

      - name: Create shared directories
        run: |
          mkdir -p shared/lib
          mkdir -p packages/gen_os_am_core/shared/lib

      - name: Run Tests - Knowledge Management
        run: |
          cd packages/gen_os_am_knowledge
          pdm install
          pdm run pytest --cov=src/gen_os_am_knowledge
          pdm run coverage report --omit="*/tests/*"
          pdm run coverage xml --omit="*/tests/*"

      - name: Run Tests - Workflows (Case Management)
        run: |
          cd packages/gen_os_am_workflows
          pdm install
          pdm run pytest --cov=src/gen_os_am_workflows
          pdm run coverage report --omit="*/tests/*"
          pdm run coverage xml --omit="*/tests/*"

      - name: Run Tests - Conversational
        run: |
          cd packages/gen_os_am_conversational
          pdm install
          pdm run pytest --cov=src/gen_os_am_conversational
          pdm run coverage report --omit="*/tests/*"
          pdm run coverage xml --omit="*/tests/*"

      - name: Run Tests - Agent Manager
        run: |
          cd packages/gen_os_am_core
          pdm install
          pdm run pytest --cov=src/gen_os_am_core
          pdm run coverage report --omit="*/tests/*"
          pdm run coverage xml --omit="*/tests/*"

      - name: Coverage Summary Report
        uses: irongut/CodeCoverageSummary@v1.0.2
        with:
          filename: packages/gen_os_am_core/coverage.xml
          badge: true
          format: 'markdown'
          output: 'both'

      - name: Add Coverage PR Comment
        uses: marocchino/sticky-pull-request-comment@v2
        if: github.event_name == 'pull_request'
        with:
          recreate: true
          path: ./code-coverage-results.md
