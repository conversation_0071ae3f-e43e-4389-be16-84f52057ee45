"""Schemas for workflows."""

import uuid
from typing import Any, Dict

from gen_os_am_workflows.database.models import Interaction<PERSON>ind
from pydantic import BaseModel, ConfigDict


class InputData(BaseModel):
    """Input data for a workflow."""

    __config__ = ConfigDict(extra="allow")

    step_block_name: str
    data: dict[str, Any]


class WorkflowTriggerRequest(BaseModel):
    """Request model for triggering a workflow."""

    input_data: list[InputData]
    test_execution: bool = False


class WorkflowTriggerResponse(BaseModel):
    """Response model for triggering a workflow."""

    execution_id: str


class InteractionNotificationRequest(BaseModel):
    """Request model for interaction notifications from gen_os_am_workflows."""

    execution_id: uuid.UUID
    step_run_id: uuid.UUID
    occurrence_id: uuid.UUID
    interaction_id: uuid.UUID
    interaction_kind: InteractionKind
    interaction_data: dict[str, Any] = {}


class WorkflowDatasetCreate(BaseModel):
    """Request model for creating a workflow dataset."""

    name: str
    description: str | None = None
    inputs: list[dict[str, Any]] = []


class WorkflowDatasetUpdate(BaseModel):
    """Request model for updating a workflow dataset."""

    name: str | None = None
    description: str | None = None
    inputs: list[dict[str, Any]] | None = None
