"""Supporting variables and functions."""

import re
from urllib.parse import unquote, urlparse

from bs4 import NavigableString, Tag

BASE_URL = "https://www.ctt.pt"
HEADER_PATTERN = re.compile(r"^h[1-6]$")
PREDIFINED_HEADERS = [
    r"^<h[1-6]>Perguntas frequentes</h[1-6]>$",
    r"^<h[1-6]>Precisa de mais informações ou ajuda\?</h[1-6]>$",
]


def normalize_url(url: str) -> str:
    """Normalize a URL.

    Normalize a URL by decoding percent-encoded characters and converting it to
    lowercase.

    Args:
        url: The input URL to normalize.

    Returns:
        The normalized URL.

    """
    parsed_url = urlparse(url)
    return unquote(parsed_url.geturl().lower())


def get_scope(url: str) -> str:
    """Create page scope through URL's path segments."""
    parsed_url = urlparse(url)
    path_segments = parsed_url.path.strip("/").split("/")
    if len(path_segments) > 2:
        formatted_path = " / ".join(
            segment.replace("-", " ").title() for segment in path_segments[:-1]
        )
        return formatted_path
    return ""


def get_highest_header(content: Tag) -> str | None:
    """Get the highest header in order to get the sections splitter."""
    highest_header = None
    for i in range(1, 7):
        current_header = content.find(f"h{i}")
        if (
            isinstance(current_header, Tag)
            and len(current_header.text) > 0
            and current_header not in PREDIFINED_HEADERS
            and not current_header.find(HEADER_PATTERN)
        ):
            highest_header = current_header.name
            break
    return highest_header


def get_tabs_dict(content: Tag) -> dict | None:
    """Get the tab dictionary in order to get the tabs names mapping."""
    tabs = content.find("ul", class_="tabs")
    if not isinstance(tabs, Tag):
        return None
    if tabs:
        tabs_dict = {}
        for tab in tabs.select("a", class_="href"):
            href = tab.get("href")
            text = tab.get_text(strip=True)
            tabs_dict[str(href)[1:]] = text
        return tabs_dict
    return None


def get_main_list(elem: Tag) -> Tag | None:
    """Return the main list from the elem grandchildren, if any."""
    for child in elem.children:
        if not isinstance(child, Tag):
            continue
        for grandchild in child.children:
            if isinstance(grandchild, Tag) and grandchild.name in ["ul", "ol"]:
                return grandchild
    return None


def get_headline_from_section_header(elem: Tag) -> str | None:
    """Find section header division element and return its text if found."""
    section_header = elem.find("div", class_="section-header")
    if section_header and isinstance(section_header, Tag):
        return prepare_text(section_header)
    return None


def remove_extra_spaces(text: str) -> str:
    """Remove spaces that are consecutive or before punctuation marks.

    Args:
        text: The input text to process.

    Returns:
        The processed text with normalized spaces.

    """
    text = re.sub(r"\s+", " ", text)
    text = re.sub(r"\s([.,;:!?])", r"\1", text)
    return text.strip()


def prepare_text(elem: Tag) -> str:
    """Prepare the text in order to maintain the reference links."""
    text = ""
    tags_to_ignore = {"table"}
    skip_next = False
    for descendant in elem.descendants:
        descendant_text = descendant.get_text(strip=True)
        if skip_next:
            if isinstance(descendant, Tag) and descendant.name == "span":
                continue
            end_in_punctuation = re.search(r"[.,;:!?]$", descendant_text)
            if end_in_punctuation:
                text += end_in_punctuation.group()
            skip_next = False
            continue
        if (
            not descendant_text
            or descendant.find_parent(tags_to_ignore)
            or not (isinstance(descendant, (Tag, NavigableString)))
        ):
            continue
        if not descendant.name and len(descendant_text) > 0:
            text += f" {descendant_text} "
        elif descendant.name == "a" and "href" in descendant.attrs:
            href = descendant["href"]
            href = (
                BASE_URL + href
                if isinstance(href, str) and href.startswith("/")
                else href
            )
            text += f" {descendant_text}[{href}] "
            skip_next = True
    return remove_extra_spaces(text)


def extract_table_data(table: Tag) -> str:
    """Convert an HTML table into a Markdown formatted string."""
    headers = [th.text.strip() for th in table.find_all("th")]
    rows = []
    for tr in table.find_all("tr"):
        if not isinstance(tr, Tag):
            continue
        cells = tr.find_all("td")
        row = [cell.text.strip() for cell in cells]
        if row:
            rows.append(row)
    markdown_lines = []
    if headers:
        markdown_lines.append("| " + " | ".join(headers) + " |")
        markdown_lines.append("|" + "---|" * len(headers))
    for row in rows:
        markdown_lines.append("| " + " | ".join(row) + " |")
    return "\n".join(markdown_lines)


def extract_list_items(elem: Tag) -> str:
    """Convert an HTML list into a string where the items are separated by new lines."""
    return "\n".join(
        f"- {prepare_text(li)}" for li in elem.find_all("li") if isinstance(li, Tag)
    )


def add_text(target: str, text: str) -> str:
    """Add text to target with a newline if target is non-empty."""
    return f"{target}\n{text}" if target else text
