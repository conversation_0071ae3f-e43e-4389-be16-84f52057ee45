"""Results data models definition using Pydantic."""

from pydantic import BaseModel


class Section(BaseModel):
    """Represents a section with headline and info."""

    headline: str | None = None
    info: str = ""


class Page(BaseModel):
    """Represents a URL with a title, scope, and content."""

    url: str
    title: str
    scope: str | None
    summary: str = ""
    sections: list[Section] = []


class ScrapedResults(BaseModel):
    """Stores scraped pages."""

    pages: list[Page] = []

    def model_dump(self, *args, **kwargs):
        """Return a dictionary representation of the model.

        Includes a count of the URLs in addition to the standard fields.
        """
        result = super().model_dump(*args, **kwargs)
        result["count"] = len(self.pages)
        return result


class UnscrapedResults(BaseModel):
    """Stores skipped or not found URLs."""

    urls: list[str] = []

    def model_dump(self, *args, **kwargs):
        """Return a dictionary representation of the model.

        Includes a count of the URLs in addition to the standard fields.
        """
        result = super().model_dump(*args, **kwargs)
        result["count"] = len(self.urls)
        return result


class Results(BaseModel):
    """Encapsulates results logic for scraping operations."""

    scraped: ScrapedResults = ScrapedResults()
    skipped: UnscrapedResults = UnscrapedResults()
    not_found: UnscrapedResults = UnscrapedResults()

    def model_dump(self, *args, **kwargs):
        """Return a dictionary representation of the Results model.

        Includes custom dumps for its subfields.
        """
        result = super().model_dump(*args, **kwargs)
        result["scraped"] = self.scraped.model_dump(*args, **kwargs)
        result["skipped"] = self.skipped.model_dump(*args, **kwargs)
        result["not_found"] = self.not_found.model_dump(*args, **kwargs)
        return result
