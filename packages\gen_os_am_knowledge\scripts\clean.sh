#!/usr/bin/env bash

read -p "Are you sure? (y/n) " -n 1 -r
echo ""

if [[ $REPLY =~ ^[Yy] ]]; then
    rm -rf ./.venv/ &&\
        rm -rf ./.pdm-build/ &&\
        rm -rf ./gen_os_am_knowledge/.pdm-build/ &&\
        rm -rf .pdm-python &&\
        rm -rf pdm.lock &&\
        rm -rf .ruff_cache &&\
        rm -rf .pytest_cache
    echo "Done! (Run deactivate to leave the virtual environment if you have activated it)"

else
    echo "Cancelled!"
fi
