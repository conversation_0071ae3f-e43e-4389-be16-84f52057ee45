"""Module for tag operations."""

import logging
import uuid
from datetime import datetime, timezone

from sqlmodel import delete, select

from gen_os_am_knowledge.config.database import get_async_session
from gen_os_am_knowledge.config.logger_config import get_sdk_logger
from gen_os_am_knowledge.sdk.models import Tag
from gen_os_am_knowledge.sdk.operations.utils import (
    NOT_PROVIDED,
    DeletionResult,
    UpdateResult,
)
from gen_os_am_knowledge.utils.ctt.ctt_utils.logging.logging_utils import (
    error_event_log,
    log_decorator,
    start_time_var,
)


class TagService:
    """Service class to handle CRUD operations for Tag model."""

    def __init__(self, logger: logging.Logger | None = None):
        """Initialize the TagService with an optional logger."""
        self.logger = logger or get_sdk_logger()

    @log_decorator()
    async def create_tag(self, tag_name: str) -> Tag:
        """Create a new tag.

        Args:
            tag_name: The name of the tag.

        Returns:
            Tag: The created tag instance.

        """
        async for session in get_async_session():
            tag = Tag(tag=tag_name)
            session.add(tag)
            await session.commit()
            await session.refresh(tag)
            return tag

    @log_decorator()
    async def delete_tag(self, tag_id: uuid.UUID) -> DeletionResult:
        """Delete tag based on id and return the result of the operation.

        Args:
            tag_id: The id of the tag to delete.

        Returns:
            DeletionResult: The result of the deletion operation.

        """
        start = start_time_var.get(datetime.now(timezone.utc))
        async for session in get_async_session():
            query = delete(Tag).where(Tag.id == tag_id)
            deleted_tag = (
                await session.execute(query.returning(Tag))
            ).scalar_one_or_none()
            if not deleted_tag:
                error_event_log(
                    msg=f"Tag with id {tag_id} not found.",
                    start=start,
                    error=Exception(f"Tag with id {tag_id} not found."),
                    logger=self.logger,
                )
                return DeletionResult(
                    success=False, error=f"Tag with id {tag_id} not found."
                )
            else:
                deleted_tag = deleted_tag.model_copy()
                await session.commit()
                return DeletionResult(success=True, deleted_instance=deleted_tag)

    @log_decorator(type_="access")
    async def get_tag(self, tag_id: uuid.UUID) -> Tag:
        """Retrieve tag from the database based on tag id.

        Args:
            tag_id: The id of the tag to retrieve.

        Returns:
            Tag: The retrieved tag instance.

        """
        async for session in get_async_session():
            query = select(Tag).where(Tag.id == tag_id)
            return (await session.execute(query)).scalars().first()

    @log_decorator(type_="access")
    async def search_tags(
        self, tag_name: str | None = None, limit: int = 20, offset: int = 0
    ) -> list[Tag]:
        """Retrieve tags from the database.

        Args:
            tag_name: str = None - The tag name to search for.
            limit: int = 20 - The number of tags to retrieve.
            offset: int = 0 - The offset to start retrieving tags from.

        Returns:
            list[Tag]: The list of tags retrieved.

        """
        async for session in get_async_session():
            query = select(Tag)

            if tag_name:
                query = query.where(Tag.tag == tag_name)

            query = query.limit(limit).offset(offset)
            return (await session.execute(query)).scalars().all()

    @log_decorator()
    async def update_tag(
        self, tag_id: uuid.UUID, tag_name: str | type[NOT_PROVIDED] = NOT_PROVIDED
    ) -> UpdateResult:
        """Update tag based on id and return the result of the operation.

        Args:
            tag_id: The id of the tag to update.
            tag_name: The new name of the tag.

        Returns:
            UpdateResult: The result of the update operation.

        """
        start = start_time_var.get(datetime.now(timezone.utc))
        async for session in get_async_session():
            query = select(Tag).where(Tag.id == tag_id)
            tag = (await session.execute(query)).scalar_one_or_none()
            if not tag:
                error_event_log(
                    msg=f"Tag with id {tag_id} not found.",
                    start=start,
                    error=Exception(f"Tag with id {tag_id} not found."),
                    logger=self.logger,
                )
                return UpdateResult(
                    success=False, error=f"Tag with id {tag_id} not found."
                )
            else:
                update_dict = {"tag": tag_name}
                updated_values = {}
                for k, v in update_dict.items():
                    if v is not NOT_PROVIDED:
                        if getattr(tag, k) != v:
                            updated_values[k] = (getattr(tag, k), v)
                            setattr(tag, k, v)
                session.add(tag)
                tag = tag.model_copy()
                await session.commit()
                return UpdateResult(
                    success=True, updated_instance=tag, updated_values=updated_values
                )
