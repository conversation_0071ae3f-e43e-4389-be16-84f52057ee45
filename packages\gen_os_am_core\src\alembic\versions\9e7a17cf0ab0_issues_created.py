"""Issues created.

Revision ID: 9e7a17cf0ab0
Revises: d765fbbc99b0
Create Date: 2025-07-10 16:45:38.012773

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "9e7a17cf0ab0"
down_revision: str | None = "d765fbbc99b0"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Create issue tables."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "issue",
        sa.Column("id", sa.Uuid(), nullable=False),
        sa.Column("description", sa.Text(), nullable=False),
        sa.Column("agent_id", sa.String(), nullable=True),
        sa.Column("issue_type", sa.Text(), nullable=False),
        sa.Column("state", sa.Text(), nullable=False),
        sa.Column("deleted", sa.<PERSON>(), server_default="false", nullable=False),
        sa.Column("close_desc", sa.Text(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "issue_logs",
        sa.Column("id", sa.Uuid(), nullable=False),
        sa.Column("issue_id", sa.Uuid(), nullable=False),
        sa.Column("log_type", sa.Text(), nullable=False),
        sa.Column("metadata", sa.JSON(), nullable=True),
        sa.Column("created_by", sa.String(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(["issue_id"], ["issue.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    with op.batch_alter_table("issue_logs", schema=None) as batch_op:
        batch_op.create_index(
            batch_op.f("ix_issue_logs_issue_id"), ["issue_id"], unique=False
        )

    op.create_table(
        "reported_incidents_conversations",
        sa.Column("id", sa.Uuid(), nullable=False),
        sa.Column("issue_id", sa.Uuid(), nullable=False),
        sa.Column("conversation_id", sa.Text(), nullable=False),
        sa.Column("answer", sa.Text(), nullable=True),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column("severity", sa.Text(), nullable=False),
        sa.Column("created_by", sa.String(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(["issue_id"], ["issue.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    with op.batch_alter_table(
        "reported_incidents_conversations", schema=None
    ) as batch_op:
        batch_op.create_index(
            batch_op.f("ix_reported_incidents_conversations_issue_id"),
            ["issue_id"],
            unique=False,
        )

    op.create_table(
        "reported_incidents_workflows",
        sa.Column("id", sa.Uuid(), nullable=False),
        sa.Column("issue_id", sa.Uuid(), nullable=False),
        sa.Column("workflow_execution_id", sa.Text(), nullable=False),
        sa.Column("step", sa.Text(), nullable=True),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column("severity", sa.Text(), nullable=False),
        sa.Column("created_by", sa.String(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(["issue_id"], ["issue.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    with op.batch_alter_table("reported_incidents_workflows", schema=None) as batch_op:
        batch_op.create_index(
            batch_op.f("ix_reported_incidents_workflows_issue_id"),
            ["issue_id"],
            unique=False,
        )

    # ### end Alembic commands ###


def downgrade() -> None:
    """Drop issue tables."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("reported_incidents_workflows", schema=None) as batch_op:
        batch_op.drop_index(batch_op.f("ix_reported_incidents_workflows_issue_id"))

    op.drop_table("reported_incidents_workflows")
    with op.batch_alter_table(
        "reported_incidents_conversations", schema=None
    ) as batch_op:
        batch_op.drop_index(batch_op.f("ix_reported_incidents_conversations_issue_id"))

    op.drop_table("reported_incidents_conversations")
    with op.batch_alter_table("issue_logs", schema=None) as batch_op:
        batch_op.drop_index(batch_op.f("ix_issue_logs_issue_id"))

    op.drop_table("issue_logs")
    op.drop_table("issue")
    # ### end Alembic commands ###
