"""Test the user API."""

import pytest


def test_user(client):  # Changed fixture from setup_api to client
    """Test the user API."""
    # Removed: app = setup_api
    # Removed: client = TestClient(app)
    user_name = "test"
    response = client.post(
        "/user",
        json={"user_name": user_name, "user_external_id": "test"},
    )

    assert response.status_code == 201
    response_json = response.json()
    assert response_json["name"] == user_name
    user_id = response_json["id"]

    # get
    response = client.get(f"/user/{user_id}")
    assert response.status_code == 200
    response_json = response.json()
    assert response_json["name"] == user_name

    # update
    updated_user_name = "update"

    response = client.patch(
        f"/user/{user_id}",
        json={"user_name": updated_user_name},
    )
    assert response.status_code == 200
    response_json = response.json()
    assert response_json["success"]
    assert response_json["updated_instance"]["name"] == updated_user_name
    assert response_json["error"] is None
    assert response_json["updated_values"] == {
        "name": [user_name, updated_user_name],
    }

    # search
    response = client.get(f"/user?user_name={updated_user_name}&user_external_id=test")

    assert response.status_code == 200
    response_json = response.json()
    assert len(response_json) == 1
    assert response_json[0]["name"] == updated_user_name

    # delete
    response = client.delete(f"/user/{user_id}")
    assert response.status_code == 200
    response_json = response.json()
    assert response_json["name"] == updated_user_name
