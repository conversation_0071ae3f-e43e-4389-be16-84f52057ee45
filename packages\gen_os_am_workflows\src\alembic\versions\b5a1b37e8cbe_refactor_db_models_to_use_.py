"""Refactor db models to use WorkflowExecution instead.

Revision ID: b5a1b37e8cbe
Revises: 911aafbcd3a0
Create Date: 2025-05-29 17:27:44.280127

"""

from collections.abc import Sequence

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "b5a1b37e8cbe"
down_revision: str | None = "911aafbcd3a0"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Upgrade the database models to use WorkflowExecution instead."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "workflow_execution",
        sa.Column("id", sa.Uuid(), nullable=False),
        sa.Column("workflow_id", sa.Uuid(), nullable=False),
        sa.Column("started_at", sa.DateTime(), nullable=False),
        sa.Column("input", sa.JSON(), nullable=False),
        sa.Column("status", sa.String(), nullable=False),
        sa.Column("extra_fields", sa.JSON(), nullable=False),
        sa.ForeignKeyConstraint(
            ["workflow_id"],
            ["workflow.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    with op.batch_alter_table("step_run", schema=None) as batch_op:
        batch_op.add_column(sa.Column("workflow_execution_id", sa.Uuid(), nullable=False))
        batch_op.drop_constraint("step_run_workflow_run_id_fkey", type_="foreignkey")
        batch_op.create_foreign_key(None, "workflow_execution", ["workflow_execution_id"], ["id"])
        batch_op.drop_column("workflow_run_id")
    op.drop_table("workflow_run")

    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade the database models to use WorkflowRun instead."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("step_run", schema=None) as batch_op:
        batch_op.add_column(
            sa.Column("workflow_run_id", sa.UUID(), autoincrement=False, nullable=False)
        )
        batch_op.drop_constraint(None, type_="foreignkey")
        batch_op.create_foreign_key(
            "step_run_workflow_run_id_fkey", "workflow_run", ["workflow_run_id"], ["id"]
        )
        batch_op.drop_column("workflow_execution_id")

    op.create_table(
        "workflow_run",
        sa.Column("id", sa.UUID(), autoincrement=False, nullable=False),
        sa.Column("workflow_id", sa.UUID(), autoincrement=False, nullable=False),
        sa.Column("started_at", postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
        sa.Column(
            "input", postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=False
        ),
        sa.Column("status", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column(
            "extra_fields",
            postgresql.JSON(astext_type=sa.Text()),
            autoincrement=False,
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["workflow_id"], ["workflow.id"], name="workflow_run_workflow_id_fkey"
        ),
        sa.PrimaryKeyConstraint("id", name="workflow_run_pkey"),
    )
    op.drop_table("workflow_execution")
    # ### end Alembic commands ###
