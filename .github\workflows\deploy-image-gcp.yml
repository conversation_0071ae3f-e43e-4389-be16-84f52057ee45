name: Deploy Docker Images to GCP

on:
  push:
    branches:
      - main
    tags:
      - '[0-9]+.[0-9]+.[0-9]+' # matches tags like 1.2.3
      - '[0-9]+.[0-9]+.[0-9]+-[ab][0-9]+' # matches tags like 1.2.3-a0 or 1.2.3-b1

  # this only triggers docker build, but not push - see passed push: ${{ github.event_name == 'push' }}
  pull_request:
    branches:
      - main

jobs:
  agent-manager:
    uses: daredata/gen-os-workflows/.github/workflows/build-push-docker.yml@0.1.0
    with:
      project_name: gen-os-agent-manager
      push: ${{ github.event_name == 'push' }}
      tags: ${{ github.event_name == 'pull_request' && github.base_ref || github.ref_name }}
      context: .
