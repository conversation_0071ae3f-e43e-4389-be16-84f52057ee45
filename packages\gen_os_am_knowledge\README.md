# Knowledge Management

## Steps to run KM API

1. Create and fill `.env` file in the root of the project (DB credentials, API)
2. Make sure you are in `gen_os_am_knowledge` folder
3. `pdm install --with test --with dev` - install all deps
4. `pdm run start-database` - launch Agent Manager DB
5. `pdm run alembic-migrate` -  create tables and default values
6. `pdm run api` - run the API
7. Make sure Agent API is running (to make vector store updates) and Agent URL and Prefix are defined in .env
8. Optional: `pdm run populate-db` - to populate DB with some mock data (only docs metadata with no contents, for FE tests)
9. Optional: `pdm run tests` - to run the available unit tests

To launch service in one command use `pdm run service` (schema migrations and api, DB launch in a separate place)

> Note, if it's the first time you're running `alembic-migrate` and you run into issues, it's probably because you're lacking PostgreSQL development libraries. If that's the case, you can install those by running the following command: `sudo apt-get update && sudo apt-get install -y libpq-dev python3-dev`

