import contextvars
import functools
import logging
import traceback
from datetime import datetime, timezone
from typing import Literal

import requests
from gen_os_am_knowledge.config.settings import BaseKmSettings
from gen_os_am_knowledge.utils.ctt.ctt_utils.logging.ctt_log_models import (
    BaseLog,
    ErrorLog,
    EventModel,
    HTTPLog,
    HTTPRequest,
    HTTPResponse,
)

start_time_var = contextvars.ContextVar("start_time")


# ------------------------ #
#          Events          #
# ------------------------ #
def event_log(
    msg: str,
    start: datetime,
    logger: logging.Logger,
    type_: Literal["access", "change"],
    category: list[Literal["database", "file"]] = ["database"],
):
    log = BaseLog(
        environment=BaseKmSettings.get_settings().ENVIRONMENT,
        event=EventModel(
            category=category, type=[type_], start=start, end=datetime.now(timezone.utc)
        ),
    )
    logger.info(msg, extra=log.model_dump(mode="json"))


def error_event_log(
    msg: str,
    start: datetime,
    error: Exception,
    logger: logging.Logger,
    category: list[Literal["database", "file"]] = ["database"],
):
    log = BaseLog(
        environment=BaseKmSettings.get_settings().ENVIRONMENT,
        event=EventModel(
            category=category,
            type=["error"],
            outcome="failure",
            start=start,
            end=datetime.now(timezone.utc),
        ),
        error=ErrorLog(
            code=0,
            message=str(error),
            type=type(error).__name__,
            stack_trace=traceback.format_exc(limit=-2),
        ),
    )
    logger.error(msg, extra=log.model_dump(mode="json"))


def log_decorator(
    log_success=True,
    log_error=True,
    type_: Literal["access", "change"] = "change",
    category: list[Literal["database", "file"]] = ["database"],
):
    """Decorator to log agent manager methods."""

    def decorator(func):
        @functools.wraps(func)
        async def wrapper(self, *args, **kwargs):
            start = datetime.now(timezone.utc)
            start_time_var.set(start)
            try:
                result = await func(self, *args, **kwargs)
                if log_success:
                    event_log(
                        msg=f"{func.__name__} executed successfully",
                        start=start,
                        logger=self.logger,
                        type_=type_,
                        category=category,
                    )
                return result
            except Exception as e:
                if log_error:
                    error_event_log(
                        msg=f"Error in {func.__name__}",
                        start=start,
                        error=e,
                        logger=self.logger,
                    )
                raise e

        return wrapper

    return decorator


# ------------------------ #
#            API           #
# ------------------------ #
def log_http_request(
    method: str,
    url: str,
    request_body: dict | None = None,
    headers: dict | None = None,
    logger: logging.Logger | None = None,
):
    """Logs external API requests and responses using the APILoggingMiddleware format."""
    if logger is None:
        return

    start_dt = datetime.now(timezone.utc)
    error_log = None
    response_body = None
    request_size = None
    response_size = None

    try:
        req_body_content = str(request_body) if request_body else None
        request_size = len(req_body_content) if req_body_content else None
    except Exception:
        req_body_content = None

    try:
        response = requests.request(method, url, json=request_body, headers=headers)
        response.raise_for_status()
        response_body = response.text
        response_size = len(response.content)
        status_code = response.status_code
        mime_type = response.headers.get("content-type")

    except requests.RequestException as e:
        status_code = getattr(e.response, "status_code", 500)
        mime_type = getattr(e.response, "headers", {}).get("content-type", None)
        error_log = ErrorLog(
            code=status_code,
            message=str(e),
            type=type(e).__name__,
            stack_trace=traceback.format_exc(),
        )

    end_dt = datetime.now(timezone.utc)

    http_req = HTTPRequest(
        method=method,
        bytes=request_size,
        mime_type=headers.get("content-type") if headers else None,
        body_content=req_body_content,
    )
    http_res = HTTPResponse(
        status_code=status_code,
        bytes=response_size,
        mime_type=mime_type,
        body_content=response_body if status_code >= 400 else None,
    )
    http_log = HTTPLog(
        version="HTTP/1.1",
        request=http_req,
        response=http_res,
    )

    event = EventModel(
        kind="event",
        category=["api"],
        type=["access"] if status_code < 400 else ["error"],
        outcome="success" if status_code < 400 else "failure",
        start=start_dt,
        end=end_dt,
    )

    base_log = BaseLog(
        environment=BaseKmSettings.get_settings().ENVIRONMENT,
        event=event,
        http=http_log,
    )

    if status_code < 400:
        logger.info(
            msg=f"Successful {method} call to {url}",
            extra=base_log.model_dump(mode="json"),
        )
    else:
        base_log.error = error_log
        logger.error(
            msg=f"Failed {method} call to {url}",
            extra=base_log.model_dump(mode="json"),
        )

    return response if "response" in locals() else None
