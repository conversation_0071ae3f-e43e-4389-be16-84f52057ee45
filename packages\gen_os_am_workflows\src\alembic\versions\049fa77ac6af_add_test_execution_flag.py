"""Add test_execution flag.

Revision ID: 049fa77ac6af
Revises: 39c852b35b0a
Create Date: 2025-07-02 09:16:28.185594

"""

from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "049fa77ac6af"
down_revision: str | None = "39c852b35b0a"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Add test_execution flag to workflow_execution table."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("workflow_execution", schema=None) as batch_op:
        batch_op.add_column(sa.Column("test_execution", sa.Boolean(), nullable=False))

    # ### end Alembic commands ###


def downgrade() -> None:
    """Remove test_execution flag from workflow_execution table."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("workflow_execution", schema=None) as batch_op:
        batch_op.drop_column("test_execution")

    # ### end Alembic commands ###
