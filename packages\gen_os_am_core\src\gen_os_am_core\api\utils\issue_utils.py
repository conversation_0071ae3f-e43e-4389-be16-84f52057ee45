"""Utility functions for issue-related database operations."""

import logging
import uuid
from datetime import datetime
from typing import Any

from sqlalchemy import String, asc, desc, func, literal, not_, or_, select, text
from sqlalchemy.ext.asyncio import AsyncSession

from gen_os_am_core.api.schemas import (
    IncidentSoftDeleteRequest,
    IssueListFilters,
    IssueUpdateRequest,
    PaginationDetails,
    ReportedIncidentConversationCreate,
    ReportedIncidentConversationUnified,
    ReportedIncidentWorkflowCreate,
    ReportedIncidentWorkflowUnified,
)
from gen_os_am_core.database.models.agent_issue import (
    Issue,
    ReportedIncidentConversation,
    ReportedIncidentWorkflow,
)

logger = logging.getLogger(__name__)


async def get_agent_issues_paginated(
    db: AsyncSession,
    agent_id: str | None,
    filters: IssueListFilters,
) -> tuple[list[dict[str, Any]], PaginationDetails]:
    """Return a list of issues for the given agent using SQL aggregates."""
    # Sub-queries
    conv_cnt_sq = (
        select(
            ReportedIncidentConversation.issue_id.label("issue_id"),
            func.count(ReportedIncidentConversation.id).label("conv_cnt"),
        )
        .group_by(ReportedIncidentConversation.issue_id)
        .subquery()
    )

    wf_cnt_sq = (
        select(
            ReportedIncidentWorkflow.issue_id.label("issue_id"),
            func.count(ReportedIncidentWorkflow.id).label("wf_cnt"),
        )
        .group_by(ReportedIncidentWorkflow.issue_id)
        .subquery()
    )

    conv_severities = select(
        ReportedIncidentConversation.issue_id.label("issue_id"),
        ReportedIncidentConversation.severity.label("severity"),
    )
    wf_severities = select(
        ReportedIncidentWorkflow.issue_id.label("issue_id"),
        ReportedIncidentWorkflow.severity.label("severity"),
    )
    all_severities = conv_severities.union_all(wf_severities).subquery()

    severity_sq = (
        select(
            all_severities.c.issue_id.label("issue_id"),
            func.min(all_severities.c.severity).label("severity"),
        )
        .group_by(all_severities.c.issue_id)
        .subquery()
    )

    # Base statement
    stmt = (
        select(
            Issue.id,
            Issue.description,
            Issue.issue_type,
            Issue.state,
            Issue.created_at,
            Issue.updated_at,
            Issue.agent_id.label("agent_name"),
            severity_sq.c.severity.label("severity"),
            (
                func.coalesce(conv_cnt_sq.c.conv_cnt, 0)
                + func.coalesce(wf_cnt_sq.c.wf_cnt, 0)
            ).label("incidents"),
        )
        .outerjoin(conv_cnt_sq, conv_cnt_sq.c.issue_id == Issue.id)
        .outerjoin(wf_cnt_sq, wf_cnt_sq.c.issue_id == Issue.id)
        .outerjoin(severity_sq, severity_sq.c.issue_id == Issue.id)
    )

    if agent_id is not None:
        stmt = stmt.where(Issue.agent_id == agent_id)

    # Filters
    if filters.is_open:
        stmt = stmt.where(Issue.state == "open")
    else:
        stmt = stmt.where(Issue.state != "open")

    if filters.search:
        search_like = f"%{filters.search}%"
        if filters.search_column:
            target_col = filters.search_column
            if target_col == "id":
                stmt = stmt.where(Issue.id.cast(String).ilike(search_like))
            elif target_col == "description":
                stmt = stmt.where(Issue.description.ilike(search_like))
            elif target_col == "issue_type":
                stmt = stmt.where(Issue.issue_type.ilike(search_like))
        else:
            stmt = stmt.where(
                or_(
                    Issue.id.cast(String).ilike(search_like),
                    Issue.description.ilike(search_like),
                    Issue.issue_type.ilike(search_like),
                )
            )

    # Sorting
    sort_mapping: dict[str, Any] = {
        "created_at": Issue.created_at,
        "updated_at": Issue.updated_at,
        "incidents": func.coalesce(conv_cnt_sq.c.conv_cnt, 0)
        + func.coalesce(wf_cnt_sq.c.wf_cnt, 0),
    }
    sort_expr = sort_mapping.get(filters.sort_field, Issue.created_at)
    order_fn = asc if filters.sort_order == "asc" else desc
    stmt = stmt.order_by(order_fn(sort_expr))

    # Count for pagination
    count_stmt = select(func.count()).select_from(stmt.subquery())

    stmt = stmt.offset(filters.offset).limit(filters.limit)

    records_result = await db.execute(stmt)
    raw_records = records_result.all()
    total_items = (await db.execute(count_stmt)).scalar_one()

    records: list[dict[str, Any]] = [
        {
            "id": row.id,
            "description": row.description,
            "issue_type": row.issue_type,
            "state": row.state,
            "agent_name": row.agent_name,
            "severity": row.severity,
            "incidents": row.incidents,
            "created_at": row.created_at,
            "updated_at": row.updated_at,
        }
        for row in raw_records
    ]

    pagination_details = PaginationDetails(
        offset=filters.offset, limit=filters.limit, total_items=total_items
    )
    return records, pagination_details


async def get_issue_summary(db: AsyncSession, agent_id: str | None) -> dict[str, Any]:
    """Compute summary counts for issues by state and type."""
    if agent_id:
        open_count_stmt = select(func.count()).where(
            Issue.agent_id == agent_id, Issue.state == "open"
        )
        closed_count_stmt = select(func.count()).where(
            Issue.agent_id == agent_id, Issue.state != "open"
        )
        by_type_stmt = select(Issue.issue_type, func.count().label("cnt")).where(
            Issue.agent_id == agent_id
        )
    else:
        open_count_stmt = select(func.count()).where(Issue.state == "open")
        closed_count_stmt = select(func.count()).where(Issue.state != "open")
        by_type_stmt = select(Issue.issue_type, func.count().label("cnt"))

    open_count = (await db.execute(open_count_stmt)).scalar_one()
    closed_count = (await db.execute(closed_count_stmt)).scalar_one()

    by_type_stmt = by_type_stmt.group_by(Issue.issue_type)
    by_type_rows = (await db.execute(by_type_stmt)).all()
    by_type: dict[str, int] = {row.issue_type: row.cnt for row in by_type_rows}

    return {"open": open_count, "closed": closed_count, "by_type": by_type}


async def fetch_issue_record(
    db: AsyncSession, issue_id: uuid.UUID, agent_id: str | None = None
) -> Issue | None:
    """Return the Issue row, optionally checking for agent ownership."""
    stmt = select(Issue).where(Issue.id == issue_id, Issue.deleted.is_(False))
    if agent_id:
        stmt = stmt.where(Issue.agent_id == agent_id)
    return await db.scalar(stmt)


async def fetch_issue_incidents(
    db: AsyncSession,
    issue_id: uuid.UUID,
    incident_type: str = "all",
    incident_sort: str = "desc",
) -> list[ReportedIncidentConversationUnified | ReportedIncidentWorkflowUnified]:
    """Fetch incidents (conversation, workflow, or both) for a given issue."""
    conv_sel = select(
        ReportedIncidentConversation.id.label("id"),
        literal("conversation").label("kind"),
        ReportedIncidentConversation.description,
        ReportedIncidentConversation.severity,
        ReportedIncidentConversation.created_by,
        ReportedIncidentConversation.created_at,
        ReportedIncidentConversation.conversation_id,
        literal(None).label("workflow_execution_id"),
    ).where(
        ReportedIncidentConversation.issue_id == issue_id,
        not_(ReportedIncidentConversation.is_deleted),
    )

    wf_sel = select(
        ReportedIncidentWorkflow.id.label("id"),
        literal("workflow").label("kind"),
        ReportedIncidentWorkflow.description,
        ReportedIncidentWorkflow.severity,
        ReportedIncidentWorkflow.created_by,
        ReportedIncidentWorkflow.created_at,
        literal(None).label("conversation_id"),
        ReportedIncidentWorkflow.workflow_execution_id,
    ).where(
        ReportedIncidentWorkflow.issue_id == issue_id,
        not_(ReportedIncidentWorkflow.is_deleted),
    )

    if incident_type == "conversation":
        incident_stmt = conv_sel
    elif incident_type == "workflow":
        incident_stmt = wf_sel
    else:
        incident_stmt = conv_sel.union_all(wf_sel)

    order_fn = asc if incident_sort == "asc" else desc
    incident_stmt = incident_stmt.order_by(order_fn(text("created_at")))

    rows = (await db.execute(incident_stmt)).all()

    incidents: list[
        ReportedIncidentConversationUnified | ReportedIncidentWorkflowUnified
    ] = []
    for r in rows:
        if r.kind == "conversation":
            incidents.append(
                ReportedIncidentConversationUnified(
                    id=r.id,
                    kind=r.kind,
                    description=r.description,
                    severity=r.severity,
                    created_by=r.created_by,
                    created_at=r.created_at,
                    conversation_id=r.conversation_id,
                )
            )
        else:
            incidents.append(
                ReportedIncidentWorkflowUnified(
                    id=r.id,
                    kind=r.kind,
                    description=r.description,
                    severity=r.severity,
                    created_by=r.created_by,
                    created_at=r.created_at,
                    workflow_execution_id=r.workflow_execution_id,
                )
            )

    return incidents


async def create_issue_with_incidents(
    db: AsyncSession,
    *,
    agent_id: str,
    description: str,
    issue_type: str,
    state: str,
    reported_incidents_conversations: list[ReportedIncidentConversationCreate],
    reported_incidents_workflows: list[ReportedIncidentWorkflowCreate],
    created_by: str | None,
) -> Issue:
    """Create an issue and its related incidents."""
    issue = Issue(
        agent_id=agent_id,
        description=description,
        issue_type=issue_type,
        state=state,
    )

    # Add conversation incidents
    for conv in reported_incidents_conversations:
        incident_conv = ReportedIncidentConversation(
            conversation_id=conv.conversation_id,
            answer=conv.answer,
            description=conv.description,
            severity=conv.severity,
            created_by=created_by,
        )
        issue.reported_incidents_conversations.append(incident_conv)

    # Add workflow incidents
    for wf in reported_incidents_workflows:
        incident_wf = ReportedIncidentWorkflow(
            workflow_execution_id=wf.workflow_execution_id,
            step=wf.step,
            description=wf.description,
            severity=wf.severity,
            created_by=created_by,
        )
        issue.reported_incidents_workflows.append(incident_wf)

    db.add(issue)
    await db.commit()
    await db.refresh(
        issue, ["reported_incidents_conversations", "reported_incidents_workflows"]
    )
    return issue


async def soft_delete_restore_incident(
    db: AsyncSession,
    *,
    agent_id: str,
    incident_id: uuid.UUID,
    payload: IncidentSoftDeleteRequest,
) -> tuple[ReportedIncidentConversation | ReportedIncidentWorkflow, str]:
    """Soft delete (or restore) a conversation/workflow incident.

    It validates that the parent issue belongs to the provided agent.

    Returns:
        A tuple containing the updated incident object and its kind.

    Raises:
        ValueError: if the incident is not found or does not belong to the agent.

    """
    # Try conversation incident first
    incident = await db.get(ReportedIncidentConversation, incident_id)
    kind = "conversation"

    if incident is None:
        # If not found, try workflow incident
        incident = await db.get(ReportedIncidentWorkflow, incident_id)
        kind = "workflow"

    if incident is None:
        raise ValueError("Incident not found")

    # Ensure incident belongs to the correct agent via its issue relationship
    await db.refresh(incident, ["issue"])
    if incident.issue.agent_id != agent_id:
        raise ValueError("Incident not found for this agent")

    # Toggle deletion fields
    incident.is_deleted = payload.is_deleted
    if payload.is_deleted:
        incident.deleted_at = datetime.utcnow()
        incident.deleted_by = payload.deleted_by
    else:
        incident.deleted_at = None
        incident.deleted_by = None

    await db.commit()
    await db.refresh(incident)

    return incident, kind


async def update_issue_fields(
    db: AsyncSession,
    *,
    agent_id: str,
    issue_id: uuid.UUID,
    update_data: IssueUpdateRequest,
) -> Issue:
    """Update an existing issue's fields for a specific agent.

    Raises:
        ValueError: if the issue is not found for the given agent.

    """
    issue = await fetch_issue_record(db, issue_id, agent_id)
    if issue is None:
        raise ValueError(f"Issue with ID {issue_id} not found for agent {agent_id}")

    # Update only the provided fields from the request
    update_fields = update_data.model_dump(exclude_unset=True)
    for field, value in update_fields.items():
        setattr(issue, field, value)

    await db.commit()
    await db.refresh(issue)

    return issue
