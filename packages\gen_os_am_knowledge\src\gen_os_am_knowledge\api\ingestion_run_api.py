"""Module for the Ingestion Run API."""

import asyncio
import contextlib
import importlib
import io
import logging
import threading
from types import ModuleType

from ctt_am_ingestions.utils import list_ingestions
from fastapi import APIRouter
from fastapi.responses import JSONResponse

from gen_os_am_knowledge.config.logger_config import get_api_logger
from gen_os_am_knowledge.sdk.filesystem_connectors.base import BaseFileSystemConnector
from gen_os_am_knowledge.sdk.operations import IngestionService


class IngestionRunAPI:
    """API for running ingestion scripts."""

    def __init__(
        self,
        storage_connector: BaseFileSystemConnector,
        logger: logging.Logger = get_api_logger(),
    ):
        """Initialize the IngestionRunAPI class."""
        self.logger = logger.getChild("ingestion_run")
        self.ingestion_service = IngestionService(logger=self.logger)
        self.storage_connector = storage_connector

    def get_router(self) -> APIRouter:
        """Get the ingestion router.

        Returns:
            APIRouter: Ingestion router.

        """
        ingestion_router = APIRouter()

        @ingestion_router.post("/run_ingestion/{ingestion_folder}")
        async def start_ingestion(
            ingestion_folder: str, new_only: bool = False
        ) -> JSONResponse:
            """Start ingestion process in a separate thread.

            Args:
                ingestion_folder (str): Name of the ingestion folder to run
                new_only (bool): Flag to run ingestion with only new urls

            Returns:
                JSONResponse: Response indicating the ingestion has started

            """
            # Check if folder exists
            valid_ingestions = list_ingestions()
            if ingestion_folder not in valid_ingestions:
                return JSONResponse(
                    content={"error": f"Ingestion folder {ingestion_folder} not found"},
                    status_code=404,
                )
            # Try importing the module to validate it exists
            try:
                ingestion_module = importlib.import_module(
                    f"ctt_am_ingestions.{ingestion_folder}.main"
                )
            except ModuleNotFoundError:
                return JSONResponse(
                    content={"error": f"main.py not found in {ingestion_folder}"},
                    status_code=404,
                )

            # Check if ingestion is already running
            ingestion = await self.ingestion_service.search_ingestions(
                description=ingestion_folder, status="Ongoing"
            )
            if len(ingestion) == 1:
                return JSONResponse(
                    content={
                        "error": f"Ingestion {ingestion_folder} is already running"
                    },
                    status_code=400,
                )

            self.ingestion_thread = threading.Thread(
                target=self.run_ingestion,
                args=(ingestion_folder, ingestion_module, new_only),
                daemon=True,
            )
            self.ingestion_thread.start()
            return JSONResponse(
                content={"message": f"Ingestion {ingestion_folder} started"},
                status_code=200,
            )

        return ingestion_router

    def run_ingestion(
        self,
        ingestion_folder: str,
        ingestion_module: ModuleType,
        new_only: bool = False,
    ) -> None:
        """Dynamically import and run the ingestion module.

        Args:
            ingestion_folder (str): Name of the ingestion package
                (e.g., website_ingestion)
            ingestion_module (ModuleType): Main module from the ingestion folder
            new_only (bool): Flag passed to main()

        """
        try:
            if not hasattr(ingestion_module, "main"):
                self.logger.error(
                    f"'main' function not found in {ingestion_folder}.main"
                )
                return

            main_func = ingestion_module.main
            log_buffer = io.StringIO()

            with contextlib.redirect_stdout(log_buffer):
                if asyncio.iscoroutinefunction(main_func):
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    loop.run_until_complete(
                        main_func(
                            storage_connector=self.storage_connector, new_only=new_only
                        )
                    )
                    loop.close()
                else:
                    main_func(
                        storage_connector=self.storage_connector, new_only=new_only
                    )
            log_output = log_buffer.getvalue()
            if log_output.strip():
                self.logger.info(
                    f"Ingestion output for {ingestion_folder}:\n{log_output}"
                )

        except Exception as e:
            self.logger.error(f"Failed to run ingestion {ingestion_folder}: {e}")
