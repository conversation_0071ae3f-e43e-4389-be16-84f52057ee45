"""Add solve_manually column to Step.

Revision ID: eb90766062b6
Revises: acce138733fe
Create Date: 2025-06-06 10:33:16.751369

"""

from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "eb90766062b6"
down_revision: str | None = "acce138733fe"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Add solve_manually column to step."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("step", schema=None) as batch_op:
        batch_op.add_column(sa.Column("solve_manually", sa.<PERSON>(), nullable=True))

    # ### end Alembic commands ###


def downgrade() -> None:
    """Drop solve_manually column from step."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("step", schema=None) as batch_op:
        batch_op.drop_column("solve_manually")

    # ### end Alembic commands ###
