"""FastAPI implementation for the Case Management backend API.

This module provides the REST API endpoints for managing workflows, steps, runs, occurrences,
and interactions in the case management system.
"""

import logging
import uuid

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from gen_os_am_workflows.api.containers import Container
from gen_os_am_workflows.api.logs import generate_span_id, log
from gen_os_am_workflows.api.router import router
from gen_os_am_workflows.api.webhooks import InteractionNotificationRequest, WebhooksService
from gen_os_am_workflows.settings import Settings


class API:
    """FastAPI wrapper for the Case Management backend API.

    This class provides a factory for creating a FastAPI application with all the necessary
    endpoints and middleware configuration for the case management system.
    """

    def __init__(
        self,
        allow_origins: list[str] | None = None,
        allow_methods: list[str] | None = None,
        allow_headers: list[str] | None = None,
        allow_credentials: bool = True,
        expose_headers: list[str] | None = None,
    ):
        """Initialize the API with CORS configuration.

        Args:
            allow_origins: List of allowed origins for CORS. Defaults to ["*"].
            allow_methods: List of allowed HTTP methods. Defaults to ["*"].
            allow_headers: List of allowed headers. Defaults to ["*"].
            allow_credentials: Whether to allow credentials. Defaults to True.
            expose_headers: List of headers to expose. Defaults to ["*"].

        """
        self.allow_origins = allow_origins or ["*"]
        self.allow_methods = allow_methods or ["*"]
        self.allow_headers = allow_headers or ["*"]
        self.allow_credentials = allow_credentials
        self.expose_headers = expose_headers or ["*"]
        self.settings = Settings.get_settings()

    def create_api(self) -> FastAPI:
        """Create and configure the FastAPI application.

        Returns:
            FastAPI: The configured FastAPI application instance.

        """
        app = FastAPI()
        app.container = Container()

        logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

        @app.middleware("http")
        async def log_requests(request, call_next):
            response = await call_next(request)

            attributes = {
                "service.operation": request.method,
                "service.path": request.url.path,
                "service.endpoint": f"{request.url.scheme}://{request.url.netloc}",
                "status.code": response.status_code,
            }

            # TODO: check if we want to keep this header verification

            if "X-user" in request.headers:
                attributes["user.original"] = request.headers["X-user"]
            if "X-process" in request.headers:
                attributes["app.caller"] = request.headers["X-process"]
            if "X-eTrackingID" in request.headers:
                attributes["eTrackingID"] = request.headers["X-eTrackingID"]

            log(
                request.headers.get("X-eTrackingID", str(uuid.uuid4())),
                generate_span_id(),
                request.url.path,
                f"[{request.method}] Request to {request.url}",
                attributes,
            )

            return response

        app.add_middleware(
            CORSMiddleware,
            allow_origins=self.allow_origins,
            allow_methods=self.allow_methods,
            allow_headers=self.allow_headers,
            allow_credentials=self.allow_credentials,
            expose_headers=self.expose_headers,
        )

        app.include_router(router, prefix="/agents/{agent_id}/wf/v1")

        # TODO(Henrique): This needs to be added differently otherwise it's not visible to the AM.
        # Probably add everything under a main router and then import it on the AM.
        # I believe KM is doing this already.
        @app.webhooks.post(WebhooksService.webhook_suffix)
        async def notify_on_interaction(
            interaction_notification: InteractionNotificationRequest,
        ):
            """Notify the orchestrator that an interaction has been created/updated."""

        return app
