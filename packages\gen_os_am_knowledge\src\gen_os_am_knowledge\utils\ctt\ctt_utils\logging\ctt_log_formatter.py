import socket

import ecs_logging


class CTTFormatter(ecs_logging.StdlibFormatter):
    def format_to_ecs(self, record):
        result = super().format_to_ecs(record)

        if "event" not in result:
            result["event"] = {}
        result["event"]["module"] = self._record_attribute(
            "module"
        )  # TODO: incorrect place to get module name
        if "host" not in result:
            result["host"] = {}
        result["host"]["name"] = socket.getfqdn()
        result["host"]["domain"] = (
            result["host"]["name"].split(".", 1)[1]
            if "." in result["host"]["name"]
            else ""
        )

        del result["log"]["original"]
        del result["log"]["origin"]
        del result["process"]["name"]

        return result
