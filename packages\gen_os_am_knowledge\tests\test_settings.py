"""Test the settings."""

from unittest.mock import MagicMock, patch

from gen_os_am_knowledge.config.settings import (
    ApiSettings,
    DatabaseSettings,
)


@patch("gen_os_am_knowledge.config.settings.ApiSettings.get_settings")
def test_api_settings(mock_get_settings):
    """Test that the settings are loaded correctly."""
    mock_settings = MagicMock()
    mock_settings.ALLOWED_ORIGINS = ["*"]
    mock_get_settings.return_value = mock_settings

    result = ApiSettings.get_settings()
    assert result.ALLOWED_ORIGINS == ["*"]


@patch("gen_os_am_knowledge.config.settings.DatabaseSettings.get_settings")
def test_database_settings(mock_get_settings):
    """Test that the settings are loaded correctly."""
    mock_settings = MagicMock()
    mock_settings.DATABASE_TYPE = "postgresql"
    mock_settings.DATABASE_NAME = "test_db"
    mock_settings.DATABASE_USER = "test_user"
    mock_settings.DATABASE_PASSWORD = "test_password"
    mock_settings.DATABASE_HOST = "localhost"
    mock_settings.DATABASE_PORT = 5432
    mock_get_settings.return_value = mock_settings

    result = DatabaseSettings.get_settings()
    assert result.DATABASE_NAME == "test_db"
    assert result.DATABASE_USER == "test_user"
    assert result.DATABASE_PASSWORD == "test_password"
    assert result.DATABASE_HOST == "localhost"
    assert result.DATABASE_PORT == 5432
