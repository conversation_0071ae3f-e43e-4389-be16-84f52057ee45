"""Functions to parse HTML data from pages."""

from bs4 import BeautifulSoup, Tag

from ctt_am_ingestions.website_ingestion.web_scraping.extract_and_process.article_pages import (  # noqa: E501
    process_article_page,
)
from ctt_am_ingestions.website_ingestion.web_scraping.extract_and_process.mixed_pages import (  # noqa: E501
    process_mixed_page,
)
from ctt_am_ingestions.website_ingestion.web_scraping.extract_and_process.schematic_pages import (  # noqa: E501
    process_schematic_page,
)
from ctt_am_ingestions.website_ingestion.web_scraping.results import Page
from ctt_am_ingestions.website_ingestion.web_scraping.utils import get_scope


def parse_page(url: str, html: str) -> Page | None:
    """Parse the HTML data of a page and determine how to process it."""
    soup = BeautifulSoup(html, "html.parser")
    breadcrumbs = soup.find("ul", class_="breadcrumbs")
    page_content = soup.find("div", id="page-content")
    if not isinstance(page_content, Tag):
        return None

    title = (
        soup.title.string.strip() if soup.title and soup.title.string else "Untitled"
    )
    scope = (
        " / ".join(breadcrumbs.text.split("\n")[1:-1])
        if breadcrumbs
        else get_scope(url)
    )
    page = Page(url=url, title=title, scope=scope)

    banner = soup.find("div", id="featured-banner")
    article = page_content.find("article", class_="article-post")

    if not banner and isinstance(article, Tag):
        return process_article_page(page, article)
    if isinstance(banner, Tag) and isinstance(article, Tag):
        return process_mixed_page(page, banner, article)
    if isinstance(banner, Tag) and isinstance(page_content, Tag) and not article:
        return process_schematic_page(page, banner, page_content)
    return None
