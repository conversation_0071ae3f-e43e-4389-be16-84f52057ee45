"""Factory for creating storage service instances."""

from typing import Literal

from gen_os_sdk_emulator.infrastructure.storage.storage import (
    GCPStorageService,
    StorageInterface,
)


class StorageFactory:
    """Factory for creating storage service instances."""

    @staticmethod
    def create_storage_service(
        provider: Literal["gcp"] | None = None,
        project_name: str | None = None,
        bucket_name: str | None = None,
        service_account_email: str | None = None,
    ) -> StorageInterface:
        """Create a storage service based on the provider.

        Args:
            provider: The storage provider to use (e.g., "gcp", "aws", "azure")
            project_name: The name of the project to use
            bucket_name: The name of the storage bucket to use
            service_account_email: The email of the service account to use
        Returns:
            StorageInterface: An implementation of the storage interface

        Raises:
            ValueError: If the provider is not supported

        """
        if not provider:
            provider = "gcp"

        match provider.lower():
            case "gcp":
                if not bucket_name:
                    raise ValueError(
                        f"Couldn't resolve bucket name for provider `{provider}`."
                    )
                if not project_name:
                    raise ValueError(
                        f"Couldn't resolve project name for provider `{provider}`."
                    )
                return GCPStorageService(
                    bucket_name=bucket_name,
                    project_name=project_name,
                    service_account_email=service_account_email,
                )
            case _:
                raise ValueError(f"Unsupported storage provider: {provider}")
