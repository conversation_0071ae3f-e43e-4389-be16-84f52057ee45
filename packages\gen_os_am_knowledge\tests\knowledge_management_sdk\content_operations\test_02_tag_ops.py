"""Tests for tag operations functionality."""

import uuid

import pytest

from gen_os_am_knowledge.sdk.operations.tag_ops import TagService

pytest_plugins = "pytest_asyncio"


@pytest.mark.asyncio
async def test_create_tag():
    """Test creating, retrieving, updating, and deleting a tag."""
    tag_service = TagService()
    # Test variables
    tag_name = "pytest_tag"

    # Create a tag
    created_tag = await tag_service.create_tag(tag_name=tag_name)
    assert created_tag is not None, "Tag creation failed"
    assert created_tag.tag == tag_name, "Tag name mismatch after creation"

    # Get the tag
    retrieved_tag = await tag_service.get_tag(tag_id=created_tag.id)
    assert retrieved_tag.tag == tag_name, "Retrieved tag mismatch"

    retrieved_tag = await tag_service.search_tags(tag_name=tag_name, limit=1, offset=0)
    assert retrieved_tag == [created_tag]

    # Update the tag
    updated_tag_name = "pytest_tag_updated"
    update_result = await tag_service.update_tag(
        tag_id=created_tag.id, tag_name=updated_tag_name
    )

    assert update_result.success
    assert update_result.updated_instance.tag == updated_tag_name
    assert update_result.error is None
    assert update_result.updated_values == {"tag": (tag_name, updated_tag_name)}

    deletion_result = await tag_service.delete_tag(tag_id=created_tag.id)

    assert deletion_result.success
    assert deletion_result.deleted_instance == update_result.updated_instance
    assert deletion_result.error is None

    tag_uuid = uuid.uuid4()
    deletion_result = await tag_service.delete_tag(tag_id=tag_uuid)

    assert not deletion_result.success
    assert deletion_result.deleted_instance is None
    assert deletion_result.error == f"Tag with id {tag_uuid} not found."
