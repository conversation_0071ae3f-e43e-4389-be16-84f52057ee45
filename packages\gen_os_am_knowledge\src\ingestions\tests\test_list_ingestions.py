"""Test the list_ingestions function."""

from unittest.mock import patch

from ctt_am_ingestions.utils import list_ingestions


def test_list_ingestions(tmp_path):
    """Test the list_ingestions function.

    Args:
        tmp_path: The temporary path to use for the test.

    """
    ing1 = tmp_path / "ingestion_one"
    ing2 = tmp_path / "ingestion_two"
    no_main = tmp_path / "no_main_script"

    ing1.mkdir()
    ing2.mkdir()
    no_main.mkdir()

    (ing1 / "main.py").touch()
    (ing2 / "main.py").touch()

    with patch("importlib.resources.files", return_value=str(tmp_path)):
        result = list_ingestions()
        assert sorted(result) == ["ingestion_one", "ingestion_two"]
