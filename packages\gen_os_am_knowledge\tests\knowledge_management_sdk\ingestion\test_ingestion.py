"""Tests for ingestion functionality."""

import io
import os
import shutil
from copy import deepcopy

import pytest
import pytest_asyncio

from gen_os_am_knowledge.sdk.filesystem_connectors.local_fs_connector import (
    LocalFSConnector,
)
from gen_os_am_knowledge.sdk.ingestion.ingestion import (
    IngestionDocument,
    KnowledgeManagementIngestion,
)
from gen_os_am_knowledge.sdk.models.common import DEFAULT_USER
from gen_os_am_knowledge.sdk.operations import (
    FolderService,
    IngestionService,
    TagService,
)


@pytest.fixture(scope="module")
def create_root_folder():
    """Create root folder for testing."""
    storage_path = "path-to-local-storage"
    root_folder = "root-folder"
    os.makedirs(f"{storage_path}/{root_folder}", exist_ok=True)
    yield root_folder
    shutil.rmtree(storage_path)


@pytest.fixture(scope="module")
def local_fs_connector(create_root_folder):
    """Create local filesystem connector."""
    yield LocalFSConnector(root_folder=create_root_folder)


@pytest.fixture(scope="module")
def create_ingestion_documents():
    """Create ingestion documents for testing."""
    ingestion_documents = []

    for i in range(5):
        text = f"test page {i}"
        file = io.BytesIO(text.encode())
        file.name = f"test_page_{i}.txt"
        doc = IngestionDocument(
            file=file,
            storage_folder_path="",
            original_path=f"local_folder/test_page_{i}.txt",
            meta={"author": "John Doe"},
            tags=["test"],
        )
        ingestion_documents.append(doc)
    yield ingestion_documents


@pytest_asyncio.fixture(scope="module")
async def create_folder(local_fs_connector):
    """Create a folder for testing."""
    folder = await FolderService(fs_connector=local_fs_connector).create_folder(
        folder_name="docs", user_id=DEFAULT_USER.id
    )
    yield folder
    await FolderService(fs_connector=local_fs_connector).delete_folder(
        folder.id, user_id=DEFAULT_USER.id
    )


@pytest_asyncio.fixture(scope="module")
async def create_tags():
    """Create a tag for testing."""
    tag = await TagService().create_tag(tag_name="test")
    yield tag
    await TagService().delete_tag(tag.id)


@pytest.mark.asyncio
async def test_ingestion(
    local_fs_connector, create_ingestion_documents, create_folder, create_tags
):
    """Test the ingestion process with various scenarios."""
    am_ingestion = KnowledgeManagementIngestion(
        ingestion_description="test ingestion",
        source_name="default",
        fs_connector=local_fs_connector,
        folder_id=create_folder.id,
    )

    ingestion_documents = deepcopy(create_ingestion_documents[:-2])

    result_create = deepcopy(
        await am_ingestion.ingest(
            ingestion_documents,
        )
    )

    assert len(result_create.ingested_documents) == 3
    assert len(result_create.not_ingested_documents) == 0
    for document in result_create.ingested_documents:
        assert document.database_id is not None
        assert document.state == "created"
        assert document.hash is not None
        assert document.file is not None
        # check if cursor is reset
        assert len(document.file.read()) == 11
        document.file.seek(0)

    ingestion = await IngestionService().search_ingestions(description="test ingestion")
    assert len(ingestion) == 1
    assert ingestion[0].status == "Ongoing"

    # unchanged ingestion
    ingestion_documents = deepcopy(create_ingestion_documents[:-2])
    result = await am_ingestion.ingest(ingestion_documents, overwrite=False)

    assert len(result.ingested_documents) == 0
    assert len(result.not_ingested_documents) == 3
    for document, error in result.not_ingested_documents:
        assert document.database_id is not None
        assert document.state == "unchanged"
        assert document.hash is not None
        assert error == "Document with the same hash already exists in the folder"
        assert document.file is not None
        # check if cursor is reset
        assert len(document.file.read()) == 11
        document.file.seek(0)

    # update ingestion with disallowed overwrite
    ingestion_documents = deepcopy(create_ingestion_documents[:-2])
    ingestion_documents[0].file = io.BytesIO(b"new content")
    ingestion_documents[0].file.name = "test_page_0.txt"

    result = await am_ingestion.ingest([ingestion_documents[0]], overwrite=False)

    for document, error in result.not_ingested_documents:
        assert document.database_id is not None
        assert document.state == "unchanged"
        assert document.hash is not None
        assert error == "Document with the same name already exists in the folder"
        assert document.file is not None
        # check if cursor is reset
        assert len(document.file.read()) == 11
        document.file.seek(0)

    # update ingestion
    ingestion_documents = deepcopy(create_ingestion_documents[:-2])
    ingestion_documents[0].file = io.BytesIO(b"new content")
    ingestion_documents[0].file.name = "test_page_0.txt"
    result = await am_ingestion.ingest(ingestion_documents, overwrite=True)

    assert len(result.ingested_documents) == 3
    assert len(result.not_ingested_documents) == 0
    for num, document in enumerate(result.ingested_documents):
        if num == 0:
            assert document.database_id is not None
            assert document.state == "updated"
            assert document.hash is not None
            assert document.updated_values is not None
            assert document.file is not None
            # check if cursor is reset
            assert len(document.file.read()) == 11
            document.file.seek(0)
        else:
            assert document.database_id is not None
            assert document.state == "unchanged"
            assert document.hash is not None
            assert document.file is not None
            # check if cursor is reset
            assert len(document.file.read()) == 11
            document.file.seek(0)

    # add ingestion
    ingestion_documents = deepcopy(create_ingestion_documents[:-1])
    ingestion_documents[0].file = io.BytesIO(b"new content")
    ingestion_documents[0].file.name = "test_page_0.txt"
    result = await am_ingestion.ingest(ingestion_documents, overwrite=True)

    for num, document in enumerate(result.ingested_documents):
        if num == 3:
            assert document.database_id is not None
            assert document.state == "created"
            assert document.hash is not None
            assert document.file is not None
            # check if cursor is reset
            assert len(document.file.read()) == 11
            document.file.seek(0)
        else:
            assert document.database_id is not None
            assert document.state == "unchanged"
            assert document.hash is not None
            assert document.updated_values is None
            assert document.file is not None
            # check if cursor is reset
            assert len(document.file.read()) == 11
            document.file.seek(0)

    # draft mode ingestion
    ingestion_documents = deepcopy(create_ingestion_documents)
    ingestion_documents[0].file = io.BytesIO(b"new content")
    ingestion_documents[0].file.name = "test_page_0.txt"

    am_ingestion = KnowledgeManagementIngestion(
        ingestion_description="test ingestion",
        source_name="default",
        fs_connector=local_fs_connector,
        draft_mode=True,
        folder_id=create_folder.id,
    )
    result = await am_ingestion.ingest(ingestion_documents, overwrite=True)

    for num, document in enumerate(result.ingested_documents):
        if num == 4:
            assert document.database_id is not None
            assert document.state == "created"
            assert document.hash is not None
            assert document.file is not None
            assert document.draft is True
            # check if cursor is reset
            assert len(document.file.read()) == 11
            document.file.seek(0)
        else:
            assert document.database_id is not None
            assert document.state == "updated"
            assert document.updated_values == {"draft": (False, True)}
            assert document.draft is True

    # publish docs back
    ingestion_documents = deepcopy(create_ingestion_documents)
    ingestion_documents[0].file = io.BytesIO(b"new content")
    ingestion_documents[0].file.name = "test_page_0.txt"

    am_ingestion = KnowledgeManagementIngestion(
        ingestion_description="test ingestion",
        source_name="default",
        fs_connector=local_fs_connector,
        draft_mode=False,
        folder_id=create_folder.id,
    )
    result = await am_ingestion.ingest(ingestion_documents, overwrite=True)

    for document in result.ingested_documents:
        assert document.database_id is not None
        assert document.state == "updated"
        assert document.updated_values == {"draft": (True, False)}
        assert document.draft is False

    # cleanup stuff
    for i in ingestion_documents:
        await am_ingestion.doc_service.delete_document(
            document_id=i.database_id, user_id=DEFAULT_USER.id
        )
    await am_ingestion.doc_service.cleanup_deleted_documents()
    await am_ingestion.ingestion_service.delete_ingestion(am_ingestion.ingestion.id)
