#!/bin/bash

# Activate PDM virtual environment
echo "Activating PDM virtual environment..."
cd /home/<USER>/packages/gen_os_am_core && source .venv/bin/activate

# Run KM migrations
echo "Running KM database migrations..."
cd /home/<USER>/packages/gen_os_am_knowledge/src && alembic upgrade head

# Run CM migrations
echo "Running CM database migrations..."
cd /home/<USER>/packages/gen_os_am_workflows/src && alembic upgrade head

# Run Conversational migrations
echo "Running Conversational database migrations..."
cd /home/<USER>/packages/gen_os_am_conversational/src && alembic upgrade head

# Run AM migrations
echo "Running AM database migrations..."
cd /home/<USER>/packages/gen_os_am_core/src && alembic upgrade head

# Start the application
echo "Starting the application..."
cd /home/<USER>/packages/gen_os_am_core/src && uvicorn gen_os_am_core.main:am_app --host 0.0.0.0 --port 8080
