"""Pydantic schemas for agent configuration management API."""

# Common schemas
from .common import PaginationDetails, PaginationFilters

# Agent configuration schemas
from .agent_config import (
    AgentConfigurationBase,
    AgentConfigurationItem,
    AgentConfigurationResponse,
    AgentConfigurationSyncRequest,
)

# Agent schemas
from .agents import AgentCreateRequest, AgentUpdateRequest

# Global issues schemas
from .global_issues import (
    IssueListFilters,
    IssueListItem,
    PaginatedIssuesResponse,
)

# Agent issues schemas
from .agent_issues import (
    IncidentSoftDeleteRequest,
    IncidentSoftDeleteResponse,
    IssueCreateRequest,
    IssueResponse,
    IssueUpdateRequest,
    IssueUpdateResponse,
    IssueWithIncidents,
    ReportedIncidentConversationBase,
    ReportedIncidentConversationCreate,
    ReportedIncidentConversationResponse,
    ReportedIncidentConversationUnified,
    ReportedIncidentWorkflowBase,
    ReportedIncidentWorkflowCreate,
    ReportedIncidentWorkflowResponse,
    ReportedIncidentWorkflowUnified,
)

__all__ = [
    # Common schemas
    "PaginationDetails",
    "PaginationFilters",
    # Agent configuration schemas
    "AgentConfigurationBase",
    "AgentConfigurationItem",
    "AgentConfigurationResponse",
    "AgentConfigurationSyncRequest",
    # Agent schemas
    "AgentCreateRequest",
    "AgentUpdateRequest",
    # Global issues schemas
    "IssueListFilters",
    "IssueListItem",
    "PaginatedIssuesResponse",
    # Agent issues schemas
    "IncidentSoftDeleteRequest",
    "IncidentSoftDeleteResponse",
    "IssueCreateRequest",
    "IssueResponse",
    "IssueUpdateRequest",
    "IssueUpdateResponse",
    "IssueWithIncidents",
    "ReportedIncidentConversationBase",
    "ReportedIncidentConversationCreate",
    "ReportedIncidentConversationResponse",
    "ReportedIncidentConversationUnified",
    "ReportedIncidentWorkflowBase",
    "ReportedIncidentWorkflowCreate",
    "ReportedIncidentWorkflowResponse",
    "ReportedIncidentWorkflowUnified",
] 