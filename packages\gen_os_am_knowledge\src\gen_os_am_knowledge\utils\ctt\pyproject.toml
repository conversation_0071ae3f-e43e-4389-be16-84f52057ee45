[build-system]
requires = ["pdm-backend"]
build-backend = "pdm.backend"

[project]
name = "ctt-utils"
version = "0.1.7"

description = "Utils"

authors = [
    {name = "DareData, S.A.", email = ""}, # add your name
]

requires-python = ">=3.12"
readme = "README.md"
license = {text = "Proprietary"}
dependencies = [
    "pip",
    "opentelemetry-api>=1.30.0",
    "opentelemetry-sdk>=1.30.0",
    "opentelemetry-instrumentation-logging>=0.51b0",
    "ecs-logging>=2.2.0",
    "pydantic>=2.10.6",
    "pydantic-settings>=2.9.1",
    "fastapi>=0.115.11"
]

[dependency-groups]
dev = [
    "pre-commit<=4.0.0,>=3.8.0",
    "isort<6.0.0,>=5.13.2",
    "ipykernel<=7.0.0,>=6.29.5",
]
test = ["pytest<9,>=8.3.3", "pytest-cov<7.0.0,>=6.0.0"]
doc = [ "sphinx<9.0.0,>=8.0.0" ]



[tool.pdm]
# to generate the wheel with pdm build
distribution = true

[tool.pdm.scripts]
# run with pdm run precommit
precommit = "sh -c 'pre-commit run --show-diff-on-failure --color=always --files $(git ls-files )'"

# clean
clean = "../../scripts/clean.sh"

[tool.ruff]
lint.ignore=["E402","F811", "E722","E712","F821"]

[tool.pyright]
reportInvalidTypeForm = false
