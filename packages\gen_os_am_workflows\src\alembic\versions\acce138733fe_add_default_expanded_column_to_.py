"""Add default_expanded column to StepInput/Output.

Revision ID: acce138733fe
Revises: 0bf0185ad232
Create Date: 2025-06-06 10:27:12.376888

"""

from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "acce138733fe"
down_revision: str | None = "0bf0185ad232"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Add default_expanded column to step_input and step_output."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("step_input", schema=None) as batch_op:
        batch_op.add_column(sa.Column("default_expanded", sa.<PERSON><PERSON><PERSON>(), nullable=True))

    with op.batch_alter_table("step_output", schema=None) as batch_op:
        batch_op.add_column(sa.Column("default_expanded", sa.<PERSON>(), nullable=True))

    # ### end Alembic commands ###


def downgrade() -> None:
    """Drop default_expanded column from step_input and step_output."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("step_output", schema=None) as batch_op:
        batch_op.drop_column("default_expanded")

    with op.batch_alter_table("step_input", schema=None) as batch_op:
        batch_op.drop_column("default_expanded")

    # ### end Alembic commands ###
