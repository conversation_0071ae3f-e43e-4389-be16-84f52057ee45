{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from gen_os_am_knowledge.sdk.filesystem_connectors import (\n", "    AzureDataLakeStorageFSConnector,\n", "    BlobFSConnector,\n", "    LocalFSConnector,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Test connector"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["connector = BlobFSConnector(root_bucket=\"test_fsspec\", storage_type=\"gcs\")\n", "\n", "connector = BlobFSConnector(root_bucket=\"testbucket\", storage_type=\"s3\")\n", "\n", "connector = BlobFSConnector(root_bucket=\"testbucket\", storage_type=\"az\")\n", "\n", "connector = AzureDataLakeStorageFSConnector(root_folder=\"testbucket\")\n", "\n", "connector = LocalFSConnector(root_folder=\"testbucket\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["[]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["connector.ls(\"/\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["FSEntity(path='test_folder/', is_folder=True, is_file=False)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["connector.create_folder(\"test_folder\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["FSEntity(path='test_folder/1/2/3/4/5/', is_folder=True, is_file=False)"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["connector.create_folder(\"test_folder/1/2/3/4/5\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["with open(\"test.txt\", \"rb\") as f:\n", "    connector.upload_file(\"test_folder/1/2/3/4/5/\", \"test.txt\", f, overwrite=True)\n", "\n", "with open(\"test.txt\", \"rb\") as f:\n", "    connector.upload_file(\"test_folder/1/2/3\", \"test.txt\", f, overwrite=True)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["b'test'\n"]}], "source": ["file_obj = connector.download_file(\"test_folder/1/2/3/\", \"test.txt\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["connector.move_file(\"test_folder/1/2/3/test.txt\", \"test_folder/test2.txt\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["FSEntity(path='test_folder/1/2/3/4/5/test3.txt', is_folder=False, is_file=True)"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["connector.copy_file(\"test_folder/1/2/3/4/5/test.txt\", \"test_folder/1/2/3/4/5/test3.txt\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["['test3.txt', 'test.txt']"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["connector.ls(\"test_folder/1/2/3/4/5/\")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["FSEntity(path='test_folder/1/2/3/4/5/test3.txt', is_folder=False, is_file=True)"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["connector.delete_file(\"test_folder/1/2/3/4/5/\", \"test3.txt\")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["['4/']"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["connector.ls(\"test_folder/1/2/3/\")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["connector.copy_folder(\"test_folder/1/\", \"test_folder/1_copy\")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["['1/']"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["connector.ls(\"test_folder/1_copy\")"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["connector.copy_folder(\"test_folder/1/\", \"test_folder/1_copy/\", with_folder=False)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["['1/', '2/']"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["connector.ls(\"test_folder/1_copy\")"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["connector.move_folder(\"test_folder/1/\", \"new/\", with_folder=True)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["['1/']"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["connector.ls(\"new/\")"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/plain": ["['test2.txt', '1_copy/']"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["connector.ls(\"test_folder/\")"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/plain": ["FSEntity(path='test_folder/1_copy/', is_folder=True, is_file=False)"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["connector.delete_folder(\"test_folder/1_copy\")"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["['test2.txt']"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["connector.ls(\"test_folder/\")"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/plain": ["FSEntity(path='new/', is_folder=True, is_file=False)"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["connector.delete_folder(\"test_folder\")\n", "connector.delete_folder(\"new\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}