"""Module for the Azure Data Lake Storage connector."""

from gen_os_am_knowledge.sdk.filesystem_connectors.blob_fs_connector import (
    BlobFSConnector,
)


class AzureDataLakeStorageFSConnector(BlobFSConnector):
    """Connector for interacting with Azure Data Lake Storage.

    Important conventions:

    - To create folder empty file named .keep is created in that folder
        and all parent folders if they do not exist.
    - Delete/move/copy folder operations are performed over all the files
        including .keep files down the hierarchy.
    - ls() will just ignore .keep files for clear interface.
    - move/copy folder means copying folder itself with all its contents
        into new folder.
    `move_folder(”1/2/3/”, “new/”)` → `ls(”new”)` → `[”new/3/”]` and all the
        contents will be under `3/`

    Args:
        root_folder (str): The root folder of the filesystem
        azure_account_name (str | None): The name of the Azure account.
            If None, the name is taken from the settings
        azure_account_key (str | None): The key of the Azure account.
            If None, the key is taken from the settings

    """

    def __init__(
        self,
        root_folder: str | None = None,
        azure_account_name: str | None = None,
        azure_account_key: str | None = None,
    ):
        """Initialize the Azure Data Lake Storage connector."""
        super().__init__(
            root_bucket=root_folder,
            storage_type="az",
            azure_account_name=azure_account_name,
            azure_account_key=azure_account_key,
        )

    def move_folder(
        self,
        original_folder_path: str,
        destination_folder_path: str,
        overwrite: bool = False,
    ):
        """Move a folder in the filesystem."""
        super().move_folder(original_folder_path, destination_folder_path, overwrite)
        self.filesystem.rm(f"{self.prefix}{original_folder_path}", recursive=True)
