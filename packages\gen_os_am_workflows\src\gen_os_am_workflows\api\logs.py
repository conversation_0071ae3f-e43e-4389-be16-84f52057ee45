"""Module for logging requests and errors."""

import json
import logging
import socket
import uuid
from datetime import UTC, datetime


def _hostname() -> str:
    return socket.gethostname()


def _now() -> str:
    return datetime.now(UTC).isoformat() + "Z"


def generate_span_id() -> str:
    """Generate an OpenTelemetry-compliant span ID (8-byte/16 hex chars)."""
    return uuid.uuid4().hex[:16]


def build_log(
    severity: str, trace_id: str, span_id: str, attributes: dict[str, str], body: str
) -> str:
    """Build a log message."""
    log = {
        "traceId": trace_id,
        "spanId": span_id,
        "severityText": severity.upper(),
        "resource": {
            "host.name": _hostname(),
        },
        "attributes": attributes,
        "body": body,
        "timestamp": _now(),
    }
    return json.dumps(log)


def log(
    trace_id: str,
    span_id: str,
    functionality: str,
    body: str,
    attributes: dict[str, str],
    force_error: bool = False,
):
    """Log a message."""
    attributes["app.functionality"] = functionality

    severity = "info"
    if "status.code" in attributes:
        status_code = int(attributes["status.code"])
        if not (200 <= status_code < 300):
            severity = "error"

    if severity == "info" and not force_error:
        logging.getLogger().info(build_log("info", trace_id, span_id, attributes, body))
    else:
        logging.getLogger().error(build_log("error", trace_id, span_id, attributes, body))
