"""Tests for the services module."""

from unittest.mock import AsyncMock, patch

import pytest
from fastapi import FastAPI

from gen_os_am_core.services import (
    services_availability_check,
    services_database_check,
    services_router_inclusion,
)


class TestServicesAvailabilityCheck:
    """Test cases for services availability checking."""

    @patch("gen_os_am_core.services.importlib.util.find_spec")
    def test_all_services_available(self, mock_find_spec):
        """Test when all services are available."""
        mock_find_spec.return_value = "mock_spec"  # Non-None indicates module found

        result = services_availability_check()

        assert result["km"] is True
        assert result["cm"] is True
        assert result["conversational"] is True

    @patch("gen_os_am_core.services.importlib.util.find_spec")
    def test_no_services_available(self, mock_find_spec):
        """Test when no services are available."""
        mock_find_spec.return_value = None  # None indicates module not found

        result = services_availability_check()

        assert result["km"] is False
        assert result["cm"] is False
        assert result["conversational"] is False

    @patch("gen_os_am_core.services.importlib.util.find_spec")
    def test_partial_services_available(self, mock_find_spec):
        """Test when only some services are available."""

        def mock_find_spec_side_effect(module_name):
            if module_name == "gen_os_am_knowledge.api.main":
                return "mock_spec"  # KM available
            elif module_name == "gen_os_am_workflows.api":
                return None  # CM not available
            elif module_name == "gen_os_am_conversational.api.router":
                return "mock_spec"  # Conversational available
            return None

        mock_find_spec.side_effect = mock_find_spec_side_effect

        result = services_availability_check()

        assert result["km"] is True
        assert result["cm"] is False
        assert result["conversational"] is True

    @patch("gen_os_am_core.services.importlib.util.find_spec")
    def test_exception_handling(self, mock_find_spec):
        """Test that exceptions during module checking are handled gracefully."""
        mock_find_spec.side_effect = Exception("Module check failed")

        result = services_availability_check()

        # Should return False for all services when exceptions occur
        assert result["km"] is False
        assert result["cm"] is False
        assert result["conversational"] is False


class TestServicesDatabaseCheck:
    """Test cases for services database checking."""

    @pytest.mark.asyncio
    @patch("gen_os_am_core.services.services_availability_check")
    async def test_no_services_available(self, mock_availability):
        """Test database check when no services are available."""
        mock_availability.return_value = {
            "km": False,
            "cm": False,
            "conversational": False,
        }

        result = await services_database_check()

        assert result["km"] is False
        assert result["cm"] is False
        assert result["conversational"] is False

    @pytest.mark.asyncio
    @patch("gen_os_am_core.services.services_availability_check")
    async def test_services_available_but_import_fails(self, mock_availability):
        """Test database check when services are available but imports fail."""
        mock_availability.return_value = {
            "km": False,  # Set to False to avoid actual imports
            "cm": False,
            "conversational": False,
        }

        # Since services are not available, should return False for all
        result = await services_database_check()

        # Should return False for all since services are not available
        assert result["km"] is False
        assert result["cm"] is False
        assert result["conversational"] is False

    @pytest.mark.asyncio
    @patch("gen_os_am_core.services.services_availability_check")
    async def test_exception_handling_in_database_check(self, mock_availability):
        """Test that exceptions during database checking are handled gracefully."""
        mock_availability.return_value = {
            "km": False,  # Set to False to avoid actual imports
            "cm": False,
            "conversational": False,
        }

        # Since services are not available, should return False gracefully
        result = await services_database_check()

        # Should return False for all since services are not available
        assert result["km"] is False
        assert result["cm"] is False
        assert result["conversational"] is False


class TestServicesRouterInclusion:
    """Test cases for services router inclusion."""

    def test_router_inclusion_no_services(self):
        """Test router inclusion when no services are available."""
        app = FastAPI()

        # Mock logger
        class MockLogger:
            def info(self, msg):
                pass

            def warning(self, msg):
                pass

            def error(self, msg):
                pass

        logger = MockLogger()

        with patch("gen_os_am_core.services.services_availability_check") as mock_check:
            mock_check.return_value = {
                "km": False,
                "cm": False,
                "conversational": False,
            }

            # Should not raise any errors
            services_router_inclusion(app, logger)

            # App should still have basic routes (no service-specific routes)
            assert len(app.routes) >= 0  # Basic FastAPI always has some routes

    def test_router_inclusion_with_services_but_import_fails(self):
        """Test router inclusion when services are available but imports fail."""
        app = FastAPI()

        # Mock logger
        class MockLogger:
            def info(self, msg):
                pass

            def warning(self, msg):
                pass

            def error(self, msg):
                pass

        logger = MockLogger()

        with patch("gen_os_am_core.services.services_availability_check") as mock_check:
            mock_check.return_value = {"km": True, "cm": True, "conversational": True}

            # Should handle import errors gracefully
            services_router_inclusion(app, logger)

            # Should not crash and should have logged errors
            assert True  # If we get here, no exceptions were raised

    @patch("gen_os_am_core.services.services_availability_check")
    def test_router_inclusion_partial_services(self, mock_check):
        """Test router inclusion with partial service availability."""
        app = FastAPI()

        # Mock logger
        class MockLogger:
            def __init__(self):
                self.messages = []

            def info(self, msg):
                self.messages.append(("info", msg))

            def warning(self, msg):
                self.messages.append(("warning", msg))

            def error(self, msg):
                self.messages.append(("error", msg))

        logger = MockLogger()

        mock_check.return_value = {
            "km": False,
            "cm": True,  # Only CM available
            "conversational": False,
        }

        services_router_inclusion(app, logger)

        # Should have logged info about KM and Conversational not found
        info_messages = [msg for level, msg in logger.messages if level == "info"]
        assert any(
            "Knowledge Management (KM) module not found" in msg for msg in info_messages
        )
        assert any("Conversational module not found" in msg for msg in info_messages)

    def test_router_inclusion_preserves_app_state(self):
        """Test that router inclusion doesn't break the FastAPI app."""
        app = FastAPI()
        original_routes_count = len(app.routes)

        # Mock logger
        class MockLogger:
            def info(self, msg):
                pass

            def warning(self, msg):
                pass

            def error(self, msg):
                pass

        logger = MockLogger()

        with patch("gen_os_am_core.services.services_availability_check") as mock_check:
            mock_check.return_value = {
                "km": False,
                "cm": False,
                "conversational": False,
            }

            services_router_inclusion(app, logger)

            # App should still be functional
            assert len(app.routes) >= original_routes_count
            assert app.title is not None
