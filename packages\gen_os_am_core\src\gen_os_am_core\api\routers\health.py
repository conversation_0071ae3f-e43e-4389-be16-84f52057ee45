"""Health check router for the Agent Manager API."""

from typing import Any

from fastapi import APIRouter, Response, status

from gen_os_am_core.models.response import StatusResponse


class HealthRouter:
    """Health check router for the Agent Manager API."""

    def __init__(self, database_checker, services_checker, settings):
        """Initialize the health router.

        Args:
            database_checker: Function to check database access
            services_checker: Function to check services access
            settings: Application settings

        """
        self.database_checker = database_checker
        self.services_checker = services_checker
        self.settings = settings
        self.router = APIRouter()
        self._setup_routes()

    def _setup_routes(self):
        """Set up the health check routes."""

        @self.router.get("/healthz", tags=["Health"], response_model=StatusResponse)
        async def healthz(response: Response) -> StatusResponse:
            """Fast health check endpoint.

            Returns:
                StatusResponse: Health check status

            """
            return StatusResponse(status="ok")

        @self.router.get("/readyz", tags=["Health"])
        async def readyz(response: Response) -> dict[str, Any]:
            """Readiness check endpoint.

            Verifies if the service is ready to process requests by checking:
            - Database access
            - Services access (KM/CM services)
            - Configuration status

            Returns:
                dict: Detailed readiness status for each component

            """
            # Check database access
            db_status: dict[str, bool] = await self.database_checker()

            # Check services access
            resource_status: dict[str, bool] = await self.services_checker()

            # Check if all configurations are present
            config_status = bool(self.settings)  # Basic check if settings are loaded

            # Aggregate all checks
            all_checks_passed = (
                db_status and all(resource_status.values()) and config_status
            )

            # Prepare response
            status_response = {
                "status": "ready" if all_checks_passed else "not ready",
                "checks": {
                    "config": "ok" if config_status else "error",
                    "database": {
                        name: "ok" if status else "error"
                        for name, status in db_status.items()
                    },
                    "resources": {
                        name: "ok" if status else "error"
                        for name, status in resource_status.items()
                    },
                },
            }

            # Set response status code
            response.status_code = (
                status.HTTP_200_OK
                if all_checks_passed
                else status.HTTP_207_MULTI_STATUS
            )

            return status_response

    def get_router(self) -> APIRouter:
        """Get the configured router."""
        return self.router
