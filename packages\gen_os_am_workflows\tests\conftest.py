"""Test configuration and fixtures for the workflows API tests.

This module provides pytest fixtures for testing the workflows API,
including database setup, test client configuration, and environment
variable management for the test environment.
"""

import logging
import os
import sys
from pathlib import Path

import pytest
import pytest_asyncio
from fastapi.testclient import TestClient

from gen_os_am_workflows.database.models import Base
from gen_os_am_workflows.database.session import SessionManager

# Set environment variables BEFORE any imports to ensure they're available
# when the mock is applied in tests/api/conftest.py
database_file = f"{Path(__file__).parent}/test_database.db"

os.environ["AM_WF_DATABASE_NAME"] = database_file
os.environ["AM_WF_DATABASE_TYPE"] = "sqlite"
os.environ["AM_WF_DATABASE_USER"] = ""
os.environ["AM_WF_DATABASE_PORT"] = "5432"
os.environ["AM_WF_DATABASE_PASSWORD"] = ""
os.environ["AM_WF_DATABASE_HOST"] = ""
os.environ["AM_WF_API_LISTEN_HOST"] = "localhost"
os.environ["AM_WF_API_LISTEN_PORT"] = "8082"
os.environ["AM_WF_API_WORKERS"] = "1"
os.environ["AM_WF_CLOUD_PROVIDER"] = "gcp"
os.environ["AM_WF_GCP_PROJECT_NAME"] = "test-project"
os.environ["AM_WF_GCP_BUCKET_NAME"] = "test-bucket"
os.environ["AM_WF_GCP_SERVICE_ACCOUNT_EMAIL"] = "<EMAIL>"
os.environ["AM_WF_GCP_PUBSUB_TOPIC_NAME"] = "test-topic"

from gen_os_am_workflows.api import API  # noqa: E402

# Add the src directory to the Python path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))


@pytest.fixture(scope="module")
def client(create_tables, config):
    """Test client fixture with database tables created."""
    logger = logging.getLogger("api")
    logger.level = logging.DEBUG

    app = API().create_api()
    with TestClient(app) as client:
        yield client


@pytest_asyncio.fixture(scope="session")
async def config():
    """Configure test environment variables."""
    # Environment variables are already set at module level
    pass


@pytest_asyncio.fixture(scope="session")
async def create_tables(config):
    """Create database tables for testing."""
    async with SessionManager.get_session() as db_session:
        await (await db_session.connection()).run_sync(Base.metadata.create_all)
        yield
        if Path(database_file).exists():
            os.remove(database_file)


@pytest_asyncio.fixture(scope="session")
async def get_session(create_tables):
    """Get database session for testing."""
    return SessionManager.get_session
