"""Configure test fixtures for the API tests."""

from unittest.mock import MagicMock, patch

import pytest

# Apply the patch immediately, before any imports are processed
settings_patch = patch("gen_os_am_workflows.settings.Settings.get_settings")
mock_get_settings = settings_patch.start()

# Create and configure mock settings
mock_settings = MagicMock()
# Storage settings
mock_settings.STORAGE_PROVIDER = "gcp"
mock_settings.GCP_BUCKET_NAME = "test-bucket"
mock_settings.GCP_PROJECT_NAME = "test-project"
mock_settings.GCP_SERVICE_ACCOUNT_EMAIL = "<EMAIL>"
mock_settings.GCP_PUBSUB_TOPIC_NAME = "test-topic"

# Database settings
mock_settings.DATABASE_NAME = "test-db"
mock_settings.DATABASE_TYPE = "sqlite"
mock_settings.DATABASE_USER = ""
mock_settings.DATABASE_PORT = 5432
mock_settings.DATABASE_PASSWORD = ""
mock_settings.DATABASE_HOST = ""
mock_settings.DATABASE_POOL_SIZE = 10

# API settings
mock_settings.API_LISTEN_HOST = "localhost"
mock_settings.API_LISTEN_PORT = 8082
mock_settings.API_WORKERS = 1

# Cloud provider settings
mock_settings.CLOUD_PROVIDER = "gcp"


# Configure cloud_provider_properties method
def mock_cloud_provider_properties():
    """Return mock cloud provider properties for testing."""
    return {
        "provider": "gcp",
        "project_name": "test-project",
        "bucket_name": "test-bucket",
        "service_account_email": "<EMAIL>",
        "topic": "test-topic",
    }


mock_settings.cloud_provider_properties = mock_cloud_provider_properties

# Set the return value for get_settings
mock_get_settings.return_value = mock_settings


# Clean up the patch after tests
@pytest.fixture(scope="session", autouse=True)
def cleanup_settings_patch():
    """Clean up the settings patch after all tests are run."""
    yield
    settings_patch.stop()
