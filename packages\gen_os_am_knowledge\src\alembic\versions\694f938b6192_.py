"""Initial migration.

Revision ID: 694f938b6192
Revises:
Create Date: 2025-03-13 17:53:15.483593

"""

from collections.abc import Sequence

import sqlalchemy as sa
import sqlmodel

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "694f938b6192"
down_revision: str | None = None
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Upgrade the database."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "folder",
        sa.Column("id", sa.Uuid(), nullable=False),
        sa.Column("name", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("parent_id", sa.Uuid(), nullable=True),
        sa.ForeignKeyConstraint(
            ["parent_id"],
            ["folder.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name", "parent_id", name="unique_folder_name_parent_id"),
    )
    op.create_table(
        "source",
        sa.Column("id", sa.Uuid(), nullable=False),
        sa.Column("name", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("type", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("meta", sa.JSON(), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name"),
    )
    op.create_table(
        "tag",
        sa.Column("id", sa.Uuid(), nullable=False),
        sa.Column("tag", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("tag"),
    )
    op.create_table(
        "user",
        sa.Column("id", sa.Uuid(), nullable=False),
        sa.Column("name", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("external_id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("external_id"),
        sa.UniqueConstraint("name"),
    )
    op.create_table(
        "ingestion",
        sa.Column("id", sa.Uuid(), nullable=False),
        sa.Column("description", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("status", sa.TEXT(), nullable=False),
        sa.Column("source_id", sa.Uuid(), nullable=False),
        sa.Column("last_run_date", sa.DateTime(), nullable=True),
        sa.Column("last_success_date", sa.DateTime(), nullable=True),
        sa.Column("meta", sa.JSON(), nullable=True),
        sa.Column("error", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.ForeignKeyConstraint(
            ["source_id"],
            ["source.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "document",
        sa.Column("id", sa.Uuid(), nullable=False),
        sa.Column("filename", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("folder_id", sa.Uuid(), nullable=True),
        sa.Column("original_path", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column("created_date", sa.DateTime(), nullable=False),
        sa.Column("created_by_id", sa.Uuid(), nullable=False),
        sa.Column("size", sa.BigInteger(), nullable=False),
        sa.Column("type", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("kind", sa.TEXT(), nullable=False),
        sa.Column("meta", sa.JSON(), nullable=True),
        sa.Column("last_modified_by_id", sa.Uuid(), nullable=False),
        sa.Column("modification_date", sa.DateTime(), nullable=False),
        sa.Column("ingestion_id", sa.Uuid(), nullable=False),
        sa.Column("hash", sa.BigInteger(), nullable=True),
        sa.Column("document_status", sa.TEXT(), nullable=False),
        sa.Column("deleted_date", sa.DateTime(), nullable=True),
        sa.Column("draft", sa.Boolean(), nullable=False),
        sa.ForeignKeyConstraint(
            ["created_by_id"],
            ["user.id"],
        ),
        sa.ForeignKeyConstraint(
            ["folder_id"],
            ["folder.id"],
        ),
        sa.ForeignKeyConstraint(
            ["ingestion_id"],
            ["ingestion.id"],
        ),
        sa.ForeignKeyConstraint(
            ["last_modified_by_id"],
            ["user.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("filename", "folder_id", name="unique_filename_folder_id"),
    )
    op.create_index(
        op.f("ix_document_deleted_date"), "document", ["deleted_date"], unique=False
    )
    op.create_index(
        op.f("ix_document_document_status"),
        "document",
        ["document_status"],
        unique=False,
    )
    op.create_index(op.f("ix_document_draft"), "document", ["draft"], unique=False)
    op.create_index(
        "ix_folder_id_hash",
        "document",
        ["folder_id", "hash"],
        unique=False,
        postgresql_where="hash IS NOT NULL",
    )
    op.create_table(
        "document_tags",
        sa.Column("document_id", sa.Uuid(), nullable=False),
        sa.Column("tag_id", sa.Uuid(), nullable=False),
        sa.ForeignKeyConstraint(
            ["document_id"],
            ["document.id"],
        ),
        sa.ForeignKeyConstraint(
            ["tag_id"],
            ["tag.id"],
        ),
        sa.PrimaryKeyConstraint("document_id", "tag_id"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade the database."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("document_tags")
    op.drop_index(
        "ix_folder_id_hash", table_name="document", postgresql_where="hash IS NOT NULL"
    )
    op.drop_index(op.f("ix_document_draft"), table_name="document")
    op.drop_index(op.f("ix_document_document_status"), table_name="document")
    op.drop_index(op.f("ix_document_deleted_date"), table_name="document")
    op.drop_table("document")
    op.drop_table("ingestion")
    op.drop_table("user")
    op.drop_table("tag")
    op.drop_table("source")
    op.drop_table("folder")
    # ### end Alembic commands ###
