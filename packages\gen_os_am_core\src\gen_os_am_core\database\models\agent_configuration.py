"""SQLAlchemy models for agent configuration management."""

from datetime import datetime

from sqlalchemy import DateTime, ForeignKey, Index, String, Text, func
from sqlalchemy.orm import Mapped, mapped_column, relationship

from gen_os_am_core.database.models.agents import Agent
from gen_os_am_core.database.models.base import Base


class AgentConfiguration(Base):
    """SQLAlchemy model for agent configurations.

    Stores configuration parameters for agents with their metadata.
    Each configuration parameter is a key-value pair associated with a specific agent.
    """

    __tablename__ = "agent_configuration"

    id: Mapped[str] = mapped_column(String, primary_key=True, nullable=False)
    agent_id: Mapped[str] = mapped_column(
        String, ForeignKey("agents.id"), nullable=False, index=True
    )
    name: Mapped[str] = mapped_column(String, nullable=False)
    description: Mapped[str | None] = mapped_column(Text, nullable=True)
    value: Mapped[str] = mapped_column(Text, nullable=False)
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now(), onupdate=func.now()
    )

    agent: Mapped[Agent] = relationship("Agent", foreign_keys=[agent_id])

    __table_args__ = (
        Index("ix_agent_configuration_agent_id_name", "agent_id", "name", unique=True),
    )

    def __repr__(self) -> str:
        """Return string representation of AgentConfiguration."""
        return (
            f"<AgentConfiguration(id='{self.id}', "
            f"agent_id='{self.agent_id}', "
            f"name='{self.name}')>"
        )
