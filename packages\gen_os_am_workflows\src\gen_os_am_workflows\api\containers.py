"""Dependency injection container for the case management API."""

from dependency_injector import containers, providers
from gen_os_sdk_emulator.infrastructure.storage import StorageFactory, StorageInterface

from gen_os_am_workflows.api.webhooks import WebhooksService
from gen_os_am_workflows.settings import Settings

settings = Settings.get_settings()


class Container(containers.DeclarativeContainer):
    """Dependency injection container for the case management API."""

    wiring_config = containers.WiringConfiguration(modules=["gen_os_am_workflows.api.router"])
    config = providers.Configuration()

    storage_service: StorageInterface = providers.Factory(
        StorageFactory.create_storage_service,
        provider=settings.cloud_provider_properties.get("provider"),
        project_name=settings.cloud_provider_properties.get("project_name"),
        bucket_name=settings.cloud_provider_properties.get("bucket_name"),
        service_account_email=settings.cloud_provider_properties.get("service_account_email"),
    )

    webhooks_service: WebhooksService = providers.Factory(
        WebhooksService,
        notification_webhook_urls=settings.INTERACTION_NOTIFICATION_WEBHOOK_URLS,
    )
