"""Module for document operations."""

import logging
import os
import uuid
from copy import deepcopy
from datetime import datetime, timezone
from io import BytesIO, IOBase
from typing import Any

import httpx
from psycopg.errors import UniqueViolation
from pydantic import BaseModel
from sqlalchemy import func
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import joinedload
from sqlmodel import col, delete, select
from starlette.datastructures import UploadFile

from gen_os_am_knowledge.config.database import get_async_session
from gen_os_am_knowledge.config.logger_config import get_sdk_logger
from gen_os_am_knowledge.config.settings import DocumentServiceSettings
from gen_os_am_knowledge.sdk.filesystem_connectors.base import BaseFileSystemConnector
from gen_os_am_knowledge.sdk.models import (
    Document,
    DocumentTags,
    Folder,
    Ingestion,
    Source,
    Tag,
    User,
)
from gen_os_am_knowledge.sdk.models.common import (
    DEFAULT_FOLDER,
    DEFAULT_INGESTION,
    master_document,
    slave_document,
)
from gen_os_am_knowledge.sdk.operations.folder_ops import FolderService
from gen_os_am_knowledge.sdk.operations.ingestion_ops import IngestionService
from gen_os_am_knowledge.sdk.operations.tag_ops import TagService
from gen_os_am_knowledge.sdk.operations.user_ops import UserService
from gen_os_am_knowledge.sdk.operations.utils import (
    NOT_PROVIDED,
    DeletionResult,
    UpdateResult,
)
from gen_os_am_knowledge.sdk.utils.hash import calculate_hash
from gen_os_am_knowledge.sdk.utils.protocols import NamedFileLike
from gen_os_am_knowledge.utils.ctt.ctt_utils.logging.logging_utils import (
    error_event_log,
    log_decorator,
    start_time_var,
)


class CheckDocumentResult(BaseModel):
    """Result of the check_document operation.

    Attributes:
        document: Document | None
            The document if found, otherwise None.
        deleted_flg: bool
            True if the found document is deleted in DB. Default is False.
        same_hash_flg: bool
            True if the found document has the same hash. Default is False.

    """

    document: Document | None
    deleted_flg: bool = False
    same_hash_flg: bool = False


class DocumentService:
    """Service class to handle CRUD operations for Document model."""

    def __init__(
        self,
        storage_connector: BaseFileSystemConnector,
        logger: logging.Logger | None = None,
    ):
        """Initialize the DocumentOperations service.

        Args:
            storage_connector: The storage connector to use for file operations.
            logger: The logger to use for logging operations.

        """
        self.logger = logger or get_sdk_logger()
        self.storage_connector = storage_connector
        self.user_service = UserService(logger=self.logger)
        self.ingestion_service = IngestionService(logger=self.logger)
        self.tag_service = TagService(logger=self.logger)
        self.folder_service = FolderService(
            fs_connector=self.storage_connector, logger=self.logger
        )

        self.agent_url_prefix = DocumentServiceSettings.get_settings().AGENT_API_URL

    @log_decorator(category=["database", "file"])
    async def create_document(
        self,
        file: NamedFileLike,
        original_path: str | None = None,
        folder_id: uuid.UUID = DEFAULT_FOLDER.id,
        user_id: uuid.UUID | None = None,
        meta: dict[Any, Any] | None = None,
        ingestion_id: uuid.UUID | None = None,
        tags: list[str] | None = None,
        hash_: int | None = None,
        draft: bool = False,
    ) -> Document | None:
        """Create a new document.

        Create a new document in the database and upload the file to the storage bucket.

        Only one of user_id or ingestion_id must be provided.
        If user_id is provided, the document is created as a master document.
        If ingestion_id is provided, the document is created as a slave document.

        Args:
            file: The file to upload.
            original_path: The original path of the file. Default is None.
            folder_id: The ID of the folder to associate the document with.
                Default is None.
            user_id: The ID of the user creating the document. Default is None.
            meta: A dictionary of metadata to associate with the document.
                Default is None.
            ingestion_id: The ID of the ingestion process associated with the document.
                Default is None.
            tags: A list of tag names to associate with the document.
                Empty list to clean all tags. Default is None.
            hash_: The hash of the file for slave documents. Default is None.
            draft: If the document is created as draft. Default is False.

        Returns:
            Document: The created Document instance.

        """
        if not user_id and not ingestion_id:
            msg = "Either user_id or ingestion_id must be provided"
            raise ValueError(msg)

        if user_id and ingestion_id:
            msg = "Only one of user_id or ingestion_id can be provided"
            raise ValueError("Only one of user_id or ingestion_id can be provided")

        if user_id:
            user = await self._check_user(user_id)
            kind = "master"

        if ingestion_id:
            ingestion = await self._check_ingestion(ingestion_id)
            kind = "slave"

        if tags:
            document_tags = []
            for i in range(len(tags)):
                tag = await self._check_tag(tags[i])
                document_tags.append(tag)

        name = self._get_filename(file)
        type_ = name.split(".")[-1]
        size = self._get_filesize(file)
        hash_ = calculate_hash(file) if hash_ is None else hash_

        if kind == "master":
            document = master_document(
                created_by_id=user.id,
                last_modified_by_id=user.id,
                filename=name,
                original_path=original_path,
                size=size,
                type_=type_,
                meta=meta,
                folder_id=folder_id,
                tags=document_tags if tags else None,
                document_status="Syncing Storage",
                draft=draft,
                hash_=hash_,
            )

        elif kind == "slave":
            document = slave_document(
                ingestion_id=ingestion.id,
                filename=name,
                original_path=original_path,
                size=size,
                type_=type_,
                meta=meta,
                folder_id=folder_id,
                tags=document_tags if tags else None,
                hash_=hash_,
                document_status="Syncing Storage",
                draft=draft,
            )
        if document:
            check_result = await self._check_document(document=document)

            if check_result.document:
                if not check_result.deleted_flg and check_result.same_hash_flg:
                    raise ValueError(
                        f"Document with name {name} already exists in this folder"
                    )

                fields_to_update = self._document_fields_comparison(
                    existing_doc=check_result.document,
                    new_doc=document,
                    fields_to_compare=[
                        "original_path",
                    ],  # TODO: add "tags" later as they cause issue due to lazy load.
                    # Add "meta" later as it is used customly now
                )
                if check_result.deleted_flg and check_result.same_hash_flg:
                    result = await self.restore_document(
                        document_id=check_result.document.id,
                        user_id=user.id,
                        draft=draft,
                    )

                    if fields_to_update:
                        result = await self.update_document(
                            document_id=result.updated_instance.id,
                            last_modified_by_id=user.id,
                            **fields_to_update,
                        )

                    return result.updated_instance

                elif not check_result.deleted_flg and not check_result.same_hash_flg:
                    # update with file
                    # hash will be rewritten, file in storage and VectorStore
                    # will be created
                    result = await self.update_document(
                        document_id=check_result.document.id,
                        last_modified_by_id=user.id,
                        file=file,
                        draft=draft
                        if check_result.document.draft != draft
                        else NOT_PROVIDED,
                        **fields_to_update,
                    )

                    return result.updated_instance

                elif check_result.deleted_flg and not check_result.same_hash_flg:
                    # restore document in DB only
                    restored_doc = await self._restore_document(
                        document_id=check_result.document.id,
                        user_id=user.id,
                        draft=draft,
                    )
                    # update with file (hash will be rewritten,
                    # file in storage and VectorStore will be created)
                    result = await self.update_document(
                        document_id=restored_doc.id,
                        last_modified_by_id=user.id,
                        file=file,
                        draft=draft if restored_doc.draft != draft else NOT_PROVIDED,
                        **fields_to_update,
                    )

                    return result.updated_instance
            elif not check_result.document:
                created_document = await self._create_document(document)
                if created_document:
                    try:
                        await self._storage_upload(document=created_document, file=file)
                    except Exception as e:
                        # hard delete to revert the document creation
                        async for session in get_async_session():
                            stmt = delete(Document).where(
                                Document.id == created_document.id
                            )
                            await session.execute(stmt)
                            await session.commit()
                            raise e

                    response = await self._agent_create_document(created_document)
                    document = await self._agent_response_handler(
                        response, created_document
                    )
                    return document

    @log_decorator(type_="access")
    async def get_document(self, document_id: uuid.UUID) -> Document | None:
        """Retrieve document from the database based on document id.

        Args:
            document_id: The id of the document to retrieve.

        Returns:
            Document: The retrieved document instance or None if not found.

        """
        async for session in get_async_session():
            query = select(Document).where(Document.id == document_id)
            return (await session.execute(query)).scalars().first()

    @log_decorator(type_="access")
    async def search_documents(
        self,
        filename: str | None = None,
        folder_name: str | None = None,
        folder_id: uuid.UUID | None = None,
        tags: list[str] | None = None,
        created_by: str | None = None,
        last_modified_by: str | None = None,
        ingestion_id: uuid.UUID | None = None,
        document_status: Document._DOC_STATUS_TYPE | None = None,
        metadata_field: dict | None = None,
        hash_: int | None = None,
        kind: str | None = None,
        limit: int = 20,
        offset: int = 0,
        search_deleted: bool = False,
        draft: bool = False,
        updated_from: datetime | None = None,
        updated_to: datetime | None = None,
        draft_only: bool = False,
    ) -> list[Document]:
        """Search for documents based on multiple criteria (all params are optional).

        If nothing is provided will return all not deleted documents.

        Args:
            filename: The name of the file to search for.
            folder_name: The name of the folder to search for.
            folder_id: The ID of the folder to search documents in.
            tags: A list of tags to filter by.
            created_by: The name of the user who created the document.
            last_modified_by: The name of the user who last modified the document.
            ingestion_id: The ID of the ingestion process associated with the document.
            document_status: The status of the document.
            metadata_field: A dictionary of key-value pairs to match against
                the document's metadata.
            hash_: The hash of the file for slave documents.
            kind: The kind of document to search for.
            limit: The maximum number of results to return.
            offset: The number of results to skip.
            search_deleted: Whether to include deleted documents in the search.
            draft: Whether to include draft documents in the search,
                returns all documents on True. Default is False.
            draft_only: Whether to include only draft documents in the search,
                returns only draft documents on True. Default is False.
            updated_from: The earliest modification date to search from.
            updated_to: The latest modification date to search up to.

        Returns:
            list[Document]: A list of matching Document instances.

        """
        async for session in get_async_session():
            if search_deleted:
                query = select(Document)
            else:
                query = select(Document).where(Document.deleted_date == None)  # noqa: E711 Comparison to `None` should be `cond is None`

            if filename:
                query = query.where(Document.filename == filename)

            # Search by tags
            if tags:
                query = query.join(Document.tags).where(Tag.tag.in_(tags))

            # Search by create_by
            if created_by:
                query = query.join(Document.created_by).where(User.name == created_by)

            # Search by last_modified_by
            if last_modified_by:
                query = query.join(Document.last_modified_by).where(
                    User.name == last_modified_by
                )

            # Search by folder name
            if folder_name:
                query = query.join(Document.folder).where(Folder.name == folder_name)

            # Search by folder id
            if folder_id:
                query = query.where(Document.folder_id == folder_id)

            # Search by ingestion id
            if ingestion_id:
                query = query.where(Document.ingestion_id == ingestion_id)

            # Search by document status
            if document_status:
                query = query.where(Document.document_status == document_status)

            # Search by metadata fields
            if metadata_field:
                for key, value in metadata_field.items():
                    query = query.where(Document.meta.op("->>")(key) == value)

            # Search by hash
            if hash_:
                query = query.where(Document.hash == hash_)

            # Search by kind
            if kind:
                query = query.where(Document.kind == kind)

            # Search by updated date range
            if updated_from:
                query = query.where(Document.modification_date >= updated_from)
            if updated_to:
                query = query.where(Document.modification_date <= updated_to)

            if draft is False:
                query = query.where(Document.draft == draft)
            if draft_only is True:
                query = query.where(Document.draft == True)  # noqa: E712

            query = query.limit(limit).offset(offset)

            # Returns list of Document objects
            return (await session.execute(query)).scalars().all()

    @log_decorator()
    async def update_document_status(
        self,
        document_id: uuid.UUID,
        user_id: uuid.UUID,
        status: Document._DOC_STATUS_TYPE,
    ) -> UpdateResult:
        """Lightweight method to update the status of the document.

        Args:
            document_id: The ID of the document to update.
            user_id: The ID of the user updating the document.
            status: The new status of the document.

        Returns:
            UpdateResult: The result of the update operation.

        """
        await self._check_user(user_id)
        start = start_time_var.get(datetime.now(timezone.utc))
        async for session in get_async_session():
            query = select(Document).where(Document.id == document_id)
            document = (await session.execute(query)).scalars().first()
            if not document:
                error_event_log(
                    msg=f"Document with id {document_id} not found.",
                    start=start,
                    error=ValueError(f"Document with id {document_id} not found."),
                    logger=self.logger,
                )
                return UpdateResult(
                    success=False, error=f"Document with id {document_id} not found."
                )
            else:
                updated_values = {"document_status": (document.document_status, status)}
                document.document_status = status
                document.last_modified_by_id = user_id
                document_copy = deepcopy(document)
                session.add(document)
                await session.commit()
                return UpdateResult(
                    success=True,
                    updated_instance=document_copy,
                    updated_values=updated_values,
                )

    @log_decorator()
    async def publish_document(
        self,
        document_id: uuid.UUID,
        user_id: uuid.UUID,
    ) -> UpdateResult:
        """Update document draft field to False.

        Args:
            document_id: The ID of the document to update.
            user_id: The ID of the user updating the document.

        Returns:
            UpdateResult: The result of the update operation.

        """
        await self._check_user(user_id)
        start = start_time_var.get(datetime.now(timezone.utc))
        async for session in get_async_session():
            query = select(Document).where(Document.id == document_id)
            document = (await session.execute(query)).scalars().first()
            if not document:
                msg = f"Document with id {document_id} not found."
                error_event_log(
                    msg=msg,
                    start=start,
                    error=ValueError(msg),
                    logger=self.logger,
                )
                return UpdateResult(success=False, error=msg)
            else:
                if document.draft == False:  # noqa: E712
                    msg = f"Document with id {document_id} is already published."
                    error_event_log(
                        msg=msg,
                        start=start,
                        error=ValueError(msg),
                        logger=self.logger,
                    )
                    return UpdateResult(success=False, error=msg)
                updated_values = {"draft": (document.draft, False)}
                document.draft = False
                document.last_modified_by_id = user_id
                document_copy = deepcopy(document)
                session.add(document)
                await session.commit()

        response = await self._agent_publish_document(document_copy)
        document = await self._agent_response_handler(response, document_copy)

        return UpdateResult(
            success=True,
            updated_instance=document_copy,
            updated_values=updated_values,
        )

    # @log_decorator()
    # async def draft_document(
    #     self,
    #     document_id: uuid.UUID,
    #     user_id: uuid.UUID,
    # ) -> UpdateResult:
    #     """
    #     Update document draft field to True.

    #     Args:
    #         document_id: The ID of the document to update.
    #         user_id: The ID of the user updating the document.

    #     Returns:
    #         UpdateResult: The result of the update operation.
    #     """
    #     await self._check_user(user_id)
    #     start = start_time_var.get(datetime.now(timezone.utc))
    #     async for session in get_async_session():
    #         query = select(Document).where(Document.id == document_id)
    #         document = (await session.execute(query)).first()
    #         if not document:
    #             msg = f"Document with id {document_id} not found."
    #             error_event_log(
    #                 msg=msg,
    #                 start=start,
    #                 error=ValueError(msg),
    #                 logger=self.logger,
    #             )
    #             return UpdateResult(success=False, error=msg)
    #         else:
    #             if document.draft == True:
    #                 msg = f"Document with id {document_id} is already in draft."
    #                 error_event_log(
    #                     msg=msg,
    #                     start=start,
    #                     error=ValueError(msg),
    #                     logger=self.logger,
    #                 )
    #                 return UpdateResult(
    #                     success=False,
    #                     error=msg,
    #                 )
    #             updated_values = {"draft": (document.draft, True)}
    #             document.draft = True
    #             document.last_modified_by_id = user_id
    #             document_copy = deepcopy(document)
    #             session.add(document)
    #             await session.commit()

    #     response = await self._agent_draft_document(document_copy)
    #     document = await self._agent_response_handler(response, document_copy)

    #     return UpdateResult(
    #         success=True,
    #         updated_instance=document,
    #         updated_values=updated_values,
    #     )

    @log_decorator()
    async def update_document(
        self,
        document_id: uuid.UUID,
        last_modified_by_id: uuid.UUID,
        filename: str | type[NOT_PROVIDED] = NOT_PROVIDED,
        folder_id: uuid.UUID | type[NOT_PROVIDED] = NOT_PROVIDED,
        tags: list[str] | type[NOT_PROVIDED] = NOT_PROVIDED,
        meta: dict | None | type[NOT_PROVIDED] = NOT_PROVIDED,
        original_path: str | None | type[NOT_PROVIDED] = NOT_PROVIDED,
        draft: bool | type[NOT_PROVIDED] = NOT_PROVIDED,
        hash_: int | type[NOT_PROVIDED] = NOT_PROVIDED,
        size: int | type[NOT_PROVIDED] = NOT_PROVIDED,
        deleted_date: datetime | None | type[NOT_PROVIDED] = NOT_PROVIDED,
        file: NamedFileLike | None = None,
    ):
        """Update an existing document by document_id.

        last_modified_by_id is required user_id field as well.

        Note! on file change  - name of the file will be kept as is,
        only the content will be updated.
        Only one of file, filename or folder_id can be changed in a single operation

        Args:
            document_id: The ID of the document to update.
            last_modified_by_id: The ID of the user updating the document.
            filename: The new name of the document.
            folder_id: The ID of the folder to associate the document with.
            tags: A list of tag names to associate with the document.
            meta: A dictionary of metadata to associate with the document.
            original_path: The new original path of the document.
            deleted_date: The new deleted date of the document.
            hash_: The new hash of the document.
            size: The new size of the document.
            file: The new file to upload.
            draft: To put the document into draft mode or publish it.

        Returns:
            UpdateResult: The result of the update operation.

        """
        if (
            sum(
                [
                    file is not None,
                    filename is not NOT_PROVIDED,
                    folder_id is not NOT_PROVIDED,
                ]
            )
            > 1
        ):
            msg = (
                f"Only one of file, "
                f"filename or folder_id can be changed in a single operation"
            )
            error_event_log(
                msg=msg,
                start=start_time_var.get(datetime.now(timezone.utc)),
                error=ValueError(msg),
                logger=self.logger,
            )
            raise ValueError(msg)

        await self._check_user(last_modified_by_id)
        start = start_time_var.get(datetime.now(timezone.utc))

        async for session in get_async_session():
            query = (
                select(Document)
                .where(Document.id == document_id)
                .options(joinedload(Document.tags))
            )
            document = (await session.execute(query)).scalars().first()
            if not document:
                msg = f"Document with id {document_id} not found"
                error_event_log(
                    msg=msg,
                    start=start,
                    error=ValueError(msg),
                    logger=self.logger,
                )
                return UpdateResult(success=False, error=msg)
            else:
                old_document = deepcopy(document)
                document_tags = NOT_PROVIDED
                old_tags = None
                if tags is not NOT_PROVIDED:
                    start = datetime.now(timezone.utc)
                    old_tags = [tag.model_copy() for tag in document.tags]
                    document_tags = []
                    for i in range(len(tags)):
                        tag = await self._check_tag(tags[i])
                        document_tags.append(tag)

                update_dict = {
                    "filename": filename,
                    "original_path": original_path,
                    "folder_id": folder_id,
                    "last_modified_by_id": last_modified_by_id,
                    "tags": document_tags,
                    "meta": meta,
                    "draft": draft,
                    "hash": hash_,
                    "size": size,
                    "deleted_date": deleted_date,
                }
                updated_values = {}
                for k, v in update_dict.items():
                    if v is not NOT_PROVIDED:
                        if getattr(document, k) != v:
                            if k != "tags":
                                if isinstance(getattr(document, k), uuid.UUID):
                                    updated_values[k] = (
                                        str(getattr(document, k)),
                                        str(v),
                                    )
                                else:
                                    updated_values[k] = (getattr(document, k), v)
                                setattr(document, k, v)
                            else:
                                tags_before = [tag.tag for tag in document.tags]

                                # this bad structure due to sqlmodel constraints on repeated instances in session  # noqa: E501
                                for tag in document_tags:
                                    if tag not in document.tags:
                                        document.tags.append(tag)

                                tags_to_remove = [
                                    tag
                                    for tag in document.tags
                                    if tag not in document_tags
                                ]
                                for tag in tags_to_remove:
                                    document.tags.remove(tag)

                                tags_after = [tag.tag for tag in document.tags]
                                updated_values[k] = (tags_before, tags_after)
                if file:
                    if self._get_filename(file).split(".")[-1] != document.type:
                        raise ValueError("File cannot be changed to different type")

                    document.size = self._get_filesize(file)
                    if document.size != old_document.size:
                        updated_values["size"] = (old_document.size, document.size)

                    document.hash = calculate_hash(file)
                    if document.hash != old_document.hash:
                        updated_values["hash"] = (old_document.hash, document.hash)

                if hash_ is not NOT_PROVIDED:
                    # check that file with that hash exists in file storage

                    storage_filename = self._create_storage_name(
                        document.filename, hash_
                    )
                    folder_path = await FolderService(
                        fs_connector=self.storage_connector
                    ).get_folder_path(document.folder_id)

                    if storage_filename not in self.storage_connector.ls(
                        folder_path=folder_path
                    ):
                        raise ValueError(
                            f"File with hash {hash_} not found in storage. "
                            f"Cannot update hash to that value"
                        )

                new_document = deepcopy(document)
                try:
                    session.add(document)
                    await session.commit()
                except IntegrityError as e:
                    if isinstance(e.orig, UniqueViolation):
                        raise ValueError(
                            f"Document with name {new_document.filename} already "
                            f"exists in this folder"
                        ) from e
                    raise e
                except Exception as e:
                    raise e

        try:
            if file or filename is not NOT_PROVIDED or folder_id is not NOT_PROVIDED:
                document = (
                    await self.update_document_status(
                        document_id=document_id,
                        user_id=last_modified_by_id,
                        status="Syncing Storage",
                    )
                ).updated_instance

                if filename is not NOT_PROVIDED:
                    folder_path = await FolderService(
                        fs_connector=self.storage_connector
                    ).get_folder_path(new_document.folder_id)

                    old_storage_filename = self._create_storage_name(
                        old_document.filename, old_document.hash
                    )
                    new_storage_filename = self._create_storage_name(
                        new_document.filename, new_document.hash
                    )

                    self.storage_connector.move_file(
                        original_file_path=folder_path + old_storage_filename,
                        destination_file_path=folder_path + new_storage_filename,
                    )
                if file:
                    await self._storage_upload(document=new_document, file=file)
                if folder_id is not NOT_PROVIDED:
                    old_folder_path = await FolderService(
                        fs_connector=self.storage_connector
                    ).get_folder_path(old_document.folder_id)
                    new_folder_path = await FolderService(
                        fs_connector=self.storage_connector
                    ).get_folder_path(folder_id)

                    old_storage_filename = self._create_storage_name(
                        old_document.filename, old_document.hash
                    )

                    self.storage_connector.move_file(
                        original_file_path=old_folder_path + old_storage_filename,
                        destination_file_path=new_folder_path + old_storage_filename,
                    )
            else:
                document = await self.get_document(document_id)
        except Exception as e:
            async for session in get_async_session():
                query = (
                    select(Document)
                    .where(Document.id == old_document.id)
                    .options(joinedload(Document.tags))
                )
                document = (await session.execute(query)).scalars().first()
                for k, v in old_document.model_dump().items():
                    setattr(document, k, v)

                if old_tags:
                    for tag in old_tags:
                        if tag not in document.tags:
                            document.tags.append(tag)
                    tags_to_remove = [
                        tag for tag in document.tags if tag not in old_tags
                    ]
                    for tag in tags_to_remove:
                        document.tags.remove(tag)

                session.add(document)
                await session.commit()
            raise e

        if file is not None:
            response = await self._agent_create_document(document)
        elif hash_ is not NOT_PROVIDED:
            response = await self._agent_restore_document(document)
        elif updated_values != {}:
            response = await self._agent_update_document(document, updated_values)
        else:
            response = None

        if response is not None:
            document = await self._agent_response_handler(response, document)
            if old_document.document_status != document.document_status:
                updated_values["document_status"] = (
                    old_document.document_status,
                    document.document_status,
                )

        return UpdateResult(
            success=True,
            updated_instance=document,
            updated_values=updated_values,
        )

    @log_decorator()
    async def delete_document(
        self, document_id: uuid.UUID, user_id: uuid.UUID, draft: bool = False
    ) -> DeletionResult:
        """Delete a document from the database based on document_id.

        User_id is required to update the last_modified_by_id field.
        Note! This operation is soft delete,
        the document is not removed from the storage.

        Args:
            document_id: The ID of the document to delete.
            user_id: The ID of the user deleting the document.
            draft: If the document should be deleted as a draft. Default is False.

        Returns:
            DeletionResult: The result of the deletion operation.

        """
        await self._check_user(user_id)
        start = start_time_var.get(datetime.now(timezone.utc))
        deleted_document = await self.get_document(document_id)
        if not deleted_document:
            msg = f"Document with id {document_id} not found"
            error_event_log(
                msg=msg,
                start=start,
                error=ValueError(msg),
                logger=self.logger,
            )
            return DeletionResult(success=False, error=msg)
        elif deleted_document.deleted_date:
            msg = f"Document with id {document_id} already deleted"
            error_event_log(
                msg=msg,
                start=start,
                error=ValueError(msg),
                logger=self.logger,
            )
            return DeletionResult(success=False, error=msg)
        else:
            # Only pass the document_id to _delete_document
            deleted_document = await self._delete_document(
                document_id, user_id=user_id, draft=draft
            )

            response = await self._agent_delete_document(deleted_document)
            deleted_document = await self._agent_response_handler(
                response, deleted_document
            )

            return DeletionResult(success=True, deleted_instance=deleted_document)

    @log_decorator()
    async def restore_document(
        self, document_id: uuid.UUID, user_id: uuid.UUID, draft: bool = False
    ) -> UpdateResult:
        """Restore a document from the database based on document_id.

        User_id is required to update the last_modified_by_id field.

        Args:
            document_id: The ID of the document to restore.
            user_id: The ID of the user undeleting the document.
            draft: If the document should be restored as a draft. Default is False.

        Returns:
            UpdateResult: The result of the restore operation.

        """
        await self._check_user(user_id)
        start = start_time_var.get(datetime.now(timezone.utc))
        restored_document = await self.get_document(document_id)
        if not restored_document:
            msg = f"Document with id {document_id} not found"
            error_event_log(
                msg=msg,
                start=start,
                error=ValueError(msg),
                logger=self.logger,
            )
            return UpdateResult(success=False, error=msg)
        elif restored_document.deleted_date is None:
            msg = f"Document with id {document_id} is not deleted"
            error_event_log(
                msg=msg,
                start=start,
                error=ValueError(msg),
                logger=self.logger,
            )
            return UpdateResult(success=False, error=msg)
        else:
            updated_values = {"deleted_date": (restored_document.deleted_date, None)}
            if draft is not None and draft != restored_document.draft:
                updated_values["draft"] = (
                    restored_document.draft,
                    draft,
                )
                restored_document.draft = draft
            restored_document = await self._restore_document(
                document_id=document_id,
                user_id=user_id,
                draft=draft,
            )

            # recreate document in the VectorStore
            response = await self._agent_create_document(restored_document)
            restored_document = await self._agent_response_handler(
                response, restored_document
            )

            return UpdateResult(
                success=True,
                updated_instance=restored_document,
                updated_values=updated_values,
            )

    @log_decorator(type_="access", category=["database", "file"])
    async def download_document(
        self, document_id: uuid.UUID
    ) -> tuple[NamedFileLike, str]:
        """Download a document from the storage based on document_id.

        Args:
            document_id: The ID of the document to download.

        Returns:
            File and the filename

        """
        async for session in get_async_session():
            query = select(Document).where(Document.id == document_id)
            document = (await session.execute(query)).scalars().first()
        folder_path = await self.folder_service.get_folder_path(document.folder_id)
        file = self.storage_connector.download_file(
            folder_path=folder_path,
            filename=self._create_storage_name(document.filename, document.hash),
        )

        return file, document.filename

    @log_decorator(type_="access", category=["database", "file"])
    async def get_presigned_url_download(self, document_id: uuid.UUID) -> str:
        """Get a presigned URL for downloading a document by document_id.

        Args:
            document_id: The ID of the document to download.

        Returns:
            str: Presigned URL for downloading the document.

        """
        async for session in get_async_session():
            query = select(Document).where(Document.id == document_id)
            document = (await session.execute(query)).scalars().first()
        folder_path = await self.folder_service.get_folder_path(document.folder_id)
        url = self.storage_connector.generate_presigned_url(
            folder_path=folder_path,
            filename=self._create_storage_name(document.filename, document.hash),
        )

        return url

    @log_decorator(category=["database", "file"])
    async def cleanup_deleted_documents(self) -> tuple[list[Document], list[Document]]:
        """Remove documents with deleted_date before now from DB and storage.

        Returns:
            (tuple[list[Document], list[Document]]): A tuple of deleted and not
                deleted documents.

        """
        async for session in get_async_session():
            query = select(Document).where(Document.deleted_date < datetime.now())
            deleted_documents = (await session.execute(query)).scalars().all()
            deleted_docs = []
            not_deleted_docs = []
            for document in deleted_documents:
                flg = True

                # delete from storage
                try:
                    folder_path = await self.folder_service.get_folder_path(
                        document.folder_id
                    )
                    self.storage_connector.delete_file(
                        folder_path=folder_path,
                        filename=self._create_storage_name(
                            document.filename, document.hash
                        ),
                    )
                except Exception:
                    not_deleted_docs.append(document.model_copy())
                    flg = False

                if flg:
                    # clean relations with tags
                    stmt = delete(DocumentTags)
                    stmt = stmt.where(DocumentTags.document_id == document.id)
                    await session.execute(stmt)

                    # delete from db
                    stmt = delete(Document).where(Document.id == document.id)
                    deleted_doc = (
                        await session.execute(stmt.returning(Document))
                    ).scalar_one_or_none()
                    deleted_docs.append(deleted_doc.model_copy())

            await session.commit()

            return (deleted_docs, not_deleted_docs)

    @log_decorator(type_="access")
    async def get_ingestions_stats(
        self,
        ingestion_ids: list[uuid.UUID] | None = None,
        ingestion_descriptions: list[str] | None = None,
    ) -> dict[uuid.UUID, dict[str, int]]:
        """Get statistics for ingestions by their IDs."""
        async for session in get_async_session():
            query = (
                select(
                    Document.ingestion_id,
                    func.count().filter(
                        col(Document.document_status).in_(
                            [
                                "Pending",
                                "Syncing Storage",
                                "Pending VectorDB",
                                "Syncing VectorDB",
                            ]
                        )
                    ),
                    func.count().filter(col(Document.document_status).in_(["Error"])),
                    func.count().filter(col(Document.document_status).in_(["Synced"])),
                )
                .where(
                    Document.ingestion_id != DEFAULT_INGESTION.id
                )  # to remove master documents
                .group_by(Document.ingestion_id)
            )
            if ingestion_ids:
                query = query.where(col(Document.ingestion_id).in_(ingestion_ids))
            if ingestion_descriptions:
                query = query.join(Document.ingestion).where(
                    col(Ingestion.description).in_(ingestion_descriptions)
                )
            result = (await session.execute(query)).all()
            statuses = ["Syncing", "Sync error", "Synced"]
            result_dict = {
                item[0]: dict(zip(statuses, item[1:], strict=False)) for item in result
            }
            return result_dict

    def _get_filename(self, file: NamedFileLike) -> str:
        if isinstance(file, UploadFile):
            return file.filename
        elif isinstance(file, IOBase):
            return os.path.basename(file.name)
        else:
            raise ValueError(f"Unsupported file type {type(file)}")

    def _get_filesize(self, file: NamedFileLike) -> int:
        if isinstance(file, UploadFile):
            if file.size is not None:
                return file.size
            else:
                file.file.seek(0, 2)
                size = file.file.tell()
                file.file.seek(0)
                return size
        elif isinstance(file, BytesIO):
            return len(file.getbuffer())
        elif isinstance(file, IOBase):
            return os.fstat(file.fileno()).st_size
        else:
            raise ValueError(f"Unsupported file type {type(file)}")

    def _pass_file(self, file: NamedFileLike) -> IOBase:
        if isinstance(file, UploadFile):
            return file.file
        elif isinstance(file, IOBase):
            return file
        else:
            raise ValueError(f"Unsupported file type {type(file)}")

    async def _agent_create_document(self, document: Document) -> httpx.Response:
        async for session in get_async_session():
            # Agent meta collecting and sending
            query = (
                select(Source)
                .join(Ingestion, Ingestion.source_id == Source.id)
                .where(Ingestion.id == document.ingestion_id)
            )
            source = (await session.execute(query)).scalars().first()

        metadata = document.model_dump(mode="json")
        metadata["source_name"] = source.name
        payload = {"docs": [metadata]}

        async with httpx.AsyncClient(timeout=httpx.Timeout(60.0)) as client:
            response = await client.post(
                url=self.agent_url_prefix + "/vector-store/create",
                json=payload,
            )

        return response

    async def _agent_update_document(
        self,
        document: Document,
        updated_values: dict[str, tuple[Any, Any]],
    ):
        payload = {}
        payload["id"] = str(document.id)
        payload["doc_metadata"] = updated_values

        async with httpx.AsyncClient() as client:
            response = await client.patch(
                url=self.agent_url_prefix + "/vector-store/update",
                json=payload,
            )

        return response

    async def _agent_delete_document(self, document: Document):
        payload = {}
        payload["doc_id"] = str(document.id)
        payload["draft"] = document.draft

        async with httpx.AsyncClient() as client:
            response = await client.request(
                "DELETE",
                url=self.agent_url_prefix + "/vector-store/delete",
                json=payload,
            )

        return response

    # async def _agent_draft_document(self, document: Document):
    #     payload = {"doc_ids": [str(document.id)]}

    #     async with httpx.AsyncClient() as client:
    #         response = await client.post(
    #             url=self.agent_url_prefix + "/vector-store/draft",
    #             json=payload,
    #         )

    #     return response

    async def _agent_publish_document(self, document: Document):
        payload = {"doc_ids": [str(document.id)]}

        async with httpx.AsyncClient() as client:
            response = await client.post(
                url=self.agent_url_prefix + "/vector-store/publish",
                json=payload,
            )

        return response

    async def _agent_restore_document(self, document: Document):
        payload = {"doc_ids": [str(document.id)]}

        async with httpx.AsyncClient() as client:
            response = await client.post(
                url=self.agent_url_prefix + "/vector-store/restore",
                json=payload,
            )

        return response

    async def _agent_response_handler(
        self, response: httpx.Response, document: Document
    ) -> Document:
        if response.status_code == 204:
            document = (
                await self.update_document_status(
                    document_id=document.id,
                    user_id=document.created_by_id,
                    status="Syncing VectorDB",
                )
            ).updated_instance
        else:
            document = (
                await self.update_document_status(
                    document_id=document.id,
                    user_id=document.created_by_id,
                    status="Error",
                )
            ).updated_instance

        return document

    def _create_storage_name(
        self,
        filename: str,
        hash_: int,
    ) -> str:
        """Create a storage name for the document.

        Args:
            filename (str): The name of the file.
            hash_ (int): The hash of the file.

        Returns:
            str: The storage name for the document.

        """
        name, ext = filename.rsplit(".", 1)
        return f"{name}_{hash_}.{ext}"

    async def _check_user(self, user_id: uuid.UUID) -> User:
        """Check if the user exists in the database.

        Args:
            user_id (uuid.UUID): The ID of the user.

        Returns:
            User: The user object if found.
            Raises ValueError if the user is not found.

        """
        start = datetime.now(timezone.utc)
        user = await self.user_service.get_user(user_id)
        if not user:
            msg = f"User with id {user_id} not found"
            error_event_log(
                msg=msg,
                start=start,
                error=ValueError(msg),
                logger=self.logger,
            )
            raise ValueError(msg)
        return user

    async def _check_ingestion(self, ingestion_id: uuid.UUID) -> Ingestion:
        """Check if the ingestion exists in the database.

        Args:
            ingestion_id (uuid.UUID): The ID of the ingestion.

        Returns:
            Ingestion: The ingestion object if found.
            Raises ValueError if the ingestion is not found.

        """
        start = datetime.now(timezone.utc)
        ingestion = await self.ingestion_service.get_ingestion(ingestion_id)
        if not ingestion:
            msg = f"Ingestion with id {ingestion_id} not found"
            error_event_log(
                msg=msg,
                start=start,
                error=ValueError(msg),
                logger=self.logger,
            )
            raise ValueError(msg)
        return ingestion

    async def _check_tag(self, tag: str) -> Tag:
        """Check if the tag exists in the database.

        Args:
            tag (str): The name of the tag.

        Returns:
            Tag: The tag object if found.
            Raises ValueError if the tag is not found.

        """
        start = datetime.now(timezone.utc)
        tag = await self.tag_service.search_tags(tag_name=tag)
        if not tag:
            msg = f"Tag with name {tag} not found"
            error_event_log(
                msg=msg,
                start=start,
                error=ValueError(msg),
                logger=self.logger,
            )
            raise ValueError(msg)
        return tag[0]

    async def _check_document(self, document: Document) -> CheckDocumentResult:
        """Check if the document exists in the database by filename and folder_id.

        Args:
            document (Document): The document to check.

        Returns:
            CheckDocumentResult: The result of the check operation.

        """
        docs = await self.search_documents(
            filename=document.filename,
            folder_id=document.folder_id,
            search_deleted=True,
            draft=True,
        )
        doc = docs[0] if docs else None
        if doc:
            deleted_flg = doc.deleted_date is not None
            same_hash_flg = doc.hash == document.hash
        else:
            deleted_flg = False
            same_hash_flg = False

        return CheckDocumentResult(
            document=doc, deleted_flg=deleted_flg, same_hash_flg=same_hash_flg
        )

    async def _storage_upload(self, document: Document, file: NamedFileLike) -> None:
        folder_path = await self.folder_service.get_folder_path(document.folder_id)

        storage_filename = self._create_storage_name(document.filename, document.hash)

        self.storage_connector.upload_file(
            folder_path=folder_path,
            filename=storage_filename,
            file=self._pass_file(file),
            overwrite=True,  # cause we might have some old files in storage with same hash in filename # noqa: E501
        )

    async def _create_document(self, document: Document) -> Document:
        """Lightweight helper method to create a new document in the database.

        Args:
            document (Document): The document to create.

        Returns:
            Document: The created document.

        """
        async for session in get_async_session():
            document_copy = deepcopy(document)
            session.add(document)
            await session.commit()
            return document_copy

    async def _delete_document(
        self, document_id: uuid.UUID, user_id: uuid.UUID, draft: bool = False
    ) -> Document:
        """Lightweight helper method to soft delete a document from the database.

        Args:
            document_id: The ID of the document to delete.
            user_id (uuid.UUID): The ID of the user deleting the document.
            draft: If the document should be deleted as a draft. Default is False.

        Returns:
            Document: The deleted document.

        """
        async for session in get_async_session():
            document = await session.get(Document, document_id)
            if not isinstance(document, Document):
                raise ValueError(f"Document with id {document_id} not found")
            document.deleted_date = datetime.now()
            document.last_modified_by_id = user_id
            document.draft = draft
            session.add(document)
            deleted_document = deepcopy(document)
            await session.commit()
            return deleted_document

    async def _restore_document(
        self, document_id: uuid.UUID, user_id: uuid.UUID, draft: bool = False
    ) -> Document:
        """Lightweight helper method to restore a document in the database.

        Args:
            document_id: The ID of the document to restore.
            user_id (uuid.UUID): The ID of the user restoring the document.
            draft: If the document should be restored as a draft. Default is False.

        Returns:
            Document: The restored document.

        """
        async for session in get_async_session():
            document = await session.get(Document, document_id)
            if not isinstance(document, Document):
                raise ValueError(f"Document with id {document_id} not found")
            document.deleted_date = None
            document.last_modified_by_id = user_id
            document.draft = draft
            session.add(document)
            restored_document = deepcopy(document)
            await session.commit()
            return restored_document

    def _document_fields_comparison(
        self, existing_doc: Document, new_doc: Document, fields_to_compare: list[str]
    ) -> dict:
        new_values = {}
        for field in fields_to_compare:
            if getattr(existing_doc, field) != getattr(new_doc, field):
                new_values[field] = getattr(new_doc, field)
        return new_values
