"""Agent manager client."""

import httpx

from gen_os_sdk_emulator.agent_manager_client.common.auth_provider import AuthProvider
from gen_os_sdk_emulator.agent_manager_client.common.base_client import BaseClient
from gen_os_sdk_emulator.agent_manager_client.gen_os_am_knowledge.client import (
    KnowledgeManagementClient,
)
from gen_os_sdk_emulator.agent_manager_client.gen_os_am_workflows.client import (
    CaseManagementClient,
)


class AgentManagerClient(BaseClient):
    """Agent manager client."""

    def __init__(
        self,
        auth_provider: AuthProvider,
        service_url: str,
    ):
        """Initialize the agent manager client.

        Args:
            auth_provider: The authentication provider to use
            service_url: The service URL to use

        """
        super().__init__(auth_provider)
        self.service_url = service_url
        self._aclient = httpx.AsyncClient()

        self.cm = CaseManagementClient(auth_provider, f"{self.service_url}/cm")
        self.km = KnowledgeManagementClient(auth_provider, f"{self.service_url}/km")

    async def get_agent_manager_status(
        self, trace_id: str | None = None, app_caller: str | None = None
    ) -> dict:
        """Get the status of the agent manager."""
        path = "/am/status"
        headers = await self._handle_headers(trace_id=trace_id, app_caller=app_caller)
        result = await self._aclient.get(f"{self.service_url}{path}", headers=headers)
        result.raise_for_status()
        return result.json()
