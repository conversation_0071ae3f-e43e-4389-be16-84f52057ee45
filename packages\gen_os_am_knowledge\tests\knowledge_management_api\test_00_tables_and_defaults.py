"""Test the tables and defaults."""

import pytest
from sqlalchemy import inspect
from sqlmodel import select

# Use KM's new SessionManager
from gen_os_am_knowledge.config.database import SessionManager
from gen_os_am_knowledge.sdk.models import (
    Document,
    DocumentTags,
    Folder,
    Ingestion,
    Source,
    Tag,
    User,
)
from gen_os_am_knowledge.sdk.models.common import (
    DEFAULT_FOLDER,
    DEFAULT_INGESTION,
    DEFAULT_SOURCE,
    DEFAULT_USER,
)


def test_database_setup(setup_database):
    """Test to ensure the database setup was successful."""
    # The setup_database fixture handles table creation and default data
    with SessionManager.get_sync_session() as session:
        # Verify that default Folder exists
        stmt = select(Folder).where(Folder.id == DEFAULT_FOLDER.id)
        result = session.exec(stmt).first()
        assert result is not None, "Default Folder not found in the database"

        # Verify that default Source exists
        stmt = select(Source).where(Source.id == DEFAULT_SOURCE.id)
        result = session.exec(stmt).first()
        assert result is not None, "Default Source not found in the database"

        # Verify that default Ingestion exists
        stmt = select(Ingestion).where(Ingestion.id == DEFAULT_INGESTION.id)
        result = session.exec(stmt).first()
        assert result is not None, "Default Ingestion not found in the database"

        # Verify that default User exists
        stmt = select(User).where(User.id == DEFAULT_USER.id)
        result = session.exec(stmt).first()
        assert result is not None, "Default User not found in the database"

        with session.connection() as conn:
            inspector = inspect(conn)

            # Get a list of all tables in the database
            tables = inspector.get_table_names()

            assert "document" in tables
            assert "tag" in tables
            assert "document_tags" in tables
            assert "ingestion" in tables
            assert "user" in tables
            assert "source" in tables
            assert "folder" in tables
