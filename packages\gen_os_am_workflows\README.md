# Gen OS Case Management Backend

## Run locally

### Docker

You can run the CM service using Docker with a single command:

```bash
pdm run cm-docker
```

This command will:

1. Configure the environment for Docker
2. Build and start all containers

To stop and remove the containers:

```bash
docker compose down
```

### Source

> Make sure you've created a fresh environment and installed the dependencies using `pdm`.

To run the service locally:

```bash
pdm run cm-source
```

This command will:

1. Start the local Postgres database
2. Run database migrations
3. Configure the environment for local development
4. Start the API server

To stop the database:

```bash
pdm run stop-database
```

## Mock Data

The `src/gen_os_am_workflows/database/mock_data` directory contains test data files used for development and testing:

- `test_data.sql`: SQL scripts with sample data for the database
- `test_wf_read_documents.yml`: Sample workflow configuration for document reading tests
- `test_wf_mermaid.md`: Mermaid diagram examples for workflow visualization

To populate the database with mock data:

```bash
pdm run populate-with-mock
```

> Note: Make sure the database is running before populating it with mock data. You can use this command after running either `cm-docker` or `cm-source` - **for both you'll need to change the `DB_HOST` to `localhost` on the `.env` file**.

## Setup Cloud Provider access

### GCP

Refer to [GCP docs](https://cloud.google.com/storage/docs/authentication#client-libs) to setup the default credentials.

Shortcut:

1. `gcloud auth login`
2. `gcloud config set project YOUR-PROJECT`
3. `gcloud auth application-default login`

#### Create a service account

To test certain gcloud endpoints you might need to setup a service account:

1. Create service account

    ```bash
        gcloud iam service-accounts create YOUR-SERVICE-ACCOUNT-NAME \
        --description="YOUR-DESCRIPTION" \
        --display-name="YOUR-SERVICE-ACCOUNT-DISPLAY-NAME"
    ```

2. Grant required IAM permissions

    ```bash
        gcloud iam service-accounts add-iam-policy-binding \
        TARGET_SERVICE_ACCOUNT_EMAIL \
        --member="user:YOUR-USER-EMAIL" \
        --role="roles/iam.serviceAccountTokenCreator"
    ```

3. Grant service account permission to write inside the bucket

    ```bash
        gsutil iam ch serviceAccount:YOUR-SERVICE-ACCOUNT-EMAIL:roles/storage.objectCreator gs://YOUR-BUCKET-NAME
    ```

#### Testing utilities

1. Uploading a file manually to a bucket trough `curl`:

    ```bash
        curl -X PUT -H "Content-Type: text/plain" \
        --upload-file test.txt \
        "THE-PRESIGNED-UPLOAD-URL"
    ```

2. Download a file manually from a bucket trough `curl`:

    ```bash
        curl -X PUT -H "Content-Type: text/plain" \
        --upload-file test.txt \
        "THE-PRESIGNED-UPLOAD-URL"
    ```
