"""Database module for the case management system.

This module contains the database models and session manager for the case management system.
"""

from gen_os_am_workflows.database.models import (
    Base,
    File,
    Interaction,
    InteractionKind,
    InteractionKindApproved,
    InteractionKindCancelled,
    InteractionKindEdited,
    InteractionKindResumed,
    InteractionKindSolveManually,
    Occurrence,
    OccurrenceReasonAssistanceNeeded,
    OccurrenceReasonEditPreviousStep,
    OccurrenceReasonError,
    OccurrenceReasonPendingApproval,
    OccurrenceReasons,
    OccurrenceReasonSolveManually,
    OccurrenceStatus,
    OccurrenceStatusOpen,
    OccurrenceStatusSolved,
    OccurrenceStatusUnsolved,
    Step,
    StepInput,
    StepOutput,
    StepRun,
    StepRunStatus,
    StepRunStatusBlockedAssistanceNeeded,
    StepRunStatusBlockedPendingApproval,
    StepRunStatusCancelled,
    StepRunStatusCompleted,
    StepRunStatusError,
    StepRunStatusRunning,
    StepRunTerminalStatuses,
    Workflow,
    WorkflowExecution,
    WorkflowExecutionHistory,
    WorkflowExecutionHistoryEvent,
    WorkflowExecutionHistoryEventApproved,
    WorkflowExecutionHistoryEventBlockedAssistanceNeeded,
    WorkflowExecutionHistoryEventBlockedPendingApproval,
    WorkflowExecutionHistoryEventCancelled,
    WorkflowExecutionHistoryEventCompleted,
    WorkflowExecutionHistoryEventEdited,
    WorkflowExecutionHistoryEventError,
    WorkflowExecutionHistoryEventExecutionStarted,
    WorkflowExecutionHistoryEventResumed,
    WorkflowExecutionStatus,
    WorkflowExecutionStatusBlocked,
    WorkflowExecutionStatusBlockedAssistanceNeeded,
    WorkflowExecutionStatusBlockedPendingApproval,
    WorkflowExecutionStatusCancelled,
    WorkflowExecutionStatusCompleted,
    WorkflowExecutionStatusError,
    WorkflowExecutionStatusRunning,
)
from gen_os_am_workflows.database.session import AsyncSession, SessionManager

__all__ = [
    "Base",
    "File",
    "Interaction",
    "InteractionKind",
    "InteractionKindApproved",
    "InteractionKindCancelled",
    "InteractionKindEdited",
    "InteractionKindResumed",
    "InteractionKindSolveManually",
    "Occurrence",
    "OccurrenceReasonEditPreviousStep",
    "OccurrenceStatus",
    "OccurrenceStatusOpen",
    "OccurrenceStatusSolved",
    "OccurrenceStatusUnsolved",
    "OccurrenceReasons",
    "OccurrenceReasonPendingApproval",
    "OccurrenceReasonAssistanceNeeded",
    "OccurrenceReasonError",
    "OccurrenceReasonSolveManually",
    "Step",
    "StepInput",
    "StepOutput",
    "StepRun",
    "StepRunStatus",
    "StepRunStatusBlockedAssistanceNeeded",
    "StepRunStatusBlockedPendingApproval",
    "StepRunStatusCancelled",
    "StepRunStatusCompleted",
    "StepRunStatusError",
    "StepRunStatusRunning",
    "StepRunTerminalStatuses",
    "Workflow",
    "WorkflowExecution",
    "WorkflowExecutionStatus",
    "WorkflowExecutionStatusBlockedAssistanceNeeded",
    "WorkflowExecutionStatusBlockedPendingApproval",
    "WorkflowExecutionStatusCancelled",
    "WorkflowExecutionStatusCompleted",
    "WorkflowExecutionStatusError",
    "WorkflowExecutionStatusRunning",
    "SessionManager",
    "AsyncSession",
    "WorkflowExecutionHistory",
    "WorkflowExecutionHistoryEvent",
    "WorkflowExecutionHistoryEventExecutionStarted",
    "WorkflowExecutionHistoryEventCompleted",
    "WorkflowExecutionHistoryEventError",
    "WorkflowExecutionHistoryEventBlockedAssistanceNeeded",
    "WorkflowExecutionHistoryEventBlockedPendingApproval",
    "WorkflowExecutionHistoryEventApproved",
    "WorkflowExecutionHistoryEventEdited",
    "WorkflowExecutionHistoryEventCancelled",
    "WorkflowExecutionHistoryEventResumed",
    "WorkflowExecutionStatusBlocked",
]

# Register events
import gen_os_am_workflows.database.events  # noqa: F401
