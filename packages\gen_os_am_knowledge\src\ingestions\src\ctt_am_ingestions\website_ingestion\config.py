"""Website ingestion configuration."""

from functools import cache
from logging import Logger
from pathlib import Path

from gen_os_am_knowledge.config.settings import LogSettings

BASE_DIR = Path(__file__).resolve().parent
WEBSITE_DATA_PATH = BASE_DIR / "data" / "urls.xlsx"


@cache
def get_website_ingestion_logger() -> Logger:
    """Get the website ingestion logger."""
    from gen_os_am_knowledge.utils.ctt.ctt_utils.logging.ctt_log_formatter import (
        CTTFormatter,
    )
    from gen_os_am_knowledge.utils.logging.log_init import init_logger

    return init_logger(
        name="website_ingestion",
        formatter=CTTFormatter(),
        log_level=LogSettings.get_settings().LOG_LEVEL,
    )
