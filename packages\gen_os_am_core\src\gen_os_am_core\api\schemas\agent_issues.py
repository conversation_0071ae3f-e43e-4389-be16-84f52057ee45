"""Pydantic schemas for agent issues management API."""

import uuid
from datetime import datetime
from typing import Literal

from pydantic import (
    BaseModel,
    ConfigDict,
    Field,
    model_validator,
)

from gen_os_am_core.models.enums import IssueCategory, IssueState


class ReportedIncidentConversationBase(BaseModel):
    """Base schema for ReportedIncidentConversation shared fields."""

    model_config = ConfigDict(
        from_attributes=True,
        populate_by_name=True,
    )

    conversation_id: str = Field(..., description="Conversation identifier")
    answer: str | None = Field(None, description="Answer provided in the conversation")
    description: str | None = Field(
        None, description="Detailed description of the incident"
    )
    severity: str = Field(..., description="Severity level e.g. low, medium, critical")


class ReportedIncidentConversationCreate(ReportedIncidentConversationBase):
    """Schema for creating a ReportedIncidentConversation (client request)."""

    pass


class ReportedIncidentConversationResponse(ReportedIncidentConversationBase):
    """Schema for returning ReportedIncidentConversation data."""

    id: uuid.UUID = Field(..., description="Unique identifier of the incident entry")
    issue_id: uuid.UUID = Field(..., description="Associated Issue identifier")
    created_by: str | None = Field(None, description="User who created this entry")
    created_at: datetime = Field(..., description="Timestamp of creation")


class ReportedIncidentWorkflowBase(BaseModel):
    """Base schema for ReportedIncidentWorkflow shared fields."""

    model_config = ConfigDict(
        from_attributes=True,
        populate_by_name=True,
    )

    workflow_execution_id: str = Field(..., description="Workflow execution identifier")
    step: str | None = Field(None, description="Step within the workflow")
    description: str | None = Field(
        None, description="Detailed description of the incident"
    )
    severity: str = Field(..., description="Severity level e.g. low, medium, critical")


class ReportedIncidentWorkflowCreate(ReportedIncidentWorkflowBase):
    """Schema for creating a ReportedIncidentWorkflow (client request)."""

    pass


class ReportedIncidentWorkflowResponse(ReportedIncidentWorkflowBase):
    """Schema for returning ReportedIncidentWorkflow data."""

    id: uuid.UUID = Field(..., description="Unique identifier of the incident entry")
    issue_id: uuid.UUID = Field(..., description="Associated Issue identifier")
    created_by: str | None = Field(None, description="User who created this entry")
    created_at: datetime = Field(..., description="Timestamp of creation")


class IssueCreateRequest(BaseModel):
    """Schema for creating a new Issue with related incidents."""

    model_config = ConfigDict(
        from_attributes=True,
        populate_by_name=True,
    )

    description: str = Field(..., description="Issue description")
    issue_type: IssueCategory = Field(
        ..., description="Predefined symptom/category of the issue"
    )
    state: IssueState = Field(..., description="Initial state of the issue")
    reported_incidents_workflows: list[ReportedIncidentWorkflowCreate] = Field(
        default_factory=list,
        description="Incidents related to workflow executions",
    )
    reported_incidents_conversations: list[ReportedIncidentConversationCreate] = Field(
        default_factory=list,
        description="Incidents related to conversations",
    )


class IssueResponse(BaseModel):
    """Schema for returning Issue data with nested incidents."""

    model_config = ConfigDict(
        from_attributes=True,
        populate_by_name=True,
    )

    id: uuid.UUID = Field(..., description="Unique identifier of the issue")
    agent_id: str = Field(..., description="Agent identifier")
    description: str = Field(..., description="Issue description")
    issue_type: IssueCategory = Field(
        ..., description="Predefined symptom/category of the issue"
    )
    state: IssueState = Field(..., description="Current state of the issue")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    reported_incidents_workflows: list[ReportedIncidentWorkflowResponse] = Field(
        default_factory=list,
        description="Incidents related to workflow executions",
    )
    reported_incidents_conversations: list[ReportedIncidentConversationResponse] = (
        Field(
            default_factory=list,
            description="Incidents related to conversations",
        )
    )


class ReportedIncidentConversationUnified(BaseModel):
    """Schema for a conversation incident in the issue detail view."""

    id: uuid.UUID
    kind: Literal["conversation"]
    description: str | None
    severity: str
    created_by: str | None
    created_at: datetime
    conversation_id: str | None = None


class ReportedIncidentWorkflowUnified(BaseModel):
    """Schema for a workflow incident in the issue detail view."""

    id: uuid.UUID
    kind: Literal["workflow"]
    description: str | None
    severity: str
    created_by: str | None
    created_at: datetime
    workflow_execution_id: str | None = None


class IssueWithIncidents(BaseModel):
    """Detailed issue response including all related incidents."""

    model_config = ConfigDict(from_attributes=True)

    id: uuid.UUID
    description: str
    issue_type: IssueCategory
    state: IssueState
    close_desc: str | None = None
    agent: str
    created_at: datetime
    updated_at: datetime
    reported_incidents: list[
        ReportedIncidentConversationUnified | ReportedIncidentWorkflowUnified
    ] = Field(default_factory=list)


class IncidentSoftDeleteRequest(BaseModel):
    """Schema for soft deleting (or restoring) an incident entry."""

    is_deleted: bool = Field(
        ..., description="Whether the incident is deleted (true) or restored (false)"
    )
    deleted_by: str = Field(
        ..., description="Identifier of the user performing the delete/restore action"
    )


class IncidentSoftDeleteResponse(BaseModel):
    """Returned payload after soft deleting/restoring an incident."""

    id: uuid.UUID
    is_deleted: bool
    deleted_at: datetime | None
    deleted_by: str | None
    kind: Literal["conversation", "workflow"]


class IssueUpdateRequest(BaseModel):
    """Schema for updating an issue via PATCH endpoint."""

    model_config = ConfigDict(
        from_attributes=True,
        populate_by_name=True,
    )

    description: str | None = Field(None, description="Updated issue description")
    issue_type: IssueCategory | None = Field(
        None, description="Updated issue type/symptom"
    )
    state: IssueState | None = Field(None, description="Updated issue state")
    close_desc: str | None = Field(None, description="Description when closing issue")
    deleted: bool | None = Field(None, description="Flag to soft delete the issue")

    @model_validator(mode="after")
    def validate_close_desc_for_closing_state(self):
        """Validate that close_desc is provided when state is set to a closing value."""
        if self.state in ["closed_fixed", "closed_wont_fix"] and not self.close_desc:
            raise ValueError("close_desc is required when closing an issue")
        return self


class IssueUpdateResponse(BaseModel):
    """Schema for returning updated issue data."""

    model_config = ConfigDict(
        from_attributes=True,
        populate_by_name=True,
    )

    id: uuid.UUID = Field(..., description="Unique identifier of the issue")
    agent_id: str = Field(..., description="Agent identifier")
    description: str = Field(..., description="Issue description")
    issue_type: IssueCategory = Field(..., description="Issue type/symptom")
    state: IssueState = Field(..., description="Current state of the issue")
    close_desc: str | None = Field(None, description="Description when closing issue")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp") 