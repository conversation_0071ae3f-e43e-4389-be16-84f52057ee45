[project]
name = "gen-os-am-core"
version = "0.1.0"
description = "The Agent Manager Library is intended to manage services such as KM and  CM in an abstract way. Those services can then be used off the shelf and further customized to the developer needs."
authors = [
    {name = "DareData", email = "<EMAIL>"},
]
requires-python = ">=3.12"
readme = "README.md"
license = {text = "Proprietary"}
dependencies = [
    "pydantic>=2.10.5",
    "pydantic-settings>=2.5.2",
    "fastapi>=0.115.8",
    "python-multipart>=0.0.20",
    "protobuf<6.0,>=5.20",
    "a2a-sdk>=0.2.9",
    "alembic>=1.15.2",
    "sqlalchemy>=2.0.38",
    "psycopg>=3.2.3",
    "httpx>=0.28.1",
    "aiosqlite>=0.21.0",
]

[build-system]
requires = ["pdm-backend"]
build-backend = "pdm.backend"

[dependency-groups]
# dev dependencies that won't appear in the package distribution metadata
test = [
    "pytest<9,>=8.3.3",
    "pytest-cov<7.0.0,>=6.0.0",
    "pytest-asyncio>=0.26.0",
]
dev = [
    "pre-commit<=4.0.0,>=3.8.0",
    "isort<6.0.0,>=5.13.2",
    "ipykernel<=7.0.0,>=6.29.5",
    "honcho>=2.0.0",
    "ruff>=0.7.3",
    "python-dotenv>=1.0.0",
]
doc = ["sphinx<9.0.0,>=8.0.0"]

[tool.pdm.dev-dependencies]
gen-os = [
    "-e file:///${PROJECT_ROOT}/../gen_os_sdk_emulator#egg=gen-os-sdk-emulator",
    "-e file:///${PROJECT_ROOT}/../gen_os_am_conversational#egg=gen-os-am-conversational",
    "-e file:///${PROJECT_ROOT}/../gen_os_am_workflows#egg=gen-os-am-workflows",
    "-e file:///${PROJECT_ROOT}/../gen_os_am_core#egg=gen-os-am-core",
    "-e file:///${PROJECT_ROOT}/../gen_os_am_knowledge/src/ingestions#egg=ctt-am-ingestions",
    "-e file:///${PROJECT_ROOT}/../gen_os_am_knowledge#egg=gen-os-am-knowledge",
]


[tool.pytest.ini_options]
asyncio_default_fixture_loop_scope = "session"

[tool.ruff.lint]
select=[
    "I",   # isort
    "A",   # flake8-builtins
    "N",   # pep8-naming
    "D",   # pydocstyle
    "E",   # pycodestyle (Error)
    "YTT", # flake8-2020
    "B",   # flake8-bugbear
    "T10", # flake8-debugger
    "T20", # flake8-print
    "C4",  # flake8-comprehensions
]

ignore=[
    "D203", #one-blank-line-before-class
    "D213", #multi-line-summary-second-line
    "D104", #package docstrings
    "B008", #do not perform function calls in argument defaults (valid in FastAPI)
]

[tool.pdm]
distribution = true

[tool.pdm.scripts]
# Start local database
start-database = "docker compose -f compose/local.yml up --build -d"
stop-database = "docker compose -f compose/local.yml down"

# Database migrations
cm-migrate = "sh -c 'cd ../gen_os_am_workflows/src && alembic upgrade head'"
km-migrate = "sh -c 'cd ../gen_os_am_knowledge/src && alembic upgrade head'"
conversational-migrate = "sh -c 'cd ../gen_os_am_conversational/src && alembic upgrade head'"
am-migrate = "sh -c 'cd src && alembic upgrade head'"
migrate-all = "sh -c 'pdm run am-migrate && pdm run cm-migrate && pdm run conversational-migrate && pdm run km-migrate'"

# Generate migrations
cm-generate = "sh -c 'cd ../gen_os_am_workflows/src && alembic revision --autogenerate'"
km-generate = "sh -c 'cd ../gen_os_am_knowledge/src && alembic revision --autogenerate'"
conversational-generate = "sh -c 'cd ../gen_os_am_conversational/src && alembic revision --autogenerate'"
am-generate = "sh -c 'cd src && alembic -c alembic.ini revision --autogenerate'"

# Agent Manager (am) migrations - Shell-based approach
am-generate-revision = "sh -c 'cd src && alembic -c alembic.ini revision --autogenerate -m \"$0\"'"
am-migrate-to = "sh -c 'cd src && alembic -c alembic.ini upgrade \"$0\"'"
am-history = "sh -c 'cd src && alembic -c alembic.ini history'"

# run with pdm run precommit
precommit = "sh -c 'pre-commit run --show-diff-on-failure --color=always --files $(git ls-files)'"

# Agent Manager API
source-env = "pdm run python scripts/update_env.py local"
api-source = "uvicorn gen_os_am_core.main:am_app --host 0.0.0.0 --port 8080 --reload"
am-source = {composite = ["source-env", "start-database", "migrate-all", "api-source"]}

docker-env = "pdm run python scripts/update_env.py docker"
api-docker = "docker compose -f ../../docker-compose.yml up --build -d"
am-docker = {composite = ["docker-env", "api-docker"]}

# TODO: We probably can remove the building of the wheels

# Build and move wheel
# We want to copy both the wheel and the tar.gz files so it's available for the docker build context
build-cm = "sh -c 'pdm build -p ../gen_os_am_workflows && cp -r ../gen_os_am_workflows/dist/* shared/lib/'"
build-km = "sh -c 'pdm build -p ../gen_os_am_knowledge && cp -r ../gen_os_am_knowledge/dist/* shared/lib/ && pdm build -p ../gen_os_am_knowledge/src/ingestions && cp -r ../gen_os_am_knowledge/src/ingestions/dist/* shared/lib/'"
build-conversational = "sh -c 'pdm build -p ../gen_os_am_conversational && cp -r ../gen_os_am_conversational/dist/* shared/lib/'"
build-gen-os-sdk-emulator = "sh -c 'pdm build -p ../gen_os_sdk_emulator && cp -r ../gen_os_sdk_emulator/dist/* shared/lib/'"
build-deps = {composite = ["build-cm", "build-km", "build-conversational", "build-gen-os-sdk-emulator"]}
