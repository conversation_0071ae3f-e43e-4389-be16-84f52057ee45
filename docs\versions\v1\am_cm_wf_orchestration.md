# Agent Manager Workflow Orchestration

## Overview

This document outlines the workflow orchestration system in the Agent Manager (AM), which manages multi-step workflow executions that interact with agents via the A2A protocol and handle human interventions through the `gen_os_am_workflows` system.

## Architecture

### Flow Overview

```mermaid
flowchart TD
    Start([WF trigger]) --> FetchWF["[AM] Fetch WF"]
    FetchWF --> StartWF["[AM] Create WF Execution w/ Status Running"]
    StartWF --> SendStep["[AM] Create/Update StepRun¹ w/ Status Running<br/>and Send to Agent"]
    SendStep --> AwaitAgent["[AM] Awaits agent response"]
    AwaitAgent --> AgentResp([Agent response])
    AgentResp --> TaskCompleted{"[AM] Is task completed?"}
    TaskCompleted -->|error/assistance_needed| CreateOcc["[AM] Create Occurrence²"]
    TaskCompleted -->|task_completed| NeedsApproval{"[AM] Does step need<br/>manual approval?"}
    NeedsApproval -->|yes| CreateOcc
    NeedsApproval -->|no| LastStep{"[AM] Is last step?"}
    LastStep -->|yes| MarkComplete["[AM] Mark StepRun as completed³"]
    LastStep -->|no| NextStep["[AM] Proceed to next step⁴"]
    NextStep --> SendStep
    CreateOcc --> AwaitHuman["[AM] Awaits human intervention"]
    AwaitHuman --> HumanInt([Human intervention])
    HumanInt --> SendStep
    MarkComplete --> WFDone(["WF Execution finished"])

    style Start fill:#b2f2bb,stroke:#2f9e44,color:black
    style AgentResp fill:#b2f2bb,stroke:#2f9e44,color:black
    style HumanInt fill:#b2f2bb,stroke:#2f9e44,color:black
    style WFDone fill:#ffc9c9,stroke:#e03131,color:black
    style FetchWF fill:white,stroke:#1971c2,color:black
    style StartWF fill:white,stroke:#1971c2,color:black
    style SendStep fill:white,stroke:#1971c2,color:black
    style AwaitAgent fill:#b19cd9,stroke:#1971c2,stroke-dasharray:5,color:black
    style AwaitHuman fill:#b19cd9,stroke:#1971c2,stroke-dasharray:5,color:black
    style TaskCompleted fill:white,stroke:#1971c2,color:black
    style LastStep fill:white,stroke:#1971c2,color:black
    style MarkComplete fill:white,stroke:#1971c2,color:black
    style NextStep fill:white,stroke:#1971c2,color:black
    style NeedsApproval fill:white,stroke:#1971c2,color:black
    style CreateOcc fill:white,stroke:#1971c2,color:black
```

#### Legend

- Green nodes (🟢): External triggers and responses
- Blue nodes (🔵): Agent Manager (AM) actions
- Red node (🔴): Workflow completion state
- Purple nodes with dotted lines (🟣): Waiting states
- Diamond shapes: Decision points

#### Notes

¹ StepRun: A Step definition + data

² Create Occurrence: CM will mark StepRun and WF Execution with the appropriate status through state change cascade

³ Mark StepRun as completed: CM will mark the WF Execution as completed through state change cascade

⁴ Proceed to next step:

- Mark StepRun as completed
- Fetch next StepRun (AM needs to check if it needs an input from the previous step)

---

### Component Architecture

```mermaid
graph TB
    subgraph "Agent Manager Core"
        WR[Workflow Router<br/>workflows/router.py]
        WO[Workflow Orchestrator<br/>workflows/orchestrator.py]
        SS[State Store<br/>workflows/state_store.py]
    end

    subgraph "External Dependencies"
        A2A[A2A Agent<br/>via a2a-sdk]
        WF[gen_os_am_workflows<br/>Database & State Management]
    end

    subgraph "Human Intervention Flow"
        UI[User Interface]
        WF -->|Create Occurrence| UI
        UI -->|Create Interaction| WF
        WF -->|Webhook Notification| WO
    end

    WR --> WO
    SS --> WO
    WO --> A2A
    WO --> WF
    A2A --> AG[External Agents]

    style WR fill:#e1f5fe
    style WO fill:#f3e5f5
    style A2A fill:#fff3e0
    style WF fill:#e8f5e8
    style UI fill:#fff3e0
```

## Implementation Components

### 1. Workflow Router (`workflows/router.py`)

- **Purpose**: Minimal API layer for workflow triggering
- **Key Endpoints**:
  - `POST /workflows/trigger` - Start new workflow execution
  - `POST /workflows/notifications/interaction` - Receive interaction notifications from `gen_os_am_workflows`

### 2. Workflow Orchestrator (`workflows/orchestrator.py`)

- **Purpose**: Core orchestration logic
- **Responsibilities**:
  - Manage workflow execution lifecycle
  - Handle A2A agent communication
  - Process agent responses
  - Create occurrences for human intervention
  - Handle error scenarios and retries

### 3. State Store (`workflows/state_store.py`)

- **Purpose**: Manages workflow execution state for multi-worker support
- **Implementation Options**:
  - **In-Memory Store**: For single-worker deployments (development)
  - **Database Store**: For multi-worker deployments using existing database (recommended)
  - **Alternative Options**: Other storage backends like Redis can be integrated
- **Configuration**: Set via `WORKFLOW_STATE_STORE` environment variable

### 4. Integration with `gen_os_am_workflows`

- **Purpose**: Authoritative source for workflow data and human interactions
- **Key Integration Points**:
  - Workflow execution creation/management
  - Step run tracking
  - Occurrence creation for human intervention needs
  - Interaction handling through `InteractionStateChangeCascade`

## Human Intervention Flow

The human intervention process follows these steps:

1. **Step Execution Issues**: When a workflow step needs human intervention, the orchestrator creates an **occurrence** via `gen_os_am_workflows`

2. **Human Interaction**: Users create **interactions** through the `gen_os_am_workflows` API:

```bash
POST /workflows/{workflow_name}/workflow-executions/{execution_id}/step-run/{step_run_id}/occurrences/{occurrence_id}/interactions
```

1. **State Change Cascade**: When an interaction is created, `gen_os_am_workflows` runs `InteractionStateChangeCascade` which:
   - Updates occurrence status (solved/unsolved)
   - Updates step run status (running/completed/cancelled)
   - Updates workflow execution status (running/completed/cancelled)

2. **Orchestrator Notification**: `gen_os_am_workflows` notifies the orchestrator through webhook

3. **Execution Resume**: Based on the interaction type, the orchestrator resumes or cancels execution

### Interaction State Mapping

| Interaction Kind | Effect on Workflow | Orchestrator Action |
|------------------|-------------------|-------------------|
| `approved` | Occurrence → solved, Step → completed/running, Execution → running/completed | Move to next step |
| `resumed` | Occurrence → solved, Step → running, Execution → running | Resend current step to agent |
| `solve_manually` | Occurrence → solved, Step → running, Execution → running | Resend current step to agent |
| `cancelled` | Occurrence → unsolved, Step → cancelled, Execution → cancelled | Cancel execution |
| `edited` | Step → edited flag set | Log only (no flow change) |

## Technical Implementation

### Orchestrator Event Handling

The orchestrator uses an event-driven approach with persistent state storage to handle human interventions across multiple worker processes:

```python
class WorkflowOrchestrator:
    def __init__(self, state_store: WorkflowStateStore):
        self.state_store = state_store  # Shared state across workers

    async def _wait_for_interaction_resolution(self, execution_id: uuid.UUID):
        """Wait for interaction via event-driven notification"""
        event = await self.state_store.get_event(execution_id)
        await event.wait()  # Blocks until notification received
        await self.state_store.clear_event(execution_id)

    async def handle_interaction_notification(
        self, execution_id: uuid.UUID, step_run_id: uuid.UUID, interaction_data: Dict[str, Any]
    ):
        """Called when gen_os_am_workflows state changes"""
        interaction_kind = interaction_data.get("kind")

        if interaction_kind == "approved":
            # Approved: move to next step in workflow
            await self._resume_execution(execution_id)
        elif interaction_kind in ["resumed", "solve_manually"]:
            # Resumed/SolveManually: user modified step run, resend to agent
            await self._retry_current_step(execution_id, step_run_id)
        elif interaction_kind == "cancelled":
            await self._cancel_execution(execution_id)
```

### Error Handling and Resilience

1. **Retry Logic**: Configurable retry count and exponential backoff for agent communication
2. **Timeout Handling**: Uses API_TIMEOUT_KEEP_ALIVE from settings
3. **Concurrency**: Supports multiple concurrent workflow executions
4. **State Management**: Uses `gen_os_am_workflows` as single source of truth

## Future Enhancements

1. **Parallel Step Execution**: Support for concurrent step execution where dependencies allow
2. **Enhanced Error Recovery**:
   - Recovery mechanism for AM crashes
   - SLA/timeout for manual approvals
   - Maximum retry/iteration count for human intervention loops
3. **Advanced Features**:
   - Multi-Agent Support
   - Workflow Versioning
   - Performance Monitoring
   - Advanced Scheduling
   - Workflow Templates
   - Dead Letter Queues
   - Audit Logging
