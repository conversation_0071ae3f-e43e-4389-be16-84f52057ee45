"""Add default source, ingestion, user and folder.

Revision ID: 9586ab310865
Revises: 8ad4c9ecbf6c
Create Date: 2025-04-05 13:54:33.012934

"""

from collections.abc import Sequence

from sqlmodel import Session, delete

from alembic import op
from gen_os_am_knowledge.sdk.models import Folder, Ingestion, Source, User
from gen_os_am_knowledge.sdk.models.common import (
    DEFAULT_FOLDER,
    DEFAULT_INGESTION,
    DEFAULT_SOURCE,
    DEFAULT_USER,
)

# revision identifiers, used by Alembic.
revision: str = "9586ab310865"
down_revision: str | None = "8ad4c9ecbf6c"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Upgrade the database."""
    bind = op.get_bind()  # we can't close this, managed by alembic
    with Session(bind) as session:
        session.add(DEFAULT_FOLDER)
        session.add(DEFAULT_SOURCE)
        session.add(DEFAULT_INGESTION)
        session.add(DEFAULT_USER)
        session.commit()
        session.flush()


def downgrade() -> None:
    """Downgrade the database."""
    bind = op.get_bind()  # we can't close this, managed by alembic
    with Session(bind) as session:
        stmt_del_usr = delete(User).where(User.id == DEFAULT_USER.id)  # type: ignore
        stmt_del_sync = delete(Ingestion).where(Ingestion.id == DEFAULT_INGESTION.id)  # type: ignore
        stmt_del_src = delete(Source).where(Source.id == DEFAULT_SOURCE.id)  # type: ignore
        stmt_del_folder = delete(Folder).where(Folder.id == DEFAULT_FOLDER.id)  # type: ignore
        session.exec(stmt_del_usr)  # type: ignore
        session.exec(stmt_del_sync)  # type: ignore
        session.exec(stmt_del_src)  # type: ignore
        session.exec(stmt_del_folder)  # type: ignore
        session.commit()
        session.flush()
