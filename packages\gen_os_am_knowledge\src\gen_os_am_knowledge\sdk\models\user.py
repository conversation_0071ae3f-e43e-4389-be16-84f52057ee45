"""The User Table that holds information about the users that operate on documents."""

import uuid

from sqlmodel import Field, Relationship, SQLModel


class User(SQLModel, table=True):
    """The User Table that holds information about the users that operate on documents.

    Args:
        id (uuid.UUID): The Internal Database Id.
            Autogenerated if not passed, which is the recommended way.
        name (str): The User Name.
        external_id (str): An Id from an External Authentication System.

        created_documents (list[Document]): The docs that were created by this User.
            A 1 User to N doc relationship, meaning a join with another table.
            Use for access.
        modified_documents (list[Document]): The docs that were modified by this User.
            A 1 User to N doc relationship, meaning a join with another table.
            Use for access.
        created_folders (list[Folder]): The folders that were created by this User.
            A 1 User to N doc relationship, meaning a join with another table.
            Use for access.
        modified_folders (list[Folder]): The folders that were modified by this User.
            A 1 User to N doc relationship, meaning a join with another table.
            Use for access.

    """

    __tablename__ = "user"  # type: ignore
    __table_args__ = {"extend_existing": True}

    id: uuid.UUID = Field(
        default_factory=uuid.uuid4,
        primary_key=True,
        nullable=False,
        description=(
            "The Internal Database Id. Autogenerated if not passed, "
            "which is the recommended way"
        ),
    )
    """The Internal Database Id.
    Autogenerated if not passed, which is the recommended way"""

    name: str = Field(
        nullable=False,
        unique=True,
        description="The User Name",
    )
    """The User Name"""

    external_id: str = Field(
        nullable=False,
        unique=True,
        description="An Id from an External Authentication System",
    )
    """An Id from an External Authentication System"""

    created_documents: list["Document"] = Relationship(  # type: ignore since pylance doesn't do forward references
        back_populates="created_by",
        sa_relationship_kwargs={"foreign_keys": "Document.created_by_id"},
    )
    """The docs that were created by this User. A 1 User to N doc relationship,
    meaning a join with another table. Use for access."""

    modified_documents: list["Document"] = Relationship(  # type: ignore since pylance doesn't do forward references
        back_populates="last_modified_by",
        sa_relationship_kwargs={"foreign_keys": "Document.last_modified_by_id"},
    )
    """
    The docs that were modified by this User.
    A 1 User to N doc relationship, meaning a join with another table. Use for access.
    """

    created_folders: list["Folder"] = Relationship(  # type: ignore since pylance doesn't do forward references
        back_populates="created_by",
        sa_relationship_kwargs={"foreign_keys": "Folder.created_by_id"},
    )
    """The folders that were created by this User. A 1 User to N doc relationship,
    meaning a join with another table. Use for access."""

    modified_folders: list["Folder"] = Relationship(  # type: ignore since pylance doesn't do forward references
        back_populates="last_modified_by",
        sa_relationship_kwargs={"foreign_keys": "Folder.last_modified_by_id"},
    )
    """The folders that were modified by this User. A 1 User to N doc relationship,
    meaning a join with another table. Use for access."""
