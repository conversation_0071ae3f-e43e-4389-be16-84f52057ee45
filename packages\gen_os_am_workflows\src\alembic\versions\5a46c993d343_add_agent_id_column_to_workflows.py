"""Add agent_id column to workflows.

Revision ID: 5a46c993d343
Revises: 049fa77ac6af
Create Date: 2025-07-04 12:36:02.223102

"""

from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "5a46c993d343"
down_revision: str | None = "049fa77ac6af"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Add agent_id column to workflows."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("workflow", schema=None) as batch_op:
        batch_op.add_column(sa.Column("agent_id", sa.String(), nullable=False))

    # ### end Alembic commands ###


def downgrade() -> None:
    """Remove agent_id column from workflows."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("workflow", schema=None) as batch_op:
        batch_op.drop_column("agent_id")

    # ### end Alembic commands ###
