"""Tests for ingestion sync status functionality."""

import os
import random
import shutil
from collections import Counter

import pytest
import pytest_asyncio

from gen_os_am_knowledge.sdk.filesystem_connectors.local_fs_connector import (
    LocalFSConnector,
)
from gen_os_am_knowledge.sdk.models.common import DEFAULT_SOURCE, DEFAULT_USER
from gen_os_am_knowledge.sdk.operations.document_ops import DocumentService
from gen_os_am_knowledge.sdk.operations.folder_ops import FolderService
from gen_os_am_knowledge.sdk.operations.ingestion_ops import IngestionService

random.seed(3)


@pytest.fixture(scope="module")
def sample_file():
    """Create sample test files."""
    for i in range(10):
        with open(f"test{i}.txt", "w") as f:
            f.write("Hello, world!")
    yield
    for i in range(10):
        os.remove(f"test{i}.txt")


@pytest.fixture(scope="module")
def create_root_folder():
    """Create root folder for testing."""
    storage_path = "path-to-local-storage-sdk"
    root_folder = "root-folder/ingestion_sync_status"
    os.makedirs(f"{storage_path}/{root_folder}", exist_ok=True)
    yield root_folder
    shutil.rmtree(storage_path)


@pytest.fixture(scope="module")
def local_fs_connector(create_root_folder):
    """Create local filesystem connector."""
    yield LocalFSConnector(root_folder=create_root_folder)


@pytest_asyncio.fixture(scope="module")
async def setup_storage(local_fs_connector, sample_file):
    """Set up storage with test folders, documents, and ingestions."""
    folder_service = FolderService(local_fs_connector)
    document_service = DocumentService(local_fs_connector)
    ingestion_service = IngestionService()
    folder1 = await folder_service.create_folder("folder1", user_id=DEFAULT_USER.id)
    folder2 = await folder_service.create_folder("folder2", user_id=DEFAULT_USER.id)

    ingestion1 = await ingestion_service.create_ingestion(
        DEFAULT_SOURCE.id, "old", folder1.id
    )
    ingestion2 = await ingestion_service.create_ingestion(
        DEFAULT_SOURCE.id, "new", folder2.id
    )

    docs1 = []
    statuses1 = []
    for i in range(5):
        with open(f"test{i}.txt", "rb") as f:
            doc = await document_service.create_document(
                file=f, folder_id=folder1.id, ingestion_id=ingestion1.id
            )
            docs1.append(doc)
            status = random.choice(["Pending", "Synced", "Error"])
            statuses1.append(status)
            await document_service.update_document_status(
                doc.id, user_id=DEFAULT_USER.id, status=status
            )
    docs2 = []
    statuses2 = []
    for i in range(5, 10):
        with open(f"test{i}.txt", "rb") as f:
            doc = await document_service.create_document(
                file=f, folder_id=folder2.id, ingestion_id=ingestion2.id
            )
            docs2.append(doc)
            status = random.choice(["Pending", "Synced", "Error"])
            statuses2.append(status)
            await document_service.update_document_status(
                doc.id, user_id=DEFAULT_USER.id, status=status
            )
    yield folder1, folder2, docs1, docs2, statuses1, statuses2, ingestion1, ingestion2
    for doc in docs1:
        await document_service.delete_document(doc.id, DEFAULT_USER.id)
    for doc in docs2:
        await document_service.delete_document(doc.id, DEFAULT_USER.id)
    await document_service.cleanup_deleted_documents()
    await ingestion_service.delete_ingestion(ingestion1.id)
    await ingestion_service.delete_ingestion(ingestion2.id)
    await folder_service.delete_folder(folder1.id, user_id=DEFAULT_USER.id)
    await folder_service.delete_folder(folder2.id, user_id=DEFAULT_USER.id)


@pytest.mark.asyncio
async def test_ingestion_stats(local_fs_connector, setup_storage):
    """Test ingestion statistics functionality."""
    folder1, folder2, docs1, docs2, statuses1, statuses2, ingestion1, ingestion2 = (
        setup_storage
    )
    key_mapping = {"Error": "Sync error", "Pending": "Syncing"}
    document_service = DocumentService(local_fs_connector)

    # get all ingestions statuses
    ing_status = await document_service.get_ingestions_stats()
    assert len(ing_status) == 2
    assert set(ing_status.keys()) == {ingestion1.id, ingestion2.id}
    assert ing_status[ingestion1.id] == Counter(
        {key_mapping.get(k, k): v for k, v in Counter(statuses1).items()}
    )
    assert ing_status[ingestion2.id] == Counter(
        {key_mapping.get(k, k): v for k, v in Counter(statuses2).items()}
    )

    # get stats filtering ingestions by ids
    ing_status = await document_service.get_ingestions_stats(
        ingestion_ids=[ingestion1.id]
    )
    assert len(ing_status) == 1
    assert set(ing_status.keys()) == {ingestion1.id}
    assert ing_status[ingestion1.id] == Counter(
        {key_mapping.get(k, k): v for k, v in Counter(statuses1).items()}
    )

    # get stats by description
    ing_status = await document_service.get_ingestions_stats(
        ingestion_descriptions=["new"]
    )
    assert len(ing_status) == 1
    assert set(ing_status.keys()) == {ingestion2.id}
    assert ing_status[ingestion2.id] == Counter(
        {key_mapping.get(k, k): v for k, v in Counter(statuses2).items()}
    )
