"""Add folder-ingestion relationship.

Revision ID: cd13c789811f
Revises: 694f938b6192
Create Date: 2025-03-27 11:50:54.780705

"""

from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "cd13c789811f"
down_revision: str | None = "694f938b6192"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Upgrade the database."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("ingestion", sa.Column("folder_id", sa.Uuid(), nullable=False))
    op.create_foreign_key(None, "ingestion", "folder", ["folder_id"], ["id"])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade the database."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "ingestion", type_="foreignkey")
    op.drop_column("ingestion", "folder_id")
    # ### end Alembic commands ###
