"""Add agent configuration table.

Revision ID: 8f5c9ab24907
Revises:
Create Date: 2025-06-27 14:17:07.865059

"""

from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "8f5c9ab24907"
down_revision: str | None = None
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Upgrade database to add agent configuration table."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "agent_configuration",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("agent_id", sa.UUID(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column("value", sa.Text(), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    with op.batch_alter_table("agent_configuration", schema=None) as batch_op:
        batch_op.create_index(
            batch_op.f("ix_agent_configuration_agent_id"), ["agent_id"], unique=False
        )
        batch_op.create_index(
            "ix_agent_configuration_agent_id_name", ["agent_id", "name"], unique=True
        )

    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade database to remove agent configuration table."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("agent_configuration", schema=None) as batch_op:
        batch_op.drop_index("ix_agent_configuration_agent_id_name")
        batch_op.drop_index(batch_op.f("ix_agent_configuration_agent_id"))

    op.drop_table("agent_configuration")
    # ### end Alembic commands ###
