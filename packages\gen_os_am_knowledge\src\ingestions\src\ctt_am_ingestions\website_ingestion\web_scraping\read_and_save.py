"""Functions to read the input file and save the output file."""

import json
import os

import pandas as pd
from numpy import ndarray

from .results import Results

DATA_FOLDER = os.path.join(os.path.dirname(__file__), "..", "data")
DATA_FOLDER = os.path.abspath(DATA_FOLDER)


def read_urls(path: str = os.path.join(DATA_FOLDER, "urls.xlsx")) -> ndarray:
    """Get a list of URLs from an Excel file."""
    df = pd.read_excel(path, skiprows=2, header=0)
    df = df.iloc[:, 1:]
    return df["URLs"].unique()


def save_to_json(
    results: Results, path: str = os.path.join(DATA_FOLDER, "output.json")
) -> None:
    """Save the results to a JSON file."""
    output = results.model_dump()
    with open(path, "w", encoding="utf-8") as f:
        json.dump(output, f, ensure_ascii=False, indent=2)
        f.write("\n")
