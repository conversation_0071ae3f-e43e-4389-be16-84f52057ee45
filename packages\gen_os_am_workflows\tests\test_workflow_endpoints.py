"""Tests for the workflow endpoints."""

import uuid
from typing import Any

import pytest
from fastapi.testclient import Test<PERSON><PERSON>
from sqlalchemy import select

from gen_os_am_workflows.database.models import (
    Step,
    StepInput,
    StepOutput,
    Workflow,
    WorkflowExecution,
)
from gen_os_am_workflows.database.session import SessionManager


class TestWorkflowEndpoints:
    """Test workflow CRUD operations."""

    def test_create_workflow(self, client: TestClient, get_session):
        """Test creating a new workflow."""
        workflow_name = f"test_workflow_{uuid.uuid4().hex[:8]}"
        workflow_data = {
            "name": workflow_name,
            "description": "A test workflow",
            "extra_fields": [{"field_name": "priority", "field_type": "string"}],
            "agent_id": "test-agent",
        }

        response = client.post("/agents/test-agent/wf/v1/workflows", json=workflow_data)

        assert response.status_code == 200
        data = response.json()
        assert data["name"] == workflow_name
        assert data["description"] == "A test workflow"
        assert "id" in data

    def test_get_workflows(self, client: TestClient):
        """Test getting all workflows."""
        response = client.get("/agents/test-agent/wf/v1/workflows")

        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)

    def test_get_workflow_by_name(self, client: TestClient):
        """Test getting a specific workflow by name."""
        # First create a workflow
        workflow_name = f"test_workflow_get_{uuid.uuid4().hex[:8]}"
        workflow_data = {
            "name": workflow_name,
            "description": "A test workflow for get",
            "extra_fields": [],
            "agent_id": "test-agent",
        }

        create_response = client.post("/agents/test-agent/wf/v1/workflows", json=workflow_data)
        assert create_response.status_code == 200

        # Then get it by name
        response = client.get(f"/agents/test-agent/wf/v1/workflows/{workflow_name}")
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == workflow_name
        assert data["description"] == "A test workflow for get"

    def test_update_workflow(self, client: TestClient):
        """Test updating a workflow."""
        # First create a workflow
        workflow_name = f"test_workflow_update_{uuid.uuid4().hex[:8]}"
        workflow_data = {
            "name": workflow_name,
            "description": "A test workflow for update",
            "extra_fields": [],
            "agent_id": "test-agent",
        }

        create_response = client.post("/agents/test-agent/wf/v1/workflows", json=workflow_data)
        assert create_response.status_code == 200

        # Then update it
        update_data = {"description": "Updated workflow description"}
        response = client.patch(
            f"/agents/test-agent/wf/v1/workflows/{workflow_name}", json=update_data
        )
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert "successfully" in data["message"].lower()

        # Verify the update by getting the workflow
        get_response = client.get(f"/agents/test-agent/wf/v1/workflows/{workflow_name}")
        assert get_response.status_code == 200
        get_data = get_response.json()
        assert get_data["description"] == "Updated workflow description"

    def test_delete_workflow(self, client: TestClient):
        """Test deleting a workflow."""
        # First create a workflow
        workflow_name = f"test_workflow_delete_{uuid.uuid4().hex[:8]}"
        workflow_data = {
            "name": workflow_name,
            "description": "A test workflow for delete",
            "extra_fields": [],
            "agent_id": "test-agent",
        }

        create_response = client.post("/agents/test-agent/wf/v1/workflows", json=workflow_data)
        assert create_response.status_code == 200

        # Then delete it
        response = client.delete(f"/agents/test-agent/wf/v1/workflows/{workflow_name}")
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert "successfully" in data["message"].lower()

        # Verify it's deleted
        get_response = client.get(f"/agents/test-agent/wf/v1/workflows/{workflow_name}")
        assert get_response.status_code == 404
