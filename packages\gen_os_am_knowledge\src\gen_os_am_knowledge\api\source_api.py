"""Module for the Source API."""

import json
import logging
import uuid

from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from gen_os_am_knowledge.api.utils import error_handling_decorator
from gen_os_am_knowledge.config.logger_config import get_api_logger
from gen_os_am_knowledge.sdk.models import Source
from gen_os_am_knowledge.sdk.operations import SourceService
from gen_os_am_knowledge.sdk.operations.utils import NOT_PROVIDED


class UpdateSourceModel(BaseModel):
    """Update source model."""

    source_name: str | type[NOT_PROVIDED] = NOT_PROVIDED
    source_type: str | type[NOT_PROVIDED] = NOT_PROVIDED
    meta: dict | type[NOT_PROVIDED] = NOT_PROVIDED


class CreateSourceModel(BaseModel):
    """Create source model."""

    source_name: str
    source_type: str
    meta: dict | None = None


class SourceAPI:
    """Source API class to handle source related operations.

    Args:
        prefix (str, optional): Prefix for the source API. Defaults to "".

    Methods:
        get_router: Returns the FastAPI router for the source API

    """

    def __init__(
        self,
        prefix: str = "",
        logger: logging.Logger = get_api_logger(),
    ):
        """Initialize the SourceAPI class."""
        self.prefix = prefix
        self.source_service = SourceService(
            logger=logger.getChild("sdk"),
        )

    def get_router(self) -> APIRouter:
        """Return the FastAPI router for the source API.

        Returns:
            APIRouter: The FastAPI router for the source API

        """
        router = APIRouter(prefix=self.prefix + "/source")

        @router.post("/")
        @error_handling_decorator
        async def create_source(
            create_model: CreateSourceModel,
        ) -> JSONResponse:
            """Create a new source.

            Args:
                create_model (CreateSourceModel): Source creation model as json
                    Ex:
                    {
                        "source_name": "new_source_name",
                        "source_type": "new_source_type",
                        "meta": {
                            "key1": "value1",
                            "key2": "value2"
                        }
                    }

            Returns:
                JSONResponse: JSON response with the created source

            """
            result = await self.source_service.create_source(
                **create_model.model_dump()
            )
            return JSONResponse(content=result.model_dump(mode="json"), status_code=201)

        @router.delete("/{source_id}")
        @error_handling_decorator
        async def delete_source(source_id: uuid.UUID) -> Source | None:
            """Delete a source.

            Args:
                source_id (uuid.UUID): Source ID to delete

            Returns:
                Source: Deleted source

            """
            result = await self.source_service.delete_source(source_id)
            if result.success:
                return result.deleted_instance
            else:
                raise HTTPException(status_code=404, detail=result.error)

        @router.get("/{source_id}")
        @error_handling_decorator
        async def get_source(source_id: uuid.UUID) -> Source | None:
            """Get a source.

            Args:
                source_id (uuid.UUID): Source ID to get

            Returns:
                Source: Source instance if found else None

            """
            result = await self.source_service.get_source(source_id)
            return result

        @router.get("/")
        @error_handling_decorator
        async def search_sources(
            source_name: str | None = None,
            source_type: str | None = None,
            metadata_field: str | None = None,
            offset: int = 0,
            limit: int = 20,
        ) -> list[Source]:
            """Search sources with query parameters.

            Args:
                source_name (str, optional): Source name to search
                source_type (str, optional): Source type to search
                metadata_field (str, optional): Metadata field to search
                offset (int, optional): Offset for pagination. Defaults to 0.
                limit (int, optional): Limit for pagination. Defaults to 20.

            Returns:
                list[Source]: List of sources found

            """
            if isinstance(metadata_field, str):
                metadata_field = json.loads(metadata_field)
            result = await self.source_service.search_sources(
                source_name=source_name,
                source_type=source_type,
                metadata_field=metadata_field,
                offset=offset,
                limit=limit,
            )
            return result

        @router.patch("/{source_id}")
        @error_handling_decorator
        async def update_source(
            source_id: uuid.UUID,
            update_model: UpdateSourceModel,
        ):
            """Update a source.

            Args:
                source_id (uuid.UUID): Source ID to update
                update_model (UpdateSourceModel): Source update model as json
                    Ex:
                    {
                        "source_name": "new_source_name",
                        "source_type": "new_source_type",
                        "meta": {
                            "key1": "value1",
                            "key2": "value2"
                        }
                    }

            Returns:
                UpdateResult: Update result with the updated source instance

            """
            result = await self.source_service.update_source(
                source_id, **update_model.model_dump()
            )
            return result

        return router
