"""Tests for the step endpoints."""

import uuid
from typing import Any

import pytest
from fastapi.testclient import Test<PERSON>lient
from sqlalchemy import select

from gen_os_am_workflows.database.models import (
    Step,
    StepInput,
    StepOutput,
    Workflow,
    WorkflowExecution,
)
from gen_os_am_workflows.database.session import SessionManager


class TestStepEndpoints:
    """Test step CRUD operations."""

    def test_create_step(self, client: TestClient):
        """Test creating a new step for a workflow."""
        # First create a workflow
        workflow_name = f"test_workflow_steps_{uuid.uuid4().hex[:8]}"
        workflow_data = {
            "name": workflow_name,
            "description": "A test workflow for steps",
            "extra_fields": [],
            "agent_id": "test-agent",
        }

        create_workflow_response = client.post(
            "/agents/test-agent/wf/v1/workflows", json=workflow_data
        )
        assert create_workflow_response.status_code == 200

        # Then create a step with proper validation
        step_name = f"test_step_{uuid.uuid4().hex[:8]}"
        step_data = {
            "name": step_name,
            "description": "A test step",
            "step_type": "gen_ai",  # Use gen_ai type which allows outputs
            "order_number": 1,
            "inputs": [
                {
                    "step_block_name": "input_text",
                    "step_block_type": "text_unstructured",
                    "type_extra": None,  # text_unstructured doesn't require type_extra
                    "order_number": 1,
                    "status": "non_editable",  # gen_ai type only allows non_editable inputs
                }
            ],
            "outputs": [
                {
                    "step_block_name": "output_text",
                    "step_block_type": "text_unstructured",
                    "type_extra": None,  # text_unstructured doesn't require type_extra
                    "order_number": 1,
                    "status": "non_editable",
                }
            ],
        }

        response = client.post(
            f"/agents/test-agent/wf/v1/workflows/{workflow_name}/steps", json=step_data
        )

        assert response.status_code == 200
        data = response.json()
        assert data["name"] == step_name
        assert data["step_type"] == "gen_ai"

    def test_get_steps(self, client: TestClient):
        """Test getting steps for a workflow."""
        workflow_name = f"test_workflow_steps_get_{uuid.uuid4().hex[:8]}"
        workflow_data = {
            "name": workflow_name,
            "description": "A test workflow for getting steps",
            "extra_fields": [],
            "agent_id": "test-agent",
        }

        # Create workflow first
        create_response = client.post("/agents/test-agent/wf/v1/workflows", json=workflow_data)
        assert create_response.status_code == 200

        # Get steps (should be empty)
        response = client.get(f"/agents/test-agent/wf/v1/workflows/{workflow_name}/steps")
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) == 0

    def test_update_step(self, client: TestClient):
        """Test updating a step."""
        # First create a workflow and step
        workflow_name = f"test_workflow_steps_update_{uuid.uuid4().hex[:8]}"
        workflow_data = {
            "name": workflow_name,
            "description": "A test workflow for updating steps",
            "extra_fields": [],
            "agent_id": "test-agent",
        }

        create_workflow_response = client.post(
            "/agents/test-agent/wf/v1/workflows", json=workflow_data
        )
        assert create_workflow_response.status_code == 200

        # Create a step
        step_name = f"test_step_update_{uuid.uuid4().hex[:8]}"
        step_data = {
            "name": step_name,
            "description": "A test step for updating",
            "step_type": "gen_ai",
            "order_number": 1,
            "inputs": [
                {
                    "step_block_name": "input_text",
                    "step_block_type": "text_unstructured",
                    "type_extra": None,
                    "order_number": 1,
                    "status": "non_editable",
                }
            ],
            "outputs": [
                {
                    "step_block_name": "output_text",
                    "step_block_type": "text_unstructured",
                    "type_extra": None,
                    "order_number": 1,
                    "status": "non_editable",
                }
            ],
        }

        create_step_response = client.post(
            f"/agents/test-agent/wf/v1/workflows/{workflow_name}/steps", json=step_data
        )
        assert create_step_response.status_code == 200

        # Update the step
        update_data = {"description": "Updated description"}
        update_response = client.patch(
            f"/agents/test-agent/wf/v1/workflows/{workflow_name}/steps/{step_name}",
            json=update_data,
        )
        assert update_response.status_code == 200
        updated_data = update_response.json()
        assert updated_data["description"] == "Updated description"

    def test_delete_step(self, client: TestClient):
        """Test deleting a step."""
        # First create a workflow and step
        workflow_name = f"test_workflow_steps_delete_{uuid.uuid4().hex[:8]}"
        workflow_data = {
            "name": workflow_name,
            "description": "A test workflow for deleting steps",
            "extra_fields": [],
            "agent_id": "test-agent",
        }

        create_workflow_response = client.post(
            "/agents/test-agent/wf/v1/workflows", json=workflow_data
        )
        assert create_workflow_response.status_code == 200

        # Create a step
        step_name = f"test_step_delete_{uuid.uuid4().hex[:8]}"
        step_data = {
            "name": step_name,
            "description": "A test step for deleting",
            "step_type": "gen_ai",
            "order_number": 1,
            "inputs": [
                {
                    "step_block_name": "input_text",
                    "step_block_type": "text_unstructured",
                    "type_extra": None,
                    "order_number": 1,
                    "status": "non_editable",
                }
            ],
            "outputs": [
                {
                    "step_block_name": "output_text",
                    "step_block_type": "text_unstructured",
                    "type_extra": None,
                    "order_number": 1,
                    "status": "non_editable",
                }
            ],
        }

        create_step_response = client.post(
            f"/agents/test-agent/wf/v1/workflows/{workflow_name}/steps", json=step_data
        )
        assert create_step_response.status_code == 200

        # Delete the step
        delete_response = client.delete(
            f"/agents/test-agent/wf/v1/workflows/{workflow_name}/steps/{step_name}"
        )
        assert delete_response.status_code == 200

        # Verify step is deleted
        get_response = client.get(f"/agents/test-agent/wf/v1/workflows/{workflow_name}/steps")
        assert get_response.status_code == 200
        data = get_response.json()
        assert len(data) == 0
