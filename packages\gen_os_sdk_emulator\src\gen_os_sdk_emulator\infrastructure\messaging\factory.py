"""Factory for creating message broker instances."""

from typing import Literal

from gen_os_sdk_emulator.infrastructure.messaging.base import MessageBroker
from gen_os_sdk_emulator.infrastructure.messaging.pubsub import GooglePubSubBroker


class MessagingFactory:
    """Factory for creating message broker instances."""

    @staticmethod
    def create_message_broker(
        provider: Literal["gcp"] | None = None,
        project_id: str | None = None,
        topic: str | None = None,
    ) -> MessageBroker:
        """Create a message broker based on the provider.

        Args:
            provider: The message broker provider to use (e.g., "gcp", "aws", "azure")
            project_id: The ID of the project to use
            topic: The topic to use

        Returns:
            MessageBroker: An implementation of the message broker interface

        Raises:
            ValueError: If the provider is not supported
            or required parameters are missing

        """
        if not provider:
            provider = "gcp"

        match provider.lower():
            case "gcp":
                if not project_id:
                    raise ValueError(
                        f"Couldn't resolve project ID for provider `{provider}`."
                    )
                return GooglePubSubBroker(project_id=project_id, topic=topic)
            case _:
                raise ValueError(f"Unsupported message broker provider: {provider}")
