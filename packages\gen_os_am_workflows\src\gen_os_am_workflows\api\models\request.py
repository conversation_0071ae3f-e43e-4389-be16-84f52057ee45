"""Request models for the Case Management API.

This module defines the Pydantic models used for request validation in the API.
The models are divided into two main categories:
1. Creation models - Used for creating new resources
2. Update models - Used for updating existing resources
"""

from datetime import datetime
from typing import Literal
from uuid import UUID

from pydantic import BaseModel, field_validator, model_validator

from gen_os_am_workflows.database.models import (
    InteractionKind,
    InteractionKindCancelled,
    InteractionKindEdited,
    OccurrenceReasonEditPreviousStep,
    OccurrenceReasonError,
    OccurrenceReasons,
    OccurrenceStatus,
    StepBlockStatus,
    StepBlockType,
    StepBlockTypeSelection,
    StepBlockTypeTextStructured,
    StepRunStatus,
    StepRunStatusBlocked,
    StepType,
    WorkflowExecutionStatus,
)

AllBaseTypes = str | float | bool | int
DatasetType = list[dict[str, list[AllBaseTypes] | AllBaseTypes]]

# Allowed MIME types and their corresponding extensions
# See https://developer.mozilla.org/en-US/docs/Web/HTTP/Guides/MIME_types/Common_types
AllowedMimeTypes = Literal[
    "text/csv",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "image/jpeg",
    "text/markdown",
    "application/vnd.oasis.opendocument.presentation",
    "application/vnd.oasis.opendocument.spreadsheet",
    "application/vnd.oasis.opendocument.text",
    "application/pdf",
    "application/vnd.ms-powerpoint",
    "application/vnd.openxmlformats-officedocument.presentationml.presentation",
    "application/vnd.ms-excel",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "text/plain",
]

ALLOWED_EXTENSIONS = [
    ".csv",
    ".doc",
    ".docx",
    ".jpg",
    ".jpeg",
    ".png",
    ".md",
    ".odp",
    ".ods",
    ".odt",
    ".pdf",
    ".pptx",
    ".xls",
    ".xlsx",
    ".txt",
]

### Creation Models

# this models should be moved to the Gen-OS SDK


class PreviousStepFed(BaseModel):
    """Model for a previous step fed."""

    step_name: str
    step_block_name: str


class StepBlockTypeExtraTextStructured(BaseModel):
    """Model for a step block type extra."""

    field_name: str
    field_type: Literal["string", "list[string]"]


class SelectionOutput(BaseModel):
    """Model for a step block run with selection."""

    options: list[str]
    default_value: str

    @model_validator(mode="after")
    @classmethod
    def validate_default_value(cls, data: "SelectionOutput") -> "SelectionOutput":
        """Validate that default_value is one of the options."""
        if data.default_value not in data.options:
            raise ValueError("default_value must be one of the options")
        return data


class StepBlock(BaseModel):
    """Model for a step block."""

    step_block_name: str
    step_block_type: StepBlockType
    order_number: int
    status: StepBlockStatus
    previous_step_fed: PreviousStepFed | dict | None = None
    default_expanded: bool | None = None
    type_extra: SelectionOutput | list[StepBlockTypeExtraTextStructured] | dict | None = None

    @model_validator(mode="after")
    @classmethod
    def validate_type_extra(cls, data: "StepBlock") -> "StepBlock":
        """Validate type_extra based on step_block_type."""
        if data.step_block_type == StepBlockTypeSelection:
            if not data.type_extra or not isinstance(data.type_extra, SelectionOutput):
                raise ValueError("type_extra must be a SelectionOutput for selection type")
        elif data.step_block_type == StepBlockTypeTextStructured:
            if (
                not data.type_extra
                or not isinstance(data.type_extra, list)
                or not all(
                    isinstance(item, StepBlockTypeExtraTextStructured) for item in data.type_extra
                )
            ):
                raise ValueError(
                    f"type_extra must be a list of StepBlockTypeExtraTextStructured for "
                    f"{StepBlockTypeTextStructured} type"
                )
        return data


class StepCreate(BaseModel):
    """Model for creating a new workflow step."""

    name: str
    description: str
    step_type: StepType
    order_number: int
    inputs: list[StepBlock]
    outputs: list[StepBlock]
    solve_manually: bool | None = None
    human_approval_required: bool | None = None

    @model_validator(mode="after")
    @classmethod
    def validate_solve_manually_only_for_action_steps(cls, data: "StepCreate") -> "StepCreate":
        """Validate that solve_manually is only set for action steps."""
        if data.step_type != "action" and data.solve_manually is not None and data.solve_manually:
            raise ValueError("Solve manually is only allowed for action steps")
        return data


class WorkflowCreate(BaseModel):
    """Model for creating a new workflow."""

    name: str
    agent_id: str
    description: str
    extra_fields: list["ExtraFields"] | None = None


class WorkflowExecutionCreate(BaseModel):
    """Model for creating a new workflow execution."""

    status: WorkflowExecutionStatus
    test_execution: bool | None = None


class StepBlockRunDataset(BaseModel):
    """Model for a step block run with data."""

    dataset: DatasetType


class File(BaseModel):
    """Model for a file."""

    # TODO(Henrique): This should be a UUID
    # but I was running into this `TypeError: Object of type UUID is not JSON serializable`
    id: str
    name: str


class StepBlockRunFile(BaseModel):
    """Model for a step block run with file."""

    files: list[File]


class StepBlockRunTextStructured(BaseModel):
    """Model for a step block run with text structured data."""

    text_structured: dict[str, str | list[AllBaseTypes]]


class StepBlockRunTextUnstructured(BaseModel):
    """Model for a step block run with text unstructured data."""

    text_unstructured: str


class ToolOutputChunk(BaseModel):
    """Model for a tool output."""

    chunk_file: str
    chunk_content: str


class ToolOutput(BaseModel):
    """Model for a step block run with tool."""

    number_of_calls: int
    tool_name: str
    tool_input: str
    tool_output: DatasetType | ToolOutputChunk


class StepBlockRunTool(BaseModel):
    """Model for a step block run with tool."""

    tool: ToolOutput


class StepBlockRunSelection(BaseModel):
    """Model for a step block run with selection."""

    selection: SelectionOutput


class EmailOutput(BaseModel):
    """Model for a step block run with email."""

    subject: str
    body: str
    sender: str
    to: list[str]
    attachments: list[File] | None = None


class StepBlockRunEmail(BaseModel):
    """Model for a step block run with email."""

    email: EmailOutput


class StepBlockRun(StepBlock):
    """Model for a step block run."""

    edited: bool | None = False
    data: (
        StepBlockRunDataset
        | StepBlockRunFile
        | StepBlockRunTextStructured
        | StepBlockRunTextUnstructured
        | StepBlockRunTool
        | StepBlockRunSelection
        | StepBlockRunEmail
    )


class OccurrenceCreate(BaseModel):
    """Model for creating a new occurrence."""

    reason: OccurrenceReasons
    message: str | None = None

    @model_validator(mode="after")
    @classmethod
    def validate_message_if_error(cls, data: "OccurrenceCreate") -> "OccurrenceCreate":
        """Validate that message is provided if reason is error."""
        if data.reason == OccurrenceReasonError and data.message is None:
            raise ValueError("Message is required when reason is error")
        return data


class StepRunCreate(BaseModel):
    """Model for creating a new step run."""

    step_id: UUID
    status: StepRunStatus
    inputs: list[StepBlockRun] | None = None
    outputs: list[StepBlockRun] | None = None


class InteractionCreate(BaseModel):
    """Model for creating a new interaction."""

    resolution: str | None = None
    edited_data: list[StepBlockRun] | None = None
    kind: InteractionKind

    @model_validator(mode="after")
    @classmethod
    def validate_kind(cls, data: "InteractionCreate") -> "InteractionCreate":
        """Validate that resolution is provided if kind is cancelled."""
        if data.kind == InteractionKindCancelled and data.resolution is None:
            raise ValueError("Resolution is required when kind is cancelled")
        if data.kind == InteractionKindEdited and data.edited_data is None:
            raise ValueError("Edited data is required when kind is edited")
        return data


### Update Models


class StepUpdate(BaseModel):
    """Model for updating an existing step."""

    name: str | None = None
    description: str | None = None
    step_type: StepType | None = None
    order_number: int | None = None
    inputs: list[StepBlock] | None = None
    outputs: list[StepBlock] | None = None
    solve_manually: bool | None = None
    human_approval_required: bool | None = None

    @model_validator(mode="after")
    @classmethod
    def validate_solve_manually_only_for_action_steps(cls, data: "StepUpdate") -> "StepUpdate":
        """Validate that solve_manually is only set for action steps."""
        if data.step_type != "action" and data.solve_manually is not None and data.solve_manually:
            raise ValueError("Solve manually is only allowed for action steps")
        return data


class WorkflowUpdate(BaseModel):
    """Model for updating an existing workflow."""

    name: str | None = None
    agent_id: str | None = None
    description: str | None = None
    extra_fields: list["ExtraFields"] | None = None


class ExtraFields(BaseModel):
    """Extra fields for a workflow."""

    field_name: str
    field_type: Literal["string", "number", "boolean"]


class ExtraFieldsRun(ExtraFields):
    """Model for extra fields with data."""

    data: str | float | bool


class WorkflowExecutionUpdate(BaseModel):
    """Model for updating an existing workflow execution."""

    status: WorkflowExecutionStatus | None = None
    extra_fields: list[ExtraFieldsRun] | None = None


class StepRunUpdate(BaseModel):
    """Model for updating an existing step run."""

    inputs: list[StepBlockRun] | None = None
    outputs: list[StepBlockRun] | None = None
    status: StepRunStatus | None = None
    completed_at: datetime | None = None
    occurrence: OccurrenceCreate | None = None

    @model_validator(mode="after")
    @classmethod
    def validate_occurrence_if_blocked(cls, data: "StepRunUpdate") -> "StepRunUpdate":
        """Validate that occurrence is provided if status is blocked."""
        if data.status in StepRunStatusBlocked and data.occurrence is None:
            raise ValueError("Occurrence is required when status is blocked")
        return data

    @model_validator(mode="after")
    @classmethod
    def validate_run_from_here(cls, data: "StepRunUpdate") -> "StepRunUpdate":
        """Validate that all fields are empty if occurrence reason is edit_previous_step."""
        if (
            data.occurrence is not None
            and data.occurrence.reason == OccurrenceReasonEditPreviousStep
            and (
                data.status is not None
                or data.completed_at is not None
                or data.inputs is not None
                or data.outputs is not None
            )
        ):
            raise ValueError(
                "All fields must be empty when occurrence reason is edit_previous_step"
            )
        return data


class OccurrenceUpdate(BaseModel):
    """Model for updating an existing occurrence."""

    reason: OccurrenceReasons | None = None
    status: OccurrenceStatus | None = None
    message: str | None = None

    @model_validator(mode="after")
    @classmethod
    def validate_message_if_error(cls, data: "OccurrenceUpdate") -> "OccurrenceUpdate":
        """Validate that message is provided if reason is error."""
        if data.reason == OccurrenceReasonError and data.message is None:
            raise ValueError("Message is required when reason is error")
        return data


class InteractionUpdate(BaseModel):
    """Model for updating an existing interaction."""

    edited_data: list[StepBlockRun] | None = None
    kind: InteractionKind | None = None
    resolution: str | None = None

    @model_validator(mode="after")
    @classmethod
    def validate_kind(cls, data: "InteractionUpdate") -> "InteractionUpdate":
        """Validate that resolution is provided if kind is cancelled."""
        if data.kind == InteractionKindCancelled and data.resolution is None:
            raise ValueError("Resolution is required when kind is cancelled")
        if data.kind == InteractionKindEdited and data.edited_data is None:
            raise ValueError("Edited data is required when kind is edited")
        return data


class FileUploadRequest(BaseModel):
    """Model for file upload requests."""

    file_name: str
    content_type: AllowedMimeTypes

    @field_validator("file_name")
    @classmethod
    def validate_file_extension(cls, v):
        """Validate the file extension is allowed."""
        ext = "." + v.split(".")[-1].lower() if "." in v else ""
        if ext not in ALLOWED_EXTENSIONS:
            raise ValueError(
                f"File extension {ext} is not allowed. Allowed extensions: {ALLOWED_EXTENSIONS}"
            )
        return v
