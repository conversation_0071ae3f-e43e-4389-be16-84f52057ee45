"""change json columns to non nullable with default {}.

Revision ID: e38a72229471
Revises: d9e3a35f44ce
Create Date: 2025-05-07 17:00:50.265573

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "e38a72229471"
down_revision: str | None = "d9e3a35f44ce"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Alembic upgrade script."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("interaction", schema=None) as batch_op:
        batch_op.alter_column(
            "edited_data", existing_type=postgresql.JSON(astext_type=sa.Text()), nullable=False
        )

    with op.batch_alter_table("output_change", schema=None) as batch_op:
        batch_op.alter_column(
            "input", existing_type=postgresql.JSON(astext_type=sa.Text()), nullable=False
        )
        batch_op.alter_column(
            "output", existing_type=postgresql.JSON(astext_type=sa.Text()), nullable=False
        )
        batch_op.alter_column(
            "output_edited", existing_type=postgresql.JSON(astext_type=sa.Text()), nullable=False
        )

    with op.batch_alter_table("step_run", schema=None) as batch_op:
        batch_op.alter_column(
            "output", existing_type=postgresql.JSON(astext_type=sa.Text()), nullable=False
        )

    with op.batch_alter_table("workflow_run", schema=None) as batch_op:
        batch_op.alter_column(
            "input", existing_type=postgresql.JSON(astext_type=sa.Text()), nullable=False
        )
        batch_op.alter_column(
            "extra_fields", existing_type=postgresql.JSON(astext_type=sa.Text()), nullable=False
        )

    # ### end Alembic commands ###


def downgrade() -> None:
    """Alembic downgrade script."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("workflow_run", schema=None) as batch_op:
        batch_op.alter_column(
            "extra_fields", existing_type=postgresql.JSON(astext_type=sa.Text()), nullable=True
        )
        batch_op.alter_column(
            "input", existing_type=postgresql.JSON(astext_type=sa.Text()), nullable=True
        )

    with op.batch_alter_table("step_run", schema=None) as batch_op:
        batch_op.alter_column(
            "output", existing_type=postgresql.JSON(astext_type=sa.Text()), nullable=True
        )

    with op.batch_alter_table("output_change", schema=None) as batch_op:
        batch_op.alter_column(
            "output_edited", existing_type=postgresql.JSON(astext_type=sa.Text()), nullable=True
        )
        batch_op.alter_column(
            "output", existing_type=postgresql.JSON(astext_type=sa.Text()), nullable=True
        )
        batch_op.alter_column(
            "input", existing_type=postgresql.JSON(astext_type=sa.Text()), nullable=True
        )

    with op.batch_alter_table("interaction", schema=None) as batch_op:
        batch_op.alter_column(
            "edited_data", existing_type=postgresql.JSON(astext_type=sa.Text()), nullable=True
        )

    # ### end Alembic commands ###
