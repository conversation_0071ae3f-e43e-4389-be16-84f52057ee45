services:
  conversational-postgres:
    image: postgres:17.5
    container_name: conversational_postgres
    environment:
      POSTGRES_DB: ${DATABASE_NAME}
      POSTGRES_USER: ${DATABASE_USER}
      POSTGRES_PASSWORD: ${DATABASE_PASSWORD}
    ports:
      - '5432:5432'
    volumes:
      - conversational_postgres_data:/var/lib/postgresql/data

  conversational_backend:
    build:
      context: ./
      dockerfile: Dockerfile
    ports:
      - '8081:8081'
    env_file:
      - .env
    depends_on:
      - conversational-postgres
    volumes:
      - ./src:/app/src

volumes:
  conversational_postgres_data:
