"""Extract and process mixed pages."""

from bs4 import Tag
from ctt_am_ingestions.website_ingestion.web_scraping.extract_and_process.article_pages import (  # noqa: E501
    extract_article_content,
)
from ctt_am_ingestions.website_ingestion.web_scraping.extract_and_process.schematic_pages import (  # noqa: E501
    extract_banner_text,
)
from ctt_am_ingestions.website_ingestion.web_scraping.results import Page


def process_mixed_page(page: Page, banner: Tag, article: Tag) -> Page | None:
    """Process a mixed page and extract relevant data."""
    summary = extract_banner_text(banner)
    sections, summary = extract_article_content(article, summary)
    if not summary and not sections:
        return None
    page.summary, page.sections = summary, sections
    return page
