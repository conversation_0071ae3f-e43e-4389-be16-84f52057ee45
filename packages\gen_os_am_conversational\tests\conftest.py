"""Test configuration and fixtures for the conversational API tests.

This module provides pytest fixtures for testing the conversational API,
including database setup, test client configuration, and environment
variable management for the test environment.
"""

import logging
import os
import sys
from pathlib import Path

import pytest
import pytest_asyncio
from fastapi.testclient import TestClient

# Add the src directory to the Python path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

# Set environment variables BEFORE any imports to ensure they're available
database_file = f"{Path(__file__).parent}/test_database.db"

os.environ["AM_CONV_DATABASE_NAME"] = database_file
os.environ["AM_CONV_DATABASE_TYPE"] = "sqlite"
os.environ["AM_CONV_DATABASE_USER"] = ""
os.environ["AM_CONV_DATABASE_PORT"] = "5432"
os.environ["AM_CONV_DATABASE_PASSWORD"] = ""
os.environ["AM_CONV_DATABASE_HOST"] = ""
os.environ["AM_CONV_API_LISTEN_HOST"] = "localhost"
os.environ["AM_CONV_API_LISTEN_PORT"] = "8081"
os.environ["AM_CONV_API_WORKERS"] = "1"
os.environ["AM_CONV_LOG_LEVEL"] = "DEBUG"

from gen_os_am_conversational.database.session import SessionManager  # noqa: E402
from gen_os_am_conversational.models.models import Base  # noqa: E402
from gen_os_am_conversational.settings import Settings  # noqa: E402
from main import app  # noqa: E402


@pytest.fixture(scope="module")
def client(create_tables, config):
    """Test client fixture with database tables created."""
    logger = logging.getLogger("api")
    logger.level = logging.DEBUG

    with TestClient(app) as client:
        yield client


@pytest_asyncio.fixture(scope="session")
async def config():
    """Configure test environment variables."""
    # Environment variables are already set at module level
    pass


@pytest_asyncio.fixture(scope="session")
async def create_tables(config):
    """Create database tables for testing."""
    async with SessionManager.get_session() as db_session:
        await (await db_session.connection()).run_sync(Base.metadata.create_all)
        yield
        if Path(database_file).exists():
            os.remove(database_file)


@pytest_asyncio.fixture(scope="session")
async def get_session(create_tables):
    """Get database session for testing."""
    return SessionManager.get_session
