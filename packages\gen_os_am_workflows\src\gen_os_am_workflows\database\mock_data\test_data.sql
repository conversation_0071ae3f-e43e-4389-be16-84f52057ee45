-- workflow
INSERT INTO public.workflow VALUES ('b00b423a-ab76-47c4-a724-5effc1771c29', 'read-documents', 'a workflow that read documents', '2025-06-05 07:59:56.717287', '2025-06-05 07:59:56.717287', '[{"field_name": "source", "field_type": "string"}, {"field_name": "documents", "field_type": "number"}]');

-- workflow - steps
INSERT INTO public.step VALUES ('605eb543-0c13-4b47-a599-374d918ac8c3', 'read-source', 'read the source', 'gen_ai', '2025-06-05 08:08:55.67944', 'b00b423a-ab76-47c4-a724-5effc1771c29', 1);
INSERT INTO public.step VALUES ('242a592e-281b-4a8a-a47a-d87f4f65e72c', 'use-tool', 'use the tool', 'tool', '2025-06-05 08:04:02.311818', 'b00b423a-ab76-47c4-a724-5effc1771c29', 2);
INSERT INTO public.step VALUES ('628bc974-41b8-4979-ae93-3d37b21a9683', 'do-action', 'do the action', 'action', '2025-06-05 08:04:57.086258', 'b00b423a-ab76-47c4-a724-5effc1771c29', 3);

-- workflow - steps - inputs
INSERT INTO public.step_input (id, step_id, step_block_type, status, step_block_name, order_number, previous_step_fed, type_extra) VALUES ('1385f177-2056-43c6-8414-4ca2104df5c6', '605eb543-0c13-4b47-a599-374d918ac8c3', 'dataset', 'non_editable', 'input-dataset', 1, '{}', '{}');
INSERT INTO public.step_input (id, step_id, step_block_type, status, step_block_name, order_number, previous_step_fed, type_extra) VALUES ('ed0292e1-a6e8-463c-806b-73c9b74687d8', '605eb543-0c13-4b47-a599-374d918ac8c3', 'text_structured', 'editable', 'input-text-struct', 2, '{}', '{}');
INSERT INTO public.step_input (id, step_id, step_block_type, status, step_block_name, order_number, previous_step_fed, type_extra) VALUES ('148bcf70-1dc4-4266-a615-ff9df75243ec', '242a592e-281b-4a8a-a47a-d87f4f65e72c', 'text_structured', 'editable', 'input-text-struct', 1, '{"step_name": "read-source", "step_block_name": "out-text-struct"}', '{}');
INSERT INTO public.step_input (id, step_id, step_block_type, status, step_block_name, order_number, previous_step_fed, type_extra) VALUES ('d01f2ba0-28fb-4914-a673-6e7bc7418f48', '242a592e-281b-4a8a-a47a-d87f4f65e72c', 'text_unstructured', 'editable', 'input-text-unstruct', 2, '{}', '{}');
INSERT INTO public.step_input (id, step_id, step_block_type, status, step_block_name, order_number, previous_step_fed, type_extra) VALUES ('69a253dd-f0f2-4257-b070-9b2485d9a1d7', '628bc974-41b8-4979-ae93-3d37b21a9683', 'file', 'non_editable', 'input-file', 1, '{}', '{}');
INSERT INTO public.step_input (id, step_id, step_block_type, status, step_block_name, order_number, previous_step_fed, type_extra) VALUES ('ff9d5700-1b3c-41c7-b8ba-a430d99b0693', '628bc974-41b8-4979-ae93-3d37b21a9683', 'selection', 'non_editable', 'input-selection', 2, '{}', '{"options": ["option1", "option2"], "default_value": "option1"}');

-- workflow - steps - outputs
INSERT INTO public.step_output (id, step_id, step_block_type, status, step_block_name, order_number, type_extra) VALUES ('a37ff19c-6730-43a1-8f14-6ec500aabd39', '605eb543-0c13-4b47-a599-374d918ac8c3', 'text_structured', 'editable', 'out-text-struct', 1, '{}');
INSERT INTO public.step_output (id, step_id, step_block_type, status, step_block_name, order_number, type_extra) VALUES ('f40d68bf-f772-4e13-b73b-61f714d5ea03', '242a592e-281b-4a8a-a47a-d87f4f65e72c', 'text_unstructured', 'editable', 'out-text-unstruct', 1, '{}');
INSERT INTO public.step_output (id, step_id, step_block_type, status, step_block_name, order_number, type_extra) VALUES ('adc02cda-4611-4982-b543-d30f02a93573', '628bc974-41b8-4979-ae93-3d37b21a9683', 'tool', 'editable', 'out-tool', 1, '{}');
INSERT INTO public.step_output (id, step_id, step_block_type, status, step_block_name, order_number, type_extra) VALUES ('476d96b9-e866-4c49-ad13-34939e9b5ea3', '628bc974-41b8-4979-ae93-3d37b21a9683', 'text_structured', 'editable', 'out-text', 2, '{}');
INSERT INTO public.step_output (id, step_id, step_block_type, status, step_block_name, order_number, type_extra) VALUES ('5f6e7d8c-9a0b-4c1d-8e2f-3b4c5d6a7b8c', '628bc974-41b8-4979-ae93-3d37b21a9683', 'email', 'editable', 'output-email', 3, '{}');

-- workflow_execution
INSERT INTO public.workflow_execution VALUES ('834acbc8-7659-44d7-8094-2094889447f1', 'b00b423a-ab76-47c4-a724-5effc1771c29', '2025-06-05 10:04:36.829116', 'completed', '[]', '2025-06-05 11:01:23.876082', '2025-06-05 11:01:23.875379');
INSERT INTO public.workflow_execution VALUES ('bc58033a-500e-49de-9ceb-13902a8e2197', 'b00b423a-ab76-47c4-a724-5effc1771c29', '2025-06-05 11:24:07.43215', 'blocked_pending_approval', '[]', '2025-06-05 11:31:53.9515', NULL);
INSERT INTO public.workflow_execution VALUES ('2f9b3f17-334f-4a29-a1b5-804031f09830', 'b00b423a-ab76-47c4-a724-5effc1771c29', '2025-06-05 11:47:02.900374', 'blocked_assistance_needed', '[]', '2025-06-05 11:51:04.406806', NULL);
INSERT INTO public.workflow_execution VALUES ('8557f526-a8d1-4a77-bb40-1b4ed13d3895', 'b00b423a-ab76-47c4-a724-5effc1771c29', '2025-06-05 11:53:36.299913', 'error', '[]', '2025-06-05 14:37:17.812923', '2025-06-05 14:37:17.812086');
INSERT INTO public.workflow_execution VALUES ('0212cc80-4cca-4507-ac57-f208682aa124', 'b00b423a-ab76-47c4-a724-5effc1771c29', '2025-06-05 14:40:02.712635', 'cancelled', '[]', '2025-06-05 14:52:20.538535', '2025-06-05 14:52:20.53832');
INSERT INTO public.workflow_execution VALUES ('8b1bf34f-f496-4f48-a669-030c253102c1', 'b00b423a-ab76-47c4-a724-5effc1771c29', '2025-06-05 14:52:31.813614', 'running', '[]', '2025-06-05 14:52:31.813633', NULL);

-- workflow_execution - step_runs
INSERT INTO public.step_run VALUES ('08658e92-8f6d-4ee6-abde-85faabe08539', '605eb543-0c13-4b47-a599-374d918ac8c3', '2025-06-05 10:20:14.003477', NULL, 'completed', '834acbc8-7659-44d7-8094-2094889447f1', false, '[{"step_block_name": "input-text-struct", "step_block_type": "text_structured", "order_number": 2, "status": "editable", "data": {"text_structured": {"this is an example": "of structured text"}}}, {"step_block_name": "input-dataset", "step_block_type": "dataset", "order_number": 1, "status": "non_editable", "data": {"dataset": [{"additionalProp1": ["123"], "additionalProp2": ["456"], "additionalProp3": ["789"]}]}}]', '[{"step_block_name": "out-text-struct", "step_block_type": "text_structured", "order_number": 1, "status": "editable", "data": {"text_structured": {"this is an example": "of structured text"}}}]');
INSERT INTO public.step_run VALUES ('f8685750-013b-420d-b1e3-265fde9094f6', '242a592e-281b-4a8a-a47a-d87f4f65e72c', '2025-06-05 10:25:43.25297', NULL, 'completed', '834acbc8-7659-44d7-8094-2094889447f1', false, '[{"step_block_name": "input-text-unstruct", "step_block_type": "text_unstructured", "order_number": 2, "status": "editable", "data": {"text_unstructured": "Lorem ipsum dolor sit amet consectetur adipiscing elit. Quisque faucibus ex sapien vitae pellentesque sem placerat."}}, {"step_block_name": "input-text-struct", "step_block_type": "text_structured", "order_number": 1, "status": "editable", "data": {"text_structured": {"Lorem ipsum dolor": "sit amet consectetur adipiscing elit."}}}]', '[{"step_block_name": "out-text-unstruct", "step_block_type": "text_unstructured", "order_number": 1, "status": "editable", "data": {"text_unstructured": "Lorem ipsum dolor sit amet consectetur adipiscing elit. Quisque faucibus ex sapien vitae pellentesque sem placerat."}}]');
INSERT INTO public.step_run VALUES ('fa76092e-ce4d-454f-bd87-c53283909c53', '628bc974-41b8-4979-ae93-3d37b21a9683', '2025-06-05 11:01:23.862266', NULL, 'completed', '834acbc8-7659-44d7-8094-2094889447f1', false, '[{"step_block_name": "input-file", "step_block_type": "file", "order_number": 1, "status": "non_editable", "data": {"files": [{"id": "628bc974-41b8-4979-ae93-3d37b21a9683", "name": "super-file-1"}]}}, {"step_block_name": "input-selection", "step_block_type": "selection", "order_number": 2, "status": "non_editable", "data": {"selection": {"options": ["daredata-is-cool", "daredata-is-super-cool"], "default_value": "daredata-is-super-cool"}}}]', '[{"step_block_name": "out-tool", "step_block_type": "tool", "order_number": 1, "status": "editable", "data": {"tool": {"number_of_calls": 5, "tool_name": "mega-tool-api", "tool_input": "mega-tool-input", "tool_output": [{"chunk_file": "super-file-1", "chunk_content": "best-content-1"}, {"chunk_file": "super-file-2", "chunk_content": "best-content-2"}]}}}, {"step_block_name": "out-text", "step_block_type": "text_structured", "order_number": 2, "status": "editable", "data": {"text_structured": "Lorem ipsum dolor sit amet consectetur adipiscing elit. Quisque faucibus ex sapien vitae pellentesque sem placerat."}}, {"step_block_name": "output-email", "step_block_type": "email", "order_number": 3, "status": "editable", "data": {"email": {"sender": "", "to": [], "subject": "", "body": "", "attachments": []}}}]');
INSERT INTO public.step_run VALUES ('3c7c3bb0-e629-422e-ac8e-83c63babdf5e', '605eb543-0c13-4b47-a599-374d918ac8c3', '2025-06-05 11:24:49.711714', NULL, 'completed', 'bc58033a-500e-49de-9ceb-13902a8e2197', false, '[{"step_block_name": "input-text-struct", "step_block_type": "text_structured", "order_number": 2, "status": "editable", "data": {"text_structured": {"this is an example": "of structured text"}}}, {"step_block_name": "input-dataset", "step_block_type": "dataset", "order_number": 1, "status": "non_editable", "data": {"dataset": [{"additionalProp1": ["123"], "additionalProp2": ["456"], "additionalProp3": ["789"]}]}}]', '[{"step_block_name": "out-text-struct", "step_block_type": "text_structured", "order_number": 1, "status": "editable", "data": {"text_structured": {"this is an example": "of structured text"}}}]');
INSERT INTO public.step_run VALUES ('cd6f65a5-5fc1-42a1-80f2-8d8966d6cde3', '242a592e-281b-4a8a-a47a-d87f4f65e72c', '2025-06-05 11:25:20.582986', NULL, 'completed', 'bc58033a-500e-49de-9ceb-13902a8e2197', false, '[{"step_block_name": "input-text-unstruct", "step_block_type": "text_unstructured", "order_number": 2, "status": "editable", "data": {"text_unstructured": "Lorem ipsum dolor sit amet consectetur adipiscing elit. Quisque faucibus ex sapien vitae pellentesque sem placerat."}}, {"step_block_name": "input-text-struct", "step_block_type": "text_structured", "order_number": 1, "status": "editable", "data": {"text_structured": {"Lorem ipsum dolor": "sit amet consectetur adipiscing elit."}}}]', '[{"step_block_name": "out-text-unstruct", "step_block_type": "text_unstructured", "order_number": 1, "status": "editable", "data": {"text_unstructured": "Lorem ipsum dolor sit amet consectetur adipiscing elit. Quisque faucibus ex sapien vitae pellentesque sem placerat."}}]');
INSERT INTO public.step_run VALUES ('980b4c74-4969-44c5-a83d-d379c70c0402', '628bc974-41b8-4979-ae93-3d37b21a9683', '2025-06-05 11:25:50.043193', NULL, 'blocked_pending_approval', 'bc58033a-500e-49de-9ceb-13902a8e2197', false, '[{"step_block_name": "input-file", "step_block_type": "file", "order_number": 1, "status": "non_editable", "data": {"files": [{"id": "a1b2c3d4-b5a6-4987-b3c2-a1d0e9f8c7b6", "name": "super-file-2"}]}}, {"step_block_name": "input-selection", "step_block_type": "selection", "order_number": 2, "status": "non_editable", "data": {"selection": {"options": ["daredata-is-cool", "daredata-is-super-cool"], "default_value": "daredata-is-super-cool"}}}]', '[{"step_block_name": "out-tool", "step_block_type": "tool", "order_number": 1, "status": "editable", "data": {"tool": {"number_of_calls": 5, "tool_name": "mega-tool-api", "tool_input": "mega-tool-input", "tool_output": [{"chunk_file": "super-file-1", "chunk_content": "best-content-1"}, {"chunk_file": "super-file-2", "chunk_content": "best-content-2"}]}}}, {"step_block_name": "out-text", "step_block_type": "text_structured", "order_number": 2, "status": "editable", "data": {"text_structured": {"this is an example": "of structured text"}}}, {"step_block_name": "output-email", "step_block_type": "email", "order_number": 3, "status": "editable", "data": {"email": {"sender": "", "to": [], "subject": "", "body": "", "attachments": []}}}]');
INSERT INTO public.step_run VALUES ('4705d58c-43f5-42b8-8c7b-e4523990bbbd', '605eb543-0c13-4b47-a599-374d918ac8c3', '2025-06-05 11:48:01.223124', NULL, 'completed', '2f9b3f17-334f-4a29-a1b5-804031f09830', false, '[{"step_block_name": "input-text-struct", "step_block_type": "text_structured", "order_number": 2, "status": "editable", "data": {"text_structured": {"this is an example": "of structured text"}}}, {"step_block_name": "input-dataset", "step_block_type": "dataset", "order_number": 1, "status": "non_editable", "data": {"dataset": [{"additionalProp1": ["123"], "additionalProp2": ["456"], "additionalProp3": ["789"]}]}}]', '[{"step_block_name": "out-text-struct", "step_block_type": "text_structured", "order_number": 1, "status": "editable", "data": {"text_structured": {"this is an example": "of structured text"}}}]');
INSERT INTO public.step_run VALUES ('c08c23a5-53b8-4b4a-8896-0db745e06b58', '242a592e-281b-4a8a-a47a-d87f4f65e72c', '2025-06-05 11:48:11.259451', NULL, 'completed', '2f9b3f17-334f-4a29-a1b5-804031f09830', false, '[{"step_block_name": "input-text-unstruct", "step_block_type": "text_unstructured", "order_number": 2, "status": "editable", "data": {"text_unstructured": "Lorem ipsum dolor sit amet consectetur adipiscing elit. Quisque faucibus ex sapien vitae pellentesque sem placerat."}}, {"step_block_name": "input-text-struct", "step_block_type": "text_structured", "order_number": 1, "status": "editable", "data": {"text_structured": {"Lorem ipsum dolor": "sit amet consectetur adipiscing elit."}}}]', '[{"step_block_name": "out-text-unstruct", "step_block_type": "text_unstructured", "order_number": 1, "status": "editable", "data": {"text_unstructured": "Lorem ipsum dolor sit amet consectetur adipiscing elit. Quisque faucibus ex sapien vitae pellentesque sem placerat."}}]');
INSERT INTO public.step_run VALUES ('9097cc3d-2925-4cd3-af10-43054d43de43', '628bc974-41b8-4979-ae93-3d37b21a9683', '2025-06-05 11:47:44.136998', NULL, 'blocked_assistance_needed', '2f9b3f17-334f-4a29-a1b5-804031f09830', false, '[{"step_block_name": "input-file", "step_block_type": "file", "order_number": 1, "status": "non_editable", "data": {"files": [{"id": "f1e2d3c4-b5a6-4987-b3c2-a1d0e9f8c7b6", "name": "super-file-3"}]}}, {"step_block_name": "input-selection", "step_block_type": "selection", "order_number": 2, "status": "non_editable", "data": {"selection": {"options": ["daredata-is-cool", "daredata-is-super-cool"], "default_value": "daredata-is-super-cool"}}}]', '[{"step_block_name": "out-tool", "step_block_type": "tool", "order_number": 1, "status": "editable", "data": {"tool": {"number_of_calls": 5, "tool_name": "mega-tool-api", "tool_input": "mega-tool-input", "tool_output": [{"chunk_file": "super-file-1", "chunk_content": "best-content-1"}, {"chunk_file": "super-file-2", "chunk_content": "best-content-2"}]}}}, {"step_block_name": "out-text", "step_block_type": "text_structured", "order_number": 2, "status": "editable", "data": {"text_structured": {"this is an example": "of structured text"}}}, {"step_block_name": "output-email", "step_block_type": "email", "order_number": 3, "status": "editable", "data": {"email": {"sender": "", "to": [], "subject": "", "body": "", "attachments": []}}}]');
INSERT INTO public.step_run VALUES ('c49b011f-88ab-479a-9afa-15c59a4447f3', '605eb543-0c13-4b47-a599-374d918ac8c3', '2025-06-05 14:52:56.734476', NULL, 'completed', '8b1bf34f-f496-4f48-a669-030c253102c1', false, '[{"step_block_name": "input-text-struct", "step_block_type": "text_structured", "order_number": 2, "status": "editable", "data": {"text_structured": {"this is an example": "of structured text"}}}, {"step_block_name": "input-dataset", "step_block_type": "dataset", "order_number": 1, "status": "non_editable", "data": {"dataset": [{"additionalProp1": ["123"], "additionalProp2": ["456"], "additionalProp3": ["789"]}]}}]', '[{"step_block_name": "out-text-struct", "step_block_type": "text_structured", "order_number": 1, "status": "editable", "data": {"text_structured": {"this is an example": "of structured text"}}}]');
INSERT INTO public.step_run VALUES ('4ec164d6-589a-410c-9ca6-894f2891c2e0', '242a592e-281b-4a8a-a47a-d87f4f65e72c', '2025-06-05 14:53:09.514554', NULL, 'completed', '8b1bf34f-f496-4f48-a669-030c253102c1', false, '[{"step_block_name": "input-text-unstruct", "step_block_type": "text_unstructured", "order_number": 2, "status": "editable", "data": {"text_unstructured": "Lorem ipsum dolor sit amet consectetur adipiscing elit. Quisque faucibus ex sapien vitae pellentesque sem placerat."}}, {"step_block_name": "input-text-struct", "step_block_type": "text_structured", "order_number": 1, "status": "editable", "data": {"text_structured": {"Lorem ipsum dolor": "sit amet consectetur adipiscing elit."}}}]', '[{"step_block_name": "out-text-unstruct", "step_block_type": "text_unstructured", "order_number": 1, "status": "editable", "data": {"text_unstructured": "Lorem ipsum dolor sit amet consectetur adipiscing elit. Quisque faucibus ex sapien vitae pellentesque sem placerat."}}]');
INSERT INTO public.step_run VALUES ('414b96bd-62f3-4805-a359-4312e91b6fef', '605eb543-0c13-4b47-a599-374d918ac8c3', '2025-06-05 11:54:23.308325', NULL, 'error', '8557f526-a8d1-4a77-bb40-1b4ed13d3895', false, '[{"step_block_name": "input-text-struct", "step_block_type": "text_structured", "order_number": 2, "status": "editable", "data": {"text_structured": {"this is an example": "of structured text"}}}, {"step_block_name": "input-dataset", "step_block_type": "dataset", "order_number": 1, "status": "non_editable", "data": {"dataset": [{"additionalProp1": ["123"], "additionalProp2": ["456"], "additionalProp3": ["789"]}]}}]', '[{"step_block_name": "out-text-struct", "step_block_type": "text_structured", "order_number": 1, "status": "editable", "data": {"text_structured": {"this is an example": "of structured text"}}}]');
INSERT INTO public.step_run VALUES ('201e288c-6bf7-4f50-9a7f-a05e8d44648a', '628bc974-41b8-4979-ae93-3d37b21a9683', '2025-06-05 14:53:22.113772', NULL, 'running', '8b1bf34f-f496-4f48-a669-030c253102c1', false, '[{"step_block_name": "input-file", "step_block_type": "file", "order_number": 1, "status": "non_editable", "data": {"files": [{"id": "d4c3b2a1-9e8f-47b6-a5c4-1b2d3e4f5a6c", "name": "super-file-4"}]}}, {"step_block_name": "input-selection", "step_block_type": "selection", "order_number": 2, "status": "non_editable", "data": {"selection": {"options": ["daredata-is-cool", "daredata-is-super-cool"], "default_value": "daredata-is-super-cool"}}}]', '[{"step_block_name": "out-tool", "step_block_type": "tool", "order_number": 1, "status": "editable", "data": {"tool": {"number_of_calls": 5, "tool_name": "mega-tool-api", "tool_input": "mega-tool-input", "tool_output": [{"chunk_file": "super-file-1", "chunk_content": "best-content-1"}, {"chunk_file": "super-file-2", "chunk_content": "best-content-2"}]}}}, {"step_block_name": "out-text", "step_block_type": "text_structured", "order_number": 2, "status": "editable", "data": {"text_structured": {"this is an example": "of structured text"}}}, {"step_block_name": "output-email", "step_block_type": "email", "order_number": 3, "status": "editable", "data": {"email": {"sender": "", "to": [], "subject": "", "body": "", "attachments": []}}}]');
INSERT INTO public.step_run VALUES ('ad94adf4-2b5e-44a2-ad01-f1ef1d6fd141', '605eb543-0c13-4b47-a599-374d918ac8c3', '2025-06-05 14:40:41.45724', NULL, 'cancelled', '0212cc80-4cca-4507-ac57-f208682aa124', false, '[{"step_block_name": "input-text-struct", "step_block_type": "text_structured", "order_number": 2, "status": "editable", "data": {"text_structured": {"this is an example": "of structured text"}}}, {"step_block_name": "input-dataset", "step_block_type": "dataset", "order_number": 1, "status": "non_editable", "data": {"dataset": [{"additionalProp1": ["123"], "additionalProp2": ["456"], "additionalProp3": ["789"]}]}}]', '[{"step_block_name": "out-text-struct", "step_block_type": "text_structured", "order_number": 1, "status": "editable", "data": {"text_structured": {"this is an example": "of structured text"}}}]');

-- workflow_execution - step_runs - occurrences
INSERT INTO public.occurrence VALUES ('9e521d56-ca1a-437d-944a-40426fb163fd', '980b4c74-4969-44c5-a83d-d379c70c0402', '2025-06-05 11:31:53.93924', 'pending_approval', 'open');
INSERT INTO public.occurrence VALUES ('3f6f5969-668c-4bf9-826c-ea91e6758795', '9097cc3d-2925-4cd3-af10-43054d43de43', '2025-06-05 11:51:04.399682', 'assistance_needed', 'open');
INSERT INTO public.occurrence VALUES ('19f0e2f7-4730-4385-93b0-2fe773fe0637', '414b96bd-62f3-4805-a359-4312e91b6fef', '2025-06-05 12:04:24.187934', 'error', 'open');
INSERT INTO public.occurrence VALUES ('3ef85ceb-9176-483b-b69f-9b28a15e9d23', '414b96bd-62f3-4805-a359-4312e91b6fef', '2025-06-05 12:05:11.397009', 'error', 'open');
INSERT INTO public.occurrence VALUES ('a8a0a087-9e3b-43af-b99f-0ffaf3db34a8', '414b96bd-62f3-4805-a359-4312e91b6fef', '2025-06-05 14:29:28.053099', 'error', 'open');
INSERT INTO public.occurrence VALUES ('b78d3892-19c9-4e7e-80c1-8825b2de1066', '414b96bd-62f3-4805-a359-4312e91b6fef', '2025-06-05 14:31:07.787307', 'error', 'open');
INSERT INTO public.occurrence VALUES ('86b68db8-b156-45f3-9bc1-4b2138ccc704', '414b96bd-62f3-4805-a359-4312e91b6fef', '2025-06-05 14:31:26.219565', 'error', 'open');
INSERT INTO public.occurrence VALUES ('31d86e13-8924-4971-9a0e-fd7f3e3a9f46', '414b96bd-62f3-4805-a359-4312e91b6fef', '2025-06-05 14:32:58.576574', 'error', 'open');
INSERT INTO public.occurrence VALUES ('eb6ff1ac-0851-43fb-9218-cce616cb6c79', '414b96bd-62f3-4805-a359-4312e91b6fef', '2025-06-05 14:33:03.575784', 'error', 'open');
INSERT INTO public.occurrence VALUES ('c45b73be-5d91-4c7a-9af0-92c39568a18e', '414b96bd-62f3-4805-a359-4312e91b6fef', '2025-06-05 14:37:12.372168', 'error', 'open');
INSERT INTO public.occurrence VALUES ('750ea29b-5312-4b39-af6c-367dae8d0eee', 'ad94adf4-2b5e-44a2-ad01-f1ef1d6fd141', '2025-06-05 14:51:32.537111', 'pending_approval', 'unsolved');

-- workflow_execution - step_runs - occurrences - interactions
INSERT INTO public.interaction VALUES ('d603de64-d8ed-4f43-83d5-970fb2d353df', '750ea29b-5312-4b39-af6c-367dae8d0eee', '2025-06-05 14:52:06.841959', '[{}]', 'cancelled', 'Agent crashed.');
