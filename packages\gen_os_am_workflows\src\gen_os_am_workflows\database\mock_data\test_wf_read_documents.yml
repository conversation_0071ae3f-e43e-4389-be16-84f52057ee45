workflow:
  name: 'read-documents'
  description: 'a workflow that read documents'
  extra_fields:
    - field_name: 'source'
      field_type: 'string'
    - field_name: 'documents'
      field_type: 'number'
  steps:
    - name: 'read-source'
      description: 'read the source'
      step_type: 'gen_ai'
      order_number: 1
      inputs:
        - step_block_name: 'input-dataset'
          status: 'non_editable'
          step_block_type: 'dataset'
          order_number: 1
        - step_block_name: 'input-text-struct'
          status: 'non_editable'
          step_block_type: 'text_structured'
          order_number: 2
          type_extra:
            - field_name: 'source'
              field_type: 'string'
            - field_name: 'documents'
              field_type: 'list[string]'
      outputs:
        - step_block_name: 'out-text-struct'
          status: 'editable'
          step_block_type: 'text_structured'
          order_number: 1
          type_extra:
            - field_name: 'source'
              field_type: 'string'
            - field_name: 'documents'
              field_type: 'list[string]'
    - name: 'use-tool'
      description: 'use the tool'
      step_type: 'tool'
      order_number: 2
      inputs:
        - step_block_name: 'input-text-struct'
          status: 'non_editable'
          step_block_type: 'text_structured'
          order_number: 1
          previous_step_fed:
            step_name: 'read-source'
            step_block_name: 'out-text-struct'
        - step_block_name: 'input-text-unstruct'
          status: 'non_editable'
          step_block_type: 'text_unstructured'
          order_number: 2
      outputs:
        - step_block_name: 'out-text-unstruct'
          status: 'editable'
          step_block_type: 'text_unstructured'
          order_number: 1
    - name: 'do-action'
      description: 'do the action'
      step_type: 'action'
      order_number: 3
      inputs:
        - step_block_name: 'input-file'
          status: 'editable'
          step_block_type: 'file'
          order_number: 1
        - step_block_name: 'input-selection'
          status: 'editable'
          step_block_type: 'selection'
          order_number: 2
          type_extra:
            - options: ['op1', 'op2', 'op3']
              default_value: 'op1'
