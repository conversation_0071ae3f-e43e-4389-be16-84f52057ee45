"""Database events for the case management service.

This module contains the events for the database models.
"""

from datetime import datetime

from sqlalchemy import event

from gen_os_am_workflows.database.models import (
    Step,
    StepRun,
    StepRunTerminalStatuses,
    WorkflowExecution,
    WorkflowTerminalStatuses,
)

# Step events


@event.listens_for(Step, "before_insert")
@event.listens_for(Step, "before_update")
def validate_solve_manually_only_for_action_steps(mapper, connection, target):
    """Validate that solve_manually is only set for action steps."""
    if target.step_type != "action" and target.solve_manually is not None and target.solve_manually:
        raise ValueError(
            f"Cannot set solve_manually=True for step type '{target.step_type}'. "
            "Only 'action' steps can have solve_manually set to True."
        )


# WorkflowExecution events


@event.listens_for(WorkflowExecution, "before_update")
def set_finished_at_on_terminal_status(mapper, connection, target):
    """Set finished_at timestamp when workflow reaches a terminal status."""
    # Set finished_at when workflow reaches a terminal status
    if target.status in WorkflowTerminalStatuses and target.finished_at is None:
        target.finished_at = datetime.now()

    # Reset finished_at when workflow returns to a non-terminal status
    if target.status not in WorkflowTerminalStatuses and target.finished_at is not None:
        target.finished_at = None


# StepRun events


@event.listens_for(StepRun, "before_insert")
def prevent_step_run_insert_on_terminal_workflow(mapper, connection, target):
    """Prevent inserting step runs when workflow is in terminal status."""
    if target.workflow_execution.status in WorkflowTerminalStatuses:
        raise ValueError(
            f"Cannot insert step run: Workflow execution {target.workflow_execution_id} "
            f"is in terminal status {target.workflow_execution.status}"
        )


@event.listens_for(StepRun, "before_update")
def prevent_step_run_update_on_terminal_workflow(mapper, connection, target):
    """Prevent updating step runs when workflow is in terminal status."""
    if target.workflow_execution.status in WorkflowTerminalStatuses:
        raise ValueError(
            f"Cannot update step run: Workflow execution {target.workflow_execution_id} "
            f"is in terminal status {target.workflow_execution.status}"
        )


@event.listens_for(StepRun, "before_update")
def set_completed_at_on_terminal_status(mapper, connection, target):
    """Set completed_at timestamp when step run reaches a terminal status."""
    # Set completed_at when step run reaches a terminal status
    if target.status in StepRunTerminalStatuses and target.completed_at is None:
        target.completed_at = datetime.now()

    # Reset completed_at when step run returns to a non-terminal status
    if target.status not in StepRunTerminalStatuses and target.completed_at is not None:
        target.completed_at = None


@event.listens_for(StepRun, "before_delete")
def prevent_step_run_delete_on_terminal_workflow(mapper, connection, target):
    """Prevent deleting step runs when workflow is in terminal status."""
    if target.workflow_execution.status in WorkflowTerminalStatuses:
        raise ValueError(
            f"Cannot delete step run: Workflow execution {target.workflow_execution_id} "
            f"is in terminal status {target.workflow_execution.status}"
        )
