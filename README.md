# Gen OS Agent Manager

A modular architecture for managing AI agents and their interactions with associated knowledge and case management services. This repository contains three main components:

> See full architecture: [Full Gen-OS Platform Architecture](https://docs.google.com/presentation/d/1k8FAmxhXrAtWoiO3bJQIMQtlMAvHpdILTQ1yr-uuuqU/edit?slide=id.g34a52be60ad_0_959#slide=id.g34a52be60ad_0_959)

## Components

### 1. Agent Manager (AM)

The main API service that orchestrates the interaction between different components and manages the overall system.

### 2. Workflows (CM)

Handles case-related operations including:

- Workflow definitions and execution
- Step management
- Occurrence tracking
- User interactions

### 3. Knowledge Management (KM)

Manages knowledge base and document operations including:

- Document storage and retrieval
- Folder organization
- Source management
- Tag management
- User management
- Ingestion pipelines

### 4. Conversational

TBD

## Project Structure

```bash
.
├── deployment/    # Core and custom deployments
├── packages/
│   ├── gen_os_am_workflows/                 # Workflows (Case Management) service
│   └── gen_os_am_knowledge/                 # Knowledge Management service
│   └── gen_os_am_conversational/            # Conversational service
│   └── gen_os_sdk_emulator/                 # SDK Code service
│   └── gen_os_am_core/                      # Core Agent Manager
└── shared/                 # Shared libraries and dependencies
```

## Prerequisites

- Python 3.12+
- Docker and Docker Compose
- PDM (Python dependency manager)
- PostgreSQL (if running locally)

## Quick Start - Agent Manager

1. Clone the repository:

```bash
git clone <repository-url>
cd gen-os-agent-manager
```

1. Set up environment files (see `.env.example` for each of the projects components):
   - Create `.env`

2. Run with Docker:

    ```bash
    cd packages/gen_os_am_core
    pdm run am-docker
    ```

> Refer to `/packages/gen_os_am_core/README.md` to learn more about running the Agent Manager.

## Documentation

Each component has its own detailed documentation in their respective directories:

- Agent Manager: `/packages/gen_os_am_core/README.md`
- Case Management: `/packages/gen_os_am_workflows/README.md`
- Knowledge Management: `/packages/gen_os_am_knowledge/README.md`

## Development

For detailed development instructions, environment setup, and component-specific documentation, please refer to the README files in each component's directory.
