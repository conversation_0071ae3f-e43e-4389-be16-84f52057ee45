"""Tests for the global issues router."""

import time
import uuid
from typing import get_args

import pytest
import pytest_asyncio

from gen_os_am_core.database.models.agent_issue import (
    Issue,
    ReportedIncidentConversation,
    ReportedIncidentWorkflow,
)
from gen_os_am_core.database.models.agents import Agent
from gen_os_am_core.database.session import Session<PERSON>anager
from gen_os_am_core.models.enums import IssueCategory, IssueState


class TestGlobalIssuesRouter:
    """Test cases for the global issues router."""

    @pytest_asyncio.fixture
    async def create_multiple_test_agents(self):
        """Create multiple test agents."""
        agents_data = [
            {"id": "test-agent-1", "name": "Agent One", "url": "http://agent1"},
            {"id": "test-agent-2", "name": "Agent Two", "url": "http://agent2"},
        ]
        async with SessionManager.get_session() as db:
            agents = [Agent(**data) for data in agents_data]
            db.add_all(agents)
            await db.commit()
        yield agents

    @pytest_asyncio.fixture
    async def create_multiple_issues(self, create_multiple_test_agents):
        """Create a variety of issues for testing list endpoints."""
        agent1_id = create_multiple_test_agents[0].id
        agent2_id = create_multiple_test_agents[1].id

        issues_data = [
            # Open issues
            {
                "agent_id": agent1_id,
                "description": "Agent one has a critical bug",
                "issue_type": "GenAI",
                "state": "open",
            },
            {
                "agent_id": agent2_id,
                "description": "Agent two is making things up",
                "issue_type": "GenAI",
                "state": "open",
            },
            # Closed issues
            {
                "agent_id": agent1_id,
                "description": "A minor typo was fixed",
                "issue_type": "Other",
                "state": "closed_fixed",
            },
        ]

        async with SessionManager.get_session() as db:
            issues = []
            for data in issues_data:
                issue = Issue(**data)
                db.add(issue)
                issues.append(issue)
                # Ensure created_at is distinct for sorting tests
                time.sleep(0.01)

            await db.commit()

            # Add incidents to test sorting by incident count
            # Give the first issue 2 incidents
            issue1_id = issues[0].id
            db.add_all(
                [
                    ReportedIncidentConversation(
                        issue_id=issue1_id,
                        description="conv1",
                        severity="high",
                        conversation_id=str(uuid.uuid4()),
                    ),
                    ReportedIncidentWorkflow(
                        issue_id=issue1_id,
                        description="wf1",
                        severity="high",
                        workflow_execution_id=str(uuid.uuid4()),
                    ),
                ]
            )
            # Give the second issue 1 incident
            issue2_id = issues[1].id
            db.add(
                ReportedIncidentConversation(
                    issue_id=issue2_id,
                    description="conv2",
                    severity="medium",
                    conversation_id=str(uuid.uuid4()),
                )
            )
            await db.commit()

        yield issues

    @pytest.mark.asyncio
    async def test_list_issues_empty(self, client):
        """Test listing issues when none exist."""
        response = client.get("/issues/core/v1/issues?is_open=true")
        assert response.status_code == 200
        data = response.json()
        assert data["items"] == []
        assert data["pagination"]["total_items"] == 0

    @pytest.mark.asyncio
    async def test_list_issues_paginated(self, client, create_multiple_issues):
        """Test basic listing and pagination headers."""
        response = client.get("/issues/core/v1/issues?is_open=true&limit=1")
        assert response.status_code == 200
        data = response.json()

        assert len(data["items"]) == 1
        assert data["pagination"]["total_items"] == 2
        assert response.headers["X-Total-Count"] == "2"

    @pytest.mark.asyncio
    async def test_list_issues_filter_by_state(self, client, create_multiple_issues):
        """Test the is_open=true and is_open=false filters."""
        # Check open issues
        response_open = client.get("/issues/core/v1/issues?is_open=true")
        assert response_open.status_code == 200
        data_open = response_open.json()
        assert len(data_open["items"]) == 2
        assert all(item["state"] == "open" for item in data_open["items"])

        # Check closed issues
        response_closed = client.get("/issues/core/v1/issues?is_open=false")
        assert response_closed.status_code == 200
        data_closed = response_closed.json()
        assert len(data_closed["items"]) == 1
        assert all(item["state"] != "open" for item in data_closed["items"])

    @pytest.mark.asyncio
    async def test_list_issues_search(self, client, create_multiple_issues):
        """Test full-text search across multiple columns."""
        response = client.get("/issues/core/v1/issues?search=critical bug")
        assert response.status_code == 200
        data = response.json()
        assert len(data["items"]) == 1
        assert data["items"][0]["description"] == "Agent one has a critical bug"

    @pytest.mark.asyncio
    async def test_list_issues_search_by_column(self, client, create_multiple_issues):
        """Test targeted search on description and issue_type."""
        # Search by description
        response = client.get(
            "/issues/core/v1/issues?search=making things up&search_column=description"
        )
        assert response.status_code == 200
        data = response.json()
        assert len(data["items"]) == 1
        assert data["items"][0]["description"] == "Agent two is making things up"

        # Search by issue_type
        response = client.get(
            "/issues/core/v1/issues?search=Other&search_column=issue_type&is_open=false"
        )
        assert response.status_code == 200
        data = response.json()
        assert len(data["items"]) == 1
        assert data["items"][0]["issue_type"] == "Other"

    @pytest.mark.asyncio
    async def test_list_issues_sorting(self, client, create_multiple_issues):
        """Test sorting by created_at and incidents."""
        # Sort by created_at ascending (oldest first)
        response = client.get(
            "/issues/core/v1/issues?is_open=true&sort_field=created_at&sort_order=asc"
        )
        assert response.status_code == 200
        data = response.json()
        assert [item["description"] for item in data["items"]] == [
            "Agent one has a critical bug",
            "Agent two is making things up",
        ]

        # Sort by incidents descending (most incidents first)
        response = client.get(
            "/issues/core/v1/issues?is_open=true&sort_field=incidents&sort_order=desc"
        )
        assert response.status_code == 200
        data = response.json()
        assert [item["incidents"] for item in data["items"]] == [2, 1]

    @pytest.mark.asyncio
    async def test_get_issue_states(self, client):
        """Test the /issue-state endpoint."""
        response = client.get("/issues/core/v1/issue-state")
        assert response.status_code == 200
        assert response.json() == list(get_args(IssueState))

    @pytest.mark.asyncio
    async def test_get_issue_types(self, client):
        """Test the /issue-types endpoint."""
        response = client.get("/issues/core/v1/issue-types")
        assert response.status_code == 200
        assert response.json() == list(get_args(IssueCategory))
