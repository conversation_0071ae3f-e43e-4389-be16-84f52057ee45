# Gen OS Agent Manager

This repository contains the Gen OS Agent Manager application, which consists of three main modules:

- **Agent Manager (AM)**: The main API service
- **Workflows (aka Case Management  - CM)**: Handles workflow/case-related operations
- **Knowledge (aka Knowledge Management- KM)**: Manages knowledge base and document operations
- **Conversational (aka Activity)**: Handles conversations and OTel logs from agents

## Prerequisites

- Docker and Docker Compose
- Git
- PostgreSQL (if running locally without Docker)
- PDM

## Environment Configuration

- `.env`

You can find an example at `.env.example`

> The environment file is automatically handled (when you run the application using PDM) to work both for source and for docker, if you need to change some values (eg: database names or passwords) please take a look at how this is handled on `scripts/update_env.py`.

## Required Environment Variables

### General Database Settings

```bash
AM_CORE_DATABASE_HOST=postgres  # Use 'localhost' for local development
AM_CORE_DATABASE_PORT=5432
AM_CORE_DATABASE_USER=adminuser
AM_CORE_DATABASE_PASSWORD=adminpass
AM_CORE_DATABASE_NAME=gen_os_agent_manager_db
```

### Case Management (CM) Settings

```bash
AM_WF_DATABASE_NAME=case_management_db
AM_WF_DATABASE_USER=adminuser
AM_WF_DATABASE_PASSWORD=adminpass
AM_WF_DATABASE_HOST=postgres  # Use 'localhost' for local development
AM_WF_DATABASE_PORT=5432
AM_WF_CLOUD_PROVIDER=gcp
```

### Knowledge Management (KM) Settings

```bash
AM_KM_DATABASE_NAME=knowledge_management_db
AM_KM_DATABASE_USER=adminuser
AM_KM_DATABASE_PASSWORD=adminpass
AM_KM_DATABASE_HOST=postgres  # Use 'localhost' for local development
AM_KM_DATABASE_PORT=5432
AM_KM_LOCAL_STORAGE_PATH=/app/storage/km
AM_KM_ENVIRONMENT=DEV
```

> Be considerate that this list is not extensive and the application will fail with a Pydantic validation error if there's an environment variable missing.

## Running with Docker

1. Clone the repository:

    ```bash
        git clone <repository-url>
        cd gen-os-agent-manager/packages/gen_os_am_core
    ```

2. Create a `.env` file with the required environment variables.
   - Make sure to use `postgres` as the database host
   - Replace placeholder values with actual credentials and settings if needed

3. Start the application using Docker Compose:

   ```bash
      pdm run am-docker
   ```

   This will:
   - Build the Docker images
   - Create and start the containers
   - Run database migrations
   - Start the API service

4. Access the API at http://localhost:8080
   - API documentation is available at http://localhost:8080/docs

## Running from source

1. Clone the repository:

   ```bash
   git clone <repository-url>
   cd gen-os-agent-manager/packages/gen_os_am_core
   ```

2. Create a `.env` file with the required environment variables.
   - Use `localhost` as the database host
   - Replace placeholder values with actual credentials and settings

3. Set up and activate a virtual environment:

   ```bash
   pdm venv create --name agent-manager 3.12
   pdm use # pick the right interpreter
   eval $(pdm venv activate)
   ```

4. Install dependencies:

   ```bash
   pdm install -G:all # install all groups (for development only)
   ```

5. Start the application:

   ```bash
   pdm run am-source
   ```

   This will:
   - Start the database
   - Run database migrations
   - Start the API service

6. Access the API at http://localhost:8080
   - API documentation is available at http://localhost:8080/docs

## Project Structure

```bash
gen-os-agent-manager/
└── packages/
    └── gen_os_am_core/
        ├── compose/
        │   └── postgres/        # PostgreSQL Docker configuration
        ├── scripts/             # Utility scripts
        ├── src/
        │   └── gen_os_am_core/
        │       └── main.py      # Main application entry point
        ├── .env                 # Environment variables
        ├── docker-compose.yml   # Docker Compose configuration
        ├── Dockerfile           # Docker image definition
        ├── entrypoint.sh        # Container entrypoint script
        └── pyproject.toml       # Python dependencies
```

## Troubleshooting

### Database Connection Issues

- When running with Docker, ensure the database host is set to `postgres` in `.env`
- When running locally, ensure the database host is set to `localhost` in `.env`

### Missing Environment Variables

- Check the error logs for missing required variables
- Ensure all required variables are properly defined in the appropriate environment file

### Container Startup Issues

- Check that the PostgreSQL container is healthy before starting the application
- Verify the database initialization scripts in `compose/postgres/scripts/`

### Hash mismatch after pdm install
- Make sure all underlying packages wheels are in `shared/lib` and match version in `pyproject.toml`
- Rebuild wheels and regenrate lock file

```bash
   rm pdm.lock # delete old lock file
   pdm cache clear # delete cache
   pdm build-deps # build and copy underlying wheels
   pdm install -G:all # install all groups (for development only)
   ```
