"""Change agent configuration IDs from UUID to String.

Revision ID: d765fbbc99b0
Revises: 8f5c9ab24907
Create Date: 2025-06-30 17:19:35.974434

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "d765fbbc99b0"
down_revision: str | None = "8f5c9ab24907"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Upgrade database schema to change agent configuration IDs from UUID to String."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("agent_configuration", schema=None) as batch_op:
        batch_op.alter_column(
            "id", existing_type=sa.UUID(), type_=sa.String(), existing_nullable=False
        )
        batch_op.alter_column(
            "agent_id",
            existing_type=sa.UUID(),
            type_=sa.String(),
            existing_nullable=False,
        )

    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade database schema to change agent configuration IDs back to UUID."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("agent_configuration", schema=None) as batch_op:
        batch_op.alter_column(
            "agent_id",
            existing_type=sa.String(),
            type_=sa.UUID(),
            existing_nullable=False,
        )
        batch_op.alter_column(
            "id", existing_type=sa.String(), type_=sa.UUID(), existing_nullable=False
        )

    # ### end Alembic commands ###
