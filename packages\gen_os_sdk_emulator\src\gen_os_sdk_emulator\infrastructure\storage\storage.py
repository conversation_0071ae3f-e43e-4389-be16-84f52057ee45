"""Abstract storage interface and implementations for file storage.

This module provides a storage abstraction layer to decouple the application
from specific cloud provider implementations.
"""

import abc
import os
import uuid
from datetime import UTC, datetime, timedelta

from pydantic import BaseModel


class FileMetadata(BaseModel):
    """Metadata for a file in storage."""

    id: uuid.UUID
    file_name: str
    storage_path: str
    content_type: str | None = None


class UploadInfo(BaseModel):
    """Information needed to upload a file."""

    file_id: uuid.UUID
    upload_url: str


class DownloadInfo(BaseModel):
    """Information needed to download/view a file."""

    file_id: uuid.UUID
    download_url: str
    file_name: str


class StorageInterface(abc.ABC):
    """Abstract interface for storage operations."""

    @abc.abstractmethod
    async def generate_upload_url(self, file_metadata: FileMetadata) -> UploadInfo:
        """Generate a URL for uploading a file.

        Args:
            file_metadata: Metadata for the file to upload

        Returns:
            UploadInfo: Contains the URL for uploading and the file ID

        """
        pass

    @abc.abstractmethod
    async def generate_download_url(self, file_metadata: FileMetadata) -> DownloadInfo:
        """Generate a URL for downloading/viewing a file.

        Args:
            file_metadata: Metadata for the file to download

        Returns:
            DownloadInfo: Contains the URL for downloading and file information

        """
        pass

    @abc.abstractmethod
    def get_storage_path(self, file_id: uuid.UUID, file_name: str) -> str:
        """Get the storage path for a file.

        Args:
            file_id: The unique ID of the file
            file_name: The original file name

        Returns:
            str: The path where the file should be stored

        """
        pass


class GCPStorageService(StorageInterface):
    """Google Cloud Platform implementation of the storage interface."""

    TARGET_SCOPES = "https://www.googleapis.com/auth/cloud-platform"

    def __init__(
        self,
        bucket_name: str,
        project_name: str | None = None,
        service_account_email: str | None = None,
    ):
        """Initialize the GCP storage service.

        Args:
            bucket_name: The name of the GCS bucket to use
            project_name: The name of the GCP project to use
            service_account_email: The email of the service account to use

        """
        self.bucket_name = bucket_name
        self.project_name = project_name
        self.service_account_email = service_account_email

    def _get_impersonated_credentials(self, source_credentials):
        """Get impersonated credentials for the service account."""
        from google.auth import impersonated_credentials

        credentials = impersonated_credentials.Credentials(
            source_credentials=source_credentials,
            target_principal=self.service_account_email,
            target_scopes=self.TARGET_SCOPES,
            lifetime=2,
        )
        return credentials

    async def generate_upload_url(self, file_metadata: FileMetadata) -> UploadInfo:
        """Generate a signed URL for uploading to Google Cloud Storage.

        Args:
            file_metadata: Metadata for the file to upload

        Returns:
            UploadInfo: Contains the signed URL and file ID

        """
        from google import auth
        from google.cloud import storage

        # Initialize GCS client
        storage_client = storage.Client(project=self.project_name)
        bucket = storage_client.bucket(self.bucket_name)
        blob = bucket.blob(file_metadata.storage_path)

        # Generate signed URL
        credentials, _ = auth.default(scopes=[self.TARGET_SCOPES])
        credentials.refresh(auth.transport.requests.Request())

        signing_args = {
            "version": "v4",
            "expiration": datetime.now(UTC) + timedelta(minutes=1),
            "method": "PUT",
            "content_type": file_metadata.content_type,
        }

        if not hasattr(credentials, "service_account_email") or not hasattr(
            credentials, "token"
        ):
            credentials = self._get_impersonated_credentials(credentials)
            signing_args["credentials"] = credentials
        else:
            signing_args["service_account_email"] = credentials.service_account_email
            signing_args["access_token"] = credentials.token

        signed_url = blob.generate_signed_url(**signing_args)

        return UploadInfo(file_id=file_metadata.id, upload_url=signed_url)

    async def generate_download_url(self, file_metadata: FileMetadata) -> DownloadInfo:
        """Generate a signed URL for downloading from Google Cloud Storage.

        Args:
            file_metadata: Metadata for the file to download

        Returns:
            DownloadInfo: Contains the signed URL and file information

        """
        from google import auth
        from google.cloud import storage

        # Generate signed URL for viewing
        storage_client = storage.Client(project=self.project_name)
        bucket = storage_client.bucket(self.bucket_name)
        blob = bucket.blob(file_metadata.storage_path)

        credentials, _ = auth.default(scopes=[self.TARGET_SCOPES])
        credentials.refresh(auth.transport.requests.Request())

        signing_args = {
            "version": "v4",
            "expiration": datetime.now(UTC) + timedelta(minutes=15),
            "method": "GET",
            "content_type": file_metadata.content_type,
        }

        if not hasattr(credentials, "service_account_email") or not hasattr(
            credentials, "token"
        ):
            credentials = self._get_impersonated_credentials(credentials)
            signing_args["credentials"] = credentials
        else:
            signing_args["service_account_email"] = credentials.service_account_email
            signing_args["access_token"] = credentials.token

        url = blob.generate_signed_url(**signing_args)

        return DownloadInfo(
            file_id=file_metadata.id,
            download_url=url,
            file_name=file_metadata.file_name,
        )

    def get_storage_path(self, file_id: uuid.UUID, file_name: str) -> str:
        """Create a storage path for the file based on its ID and extension.

        Args:
            file_id: The unique ID of the file
            file_name: The original file name

        Returns:
            str: The path where the file will be stored

        """
        file_extension = os.path.splitext(file_name)[1]
        return f"files/{file_id}{file_extension}"
