"""Make block name unique within it's input/output.

Revision ID: 37d427c5993c
Revises: 1aa2a428c535
Create Date: 2025-06-02 14:07:42.778635

"""

from collections.abc import Sequence

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "37d427c5993c"
down_revision: str | None = "1aa2a428c535"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Make block name unique within it's input/output."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("step_input", schema=None) as batch_op:
        batch_op.create_unique_constraint(
            "unique_step_input_block_name", ["step_id", "step_block_name"]
        )

    with op.batch_alter_table("step_output", schema=None) as batch_op:
        batch_op.create_unique_constraint(
            "unique_step_output_block_name", ["step_id", "step_block_name"]
        )

    # ### end Alembic commands ###


def downgrade() -> None:
    """Make block name not unique within it's input/output."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("step_output", schema=None) as batch_op:
        batch_op.drop_constraint("unique_step_output_block_name", type_="unique")

    with op.batch_alter_table("step_input", schema=None) as batch_op:
        batch_op.drop_constraint("unique_step_input_block_name", type_="unique")

    # ### end Alembic commands ###
