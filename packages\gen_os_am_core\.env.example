# Gen OS Agent Manager
## Database - General
AM_CORE_DATABASE_USER=gen_os_agent_manager
AM_CORE_DATABASE_PORT=5432
AM_CORE_DATABASE_PASSWORD=somepass
AM_CORE_DATABASE_HOST=postgres
AM_CORE_DATABASE_NAME=gen_os_agent_manager_db
AM_CORE_DATABASE_PORT=5432

AM_KM_DATABASE_NAME=knowledge_management_db
AM_KM_DATABASE_USER=gen_os_agent_manager
AM_KM_DATABASE_PORT=5432
AM_KM_DATABASE_PASSWORD=somepass
AM_KM_DATABASE_HOST=postgres

AM_WF_DATABASE_NAME=case_management_db
AM_WF_DATABASE_USER=gen_os_agent_manager
AM_WF_DATABASE_PORT=5432
AM_WF_DATABASE_PASSWORD=somepass
AM_WF_DATABASE_HOST=postgres


AM_CONV_DATABASE_NAME=conversational_db
AM_CONV_DATABASE_USER=gen_os_agent_manager
AM_CONV_DATABASE_PORT=5432
AM_CONV_DATABASE_PASSWORD=somepass
AM_CONV_DATABASE_HOST=postgres

# Case Management
## Cloud Provider
AM_WF_CLOUD_PROVIDER=gcp
GOOGLE_APPLICATION_CREDENTIALS_LOCAL_PATH=/home/<USER>/.config/gcloud/application_default_credentials.json
## Interaction Notification
AM_WF_INTERACTION_NOTIFICATION_WEBHOOK_URLS='["http://localhost:8080"]' # This needs to be the AM url
## GCP
AM_WF_GCP_BUCKET_NAME=placeholder-bucket-name
AM_WF_GCP_SERVICE_ACCOUNT_EMAIL=placeholder-service-account-email
AM_WF_GCP_PROJECT_NAME=placeholder-project-name

# Knowledge Management

## General
AM_KM_ENVIRONMENT="QUA"
AM_KM_LOG_LEVEL="INFO"

## Only one type of connectopr needed (ADLS the only one working with presigned urls now)
LOCAL_STORAGE_PATH="path"

## Azure (ADLS)
AM_KM_AZURE_STORAGE_ACCOUNT_CONNECTION_STRING="string"
AM_KM_ROOT_BUCKET="bucket"
AM_KM_PRESIGNED_URL_EXPIRY_MINUTES=1

## API
AM_KM_API_PORT=8088
AM_KM_API_HOST=localhost
AM_KM_API_PREFIX=""
### API Optional
AM_KM_ALLOWED_ORIGINS='["*"]'
AM_KM_API_WORKERS=4
AM_KM_API_RELOAD=false

## Knowledge Management ingestion
AM_KM_INGESTION_DRAFT_MODE=false

## Knowledge Management Agent URL
AM_KM_AGENT_API_URL="http://localhost:8084"
