"""Populate the database with mock data."""

import asyncio
import os
import sys
from pathlib import Path

import psycopg
from dotenv import load_dotenv

if sys.platform.startswith("win"):
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())


def _get_prefixed_env_var(var_name: str, default: str) -> str:
    """Get the prefixed variable if exists, else return the original variable name."""
    return os.getenv(f"AM_WF_{var_name}", os.getenv(var_name, default))


async def populate_db():
    """Execute the SQL statements from test_data.sql to populate the database."""
    # Load environment variables
    dotenv_path = Path(__file__).parent.parent / ".env"
    load_dotenv(dotenv_path=dotenv_path)

    # Get database connection parameters from environment variables
    db_host = _get_prefixed_env_var("DATABASE_HOST", "localhost")
    db_port = _get_prefixed_env_var("DATABASE_PORT", "5432")
    db_name = _get_prefixed_env_var("DATABASE_NAME", "cm")
    db_user = _get_prefixed_env_var("DATABASE_USER", "postgres")
    db_password = _get_prefixed_env_var("DATABASE_PASSWORD", "postgres")

    # Create the database connection using psycopg async
    conn = await psycopg.AsyncConnection.connect(
        host=db_host, port=db_port, dbname=db_name, user=db_user, password=db_password
    )

    try:
        # Read the SQL file
        sql_file_path = (
            Path(__file__).parent.parent / "src" / "cm" / "database" / "mock_data" / "test_data.sql"
        )
        with open(sql_file_path) as f:
            sql_content = f.read()

        # Split the content into individual statements
        # We split on semicolons but ignore those within parentheses
        statements = []
        current_statement = []
        paren_count = 0

        for line in sql_content.split("\n"):
            line = line.strip()
            if not line or line.startswith("--"):  # Skip empty lines and comments
                continue

            current_statement.append(line)
            paren_count += line.count("(") - line.count(")")

            if paren_count == 0 and line.endswith(";"):
                statements.append(" ".join(current_statement))
                current_statement = []

        # Execute each statement using cursor
        print("Executing mock data population...")  # noqa: T201
        async with conn.cursor() as cursor:
            for statement in statements:
                try:
                    await cursor.execute(statement)
                except Exception as e:
                    print(f"Error executing statement: {e}")  # noqa: T201
                    print(f"Statement: {statement}")  # noqa: T201
                    raise

        # Commit the transaction
        await conn.commit()
        print("Mock data population completed successfully!")  # noqa: T201

    finally:
        # Close the connection
        await conn.close()


def main():
    """Run the database population script."""
    asyncio.run(populate_db())


if __name__ == "__main__":
    main()
