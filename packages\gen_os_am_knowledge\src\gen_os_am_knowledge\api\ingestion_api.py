"""Module for the Ingestion API."""

import logging
import uuid
from datetime import datetime

from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from gen_os_am_knowledge.api.utils import error_handling_decorator
from gen_os_am_knowledge.config.logger_config import get_api_logger
from gen_os_am_knowledge.sdk.models import Ingestion
from gen_os_am_knowledge.sdk.operations import IngestionService
from gen_os_am_knowledge.sdk.operations.utils import NOT_PROVIDED


class IngestionUpdateModel(BaseModel):
    """Ingestion update model."""

    description: str | type[NOT_PROVIDED] = NOT_PROVIDED
    status: Ingestion._ING_STATUS_TYPE | type[NOT_PROVIDED] = NOT_PROVIDED
    source_id: uuid.UUID | type[NOT_PROVIDED] = NOT_PROVIDED
    last_run_date: datetime | None | type[NOT_PROVIDED] = NOT_PROVIDED
    last_success_date: datetime | None | type[NOT_PROVIDED] = NOT_PROVIDED
    meta: dict | None | type[NOT_PROVIDED] = NOT_PROVIDED
    error: str | None | type[NOT_PROVIDED] = NOT_PROVIDED


class CreateIngestionModel(BaseModel):
    """Create ingestion model."""

    source_id: uuid.UUID
    description: str
    folder_id: uuid.UUID
    meta: str | None = None


class IngestionAPI:
    """Ingestion API class to handle ingestion related operations.

    Args:
        prefix (str, optional): Prefix for the ingestion API. Defaults to "".

    Methods:
        get_router: Returns the FastAPI router for the ingestion API

    """

    def __init__(
        self,
        prefix: str = "",
        logger: logging.Logger = get_api_logger(),
    ):
        """Initialize the IngestionAPI."""
        self.prefix = prefix
        self.ingestion_service = IngestionService(
            logger=logger.getChild("sdk"),
        )

    def get_router(self) -> APIRouter:
        """Return the FastAPI router for the ingestion API.

        Returns:
            APIRouter: The FastAPI router for the ingestion API

        """
        router = APIRouter(prefix=self.prefix + "/ingestion")

        @router.post("/")
        @error_handling_decorator
        async def create_ingestion(
            create_model: CreateIngestionModel,
        ) -> JSONResponse:
            """Create a new ingestion.

            Args:
                create_model (CreateIngestionModel): Ingestion creation model as json
                    Ex:
                    {
                        "source_id": "new_source_id",
                        "description": "new_description",
                        "folder_id": "new_folder_id",
                        "meta": {
                            "key1": "value1",
                            "key2": "value2"
                        }
                    }

            Returns:
                JSONResponse: JSON response with the created ingestion

            """
            result = await self.ingestion_service.create_ingestion(
                **create_model.model_dump()
            )
            return JSONResponse(content=result.model_dump(mode="json"), status_code=201)

        @router.delete("/{ingestion_id}")
        @error_handling_decorator
        async def delete_ingestion(ingestion_id: uuid.UUID) -> Ingestion | None:
            """Delete a ingestion.

            Args:
                ingestion_id (uuid.UUID): Ingestion ID to delete

            Returns:
                Ingestion: Deleted ingestion instance

            """
            result = await self.ingestion_service.delete_ingestion(ingestion_id)
            if result.success:
                return result.deleted_instance
            else:
                raise HTTPException(status_code=404, detail=result.error)

        @router.get("/{ingestion_id}")
        @error_handling_decorator
        async def get_ingestion(ingestion_id: uuid.UUID) -> Ingestion | None:
            """Get a ingestion.

            Args:
                ingestion_id (uuid.UUID): Ingestion ID to get

            Returns:
                JSONResponse: JSON response with the ingestion

            """
            result = await self.ingestion_service.get_ingestion(ingestion_id)
            return result

        @router.get("/")
        @error_handling_decorator
        async def search_ingestions(
            description: str | None = None,
            status: Ingestion._ING_STATUS_TYPE | None = None,
            source_id: uuid.UUID | None = None,
            offset: int = 0,
            limit: int = 20,
        ) -> list[Ingestion]:
            """Search ingestions.

            Args:
                description (str, optional): Ingestion description to search
                status (Ingestion._ING_STATUS_TYPE, optional):
                    Ingestion status to search
                source_id (uuid.UUID, optional): Source ID to search
                offset (int, optional): Offset for pagination. Defaults to 0.
                limit (int, optional): Limit for pagination. Defaults to 20.

            Returns:
                list[Ingestion]: List of ingestions

            """
            result = await self.ingestion_service.search_ingestions(
                description=description,
                status=status,
                source_id=source_id,
                offset=offset,
                limit=limit,
            )
            return result

        @router.patch("/{ingestion_id}")
        @error_handling_decorator
        async def update_ingestion(
            ingestion_id: uuid.UUID,
            update_model: IngestionUpdateModel,
        ):
            """Update a ingestion.

            Args:
                ingestion_id (uuid.UUID): Ingestion ID to update
                update_model (IngestionUpdateModel): Ingestion update model as json
                    Ex:
                    {
                        "description": "new_description",
                        "status": "new_status",
                        "source_id": "new_source_id",
                        "last_run_date": "new_last_run_date",
                        "last_success_date": "new_last_success_date",
                        "meta": {
                            "key1": "value1",
                            "key2": "value2"
                        },
                        "error": "new_error"
                    }

            Returns:
                UpdateResult: Update result with the updated ingestion instance

            """
            result = await self.ingestion_service.update_ingestion(
                ingestion_id, **update_model.model_dump()
            )
            return result

        return router
