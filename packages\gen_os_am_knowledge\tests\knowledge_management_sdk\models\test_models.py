"""Tests for the Document model and related models."""

# type: ignore
import datetime

from gen_os_am_knowledge.sdk.models import (
    Document,
    Folder,
    Ingestion,
    Source,
    Tag,
    User,
)


def test_document():
    """Test creation of Document and related models with various fields."""
    create_date = datetime.datetime.fromisoformat("2024-01-02T03:04:05.678")
    modified_date = datetime.datetime.fromisoformat("2025-01-02T03:04:05.678")
    deleted_date = datetime.datetime.fromisoformat("2026-01-02T03:04:05.678")

    user_create = User(name="C", external_id="C_Ext")
    user_modify = User(name="M", external_id="M_Ext")
    parent_folder = Folder(name="PARENT_FOLDER")

    folder = Folder(name="FOLDER", parent=parent_folder)
    source = Source(name="default", type="internal", meta=None)
    ingestion = Ingestion(description="Some Ingestion", source=source, meta=None)
    tags = [Tag(tag="tag_a"), Tag(tag="tag_b"), Tag(tag="tag_c")]
    Document(
        created_by=user_create,
        created_date=create_date,
        deleted_date=deleted_date,
        file_path="somepath.txt",
        folder=folder,
        meta={"someparam": 2},
        modification_date=modified_date,
        last_modified_by=user_modify,
        original_path="originalpath",
        size=128,
        ingestion=ingestion,
        document_status="Pending",
        tags=tags,
        type=".txt",
    )
