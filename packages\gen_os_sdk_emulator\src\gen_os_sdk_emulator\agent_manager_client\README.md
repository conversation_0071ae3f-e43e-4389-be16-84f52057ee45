# Agent Manager Client

A Python client for interacting with the Agent Manager service, providing access to Knowledge Management (KM) and Case Management (CM) APIs.

## Installation

```bash
pip install gen-os-sdk-emulator
```

## Quick Start

```python
from gen_os_sdk_emulator.agent_manager_client import (
    AgentManagerClient,
    LocalAuthProvider,
)

# Initialize the client
auth_provider = LocalAuthProvider()
client = AgentManagerClient(auth_provider, "http://0.0.0.0:8080")

# Check agent manager status
status = await client.get_agent_manager_status()
```

## Features

### Knowledge Management (KM)

Access the Knowledge Management API through the `km` property:

```python
# Create a tag
tag = await client.gen_os_am_knowledge.create_tag("my-tag")

# Other KM operations available through client.km
```

### Case Management (CM)

Access the Case Management API through the `cm` property:

```python
# Create a workflow
workflow = await client.gen_os_am_workflows.create_workflow(
    name="my-workflow",
    description="My workflow description"
)

# Other CM operations available through client.cm
```

## Authentication

The client supports different authentication providers. Currently implemented:

- `LocalAuthProvider`: For local development and testing
- `GoogleAuthProvider`: GCP authentication

## Examples

See the `examples` directory for more detailed usage examples, including:

- Basic usage example (`basic_example.ipynb`)
