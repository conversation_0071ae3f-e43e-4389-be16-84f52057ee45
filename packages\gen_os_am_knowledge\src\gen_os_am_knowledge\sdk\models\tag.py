"""Module with tag data model."""

import uuid

import regex
from sqlalchemy.orm import validates
from sqlmodel import Field, Relationship, SQLModel

_tag_regex_pattern = regex.compile(r"^[\p{Ll}_\-0-9]+$")
# regex that matches unicode lower case letters, numbers, _ and -


class DocumentTags(SQLModel, table=True):
    """A N:N Link Table.

    You never need to use or create this it is automatically managed by the ORM.
    Used to define the relationships between Documents and Tags.
    """

    __tablename__ = "document_tags"  # type: ignore
    __table_args__ = {"extend_existing": True}

    document_id: uuid.UUID = Field(
        primary_key=True,
        nullable=False,
        foreign_key="document.id",
        description="The Document Internal Database Id",
    )
    """The Document Internal Database Id"""

    tag_id: uuid.UUID = Field(
        primary_key=True,
        nullable=False,
        foreign_key="tag.id",
        description="The Tag Internal Database Id",
    )
    """The Tag Internal Database Id"""


class Tag(SQLModel, table=True):
    """The Tag Table.

    Holds information about the tags that can be assigned to documents.

    Args:
        id (uuid.UUID): "The Internal Database Id. Autogenerated if not passed,
            which is the recommended way."
        tag (str): "The Tag. Can contain: unicode lower case letters, numbers,
            underscore '_', and dash '-'."

        documents (list[Document]):
            The docs that use this Tag. A N Tag to N doc relationship,
            meaning a join with another table via link table on access. Use for access.

    """

    __tablename__ = "tag"  # type: ignore
    __table_args__ = {"extend_existing": True}

    id: uuid.UUID = Field(
        default_factory=uuid.uuid4,
        primary_key=True,
        nullable=False,
        description="The Internal Database Id. "
        "Autogenerated if not passed, which is the recommended way",
    )
    """The Internal Database Id.
    Autogenerated if not passed, which is the recommended way"""
    tag: str = Field(
        nullable=False,
        unique=True,
        description="The Tag",
    )
    """The Tag"""

    @validates("tag")
    def validate_tag(self, _, v):
        """Check tag against a regex using SQLAlchemy validators."""
        if not _tag_regex_pattern.match(v):
            raise ValueError(
                f"Invalid value for field tag.tag: {v}. Must be composed of "
                f"unicode lower case letters, numbers, '_' or '-'"
            )

    documents: list["Document"] = Relationship(  # type: ignore since pylance doesn't do forward references
        back_populates="tags", link_model=DocumentTags
    )
    """The docs that use this Tag. A N Tag to N doc relationship,
    meaning a join with another table via link table on access.
    Use for access."""
