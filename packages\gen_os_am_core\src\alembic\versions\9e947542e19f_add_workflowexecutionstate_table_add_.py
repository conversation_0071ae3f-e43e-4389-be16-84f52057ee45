"""Add WorkflowExecutionState table; Add agents and workflow_datasets tables.

Revision ID: 9e947542e19f
Revises: 9e7a17cf0ab0
Create Date: 2025-07-11 10:16:15.083239

"""

from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "9e947542e19f"
down_revision: str | None = "9e7a17cf0ab0"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Add WorkflowExecutionState table; Add agents and workflow_datasets tables."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "agents",
        sa.Column("id", sa.String(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("url", sa.String(), nullable=False),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "workflow_execution_state",
        sa.Column("id", sa.Uuid(), nullable=False),
        sa.Column("execution_id", sa.String(), nullable=False),
        sa.Column("state_type", sa.String(), nullable=False),
        sa.Column("state_value", sa.Text(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "workflow_datasets",
        sa.Column("id", sa.Uuid(), nullable=False),
        sa.Column("handle", sa.Integer(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("agent_id", sa.String(), nullable=False),
        sa.Column("inputs", sa.JSON(), nullable=False),
        sa.ForeignKeyConstraint(
            ["agent_id"],
            ["agents.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("handle"),
    )
    # Create a sequence for the handle column
    op.execute(
        sa.schema.CreateSequence(
            sa.Sequence("workflow_datasets_handle_seq", start=1, increment=1)
        )
    )
    # Alter the column to use the sequence
    op.alter_column(
        "workflow_datasets",
        "handle",
        existing_type=sa.Integer(),
        server_default=sa.text("nextval('workflow_datasets_handle_seq'::regclass)"),
        existing_nullable=False,
    )
    with op.batch_alter_table("agent_configuration", schema=None) as batch_op:
        batch_op.drop_index(batch_op.f("ix_agent_configuration_agent_id"))
        batch_op.create_foreign_key(None, "agents", ["agent_id"], ["id"])

    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrades."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("agent_configuration", schema=None) as batch_op:
        batch_op.drop_constraint(None, type_="foreignkey")
        batch_op.create_index(
            batch_op.f("ix_agent_configuration_agent_id"), ["agent_id"], unique=False
        )

    op.drop_table("workflow_datasets")
    op.drop_table("workflow_execution_state")
    op.drop_table("agents")
    # ### end Alembic commands ###
