"""Module with document data model."""

import datetime
import uuid
from typing import Any, ClassVar, Literal

from sqlalchemy.orm import validates
from sqlmodel import (
    JSON,
    TEXT,
    BigInteger,
    Column,
    DateTime,
    Field,
    Index,
    Relationship,
    SQLModel,
    UniqueConstraint,
    func,
)

from gen_os_am_knowledge.sdk.models.tag import DocumentTags, Tag


class Document(SQLModel, table=True):
    """The Document Table that holds information about the documents in the CMS.

    Notes:
      - SlaveDocuments have DEFAULT_USER as create and modify users.
      - MasterDocuments have DEFAULT_INGESTION as their ingestion.

    For Slave documents use:
    ```python
    gen_os_am_knowledge.sdk.models.common.slave_document(...)
    ```

    For Master documents use
    ```python
    gen_os_am_knowledge.sdk.models.common.master_document(...)
    ```

    For any other cases uses the DEFAULT_USER and/or DEFAULT_INGESTION as shown below:

    ```python
    from gen_os_am_knowledge.sdk.models.common import DEFAULT_USER, DEFAULT_INGESTION
    Document(...
        created_by = DEFAULT_USER,
        last_modified_by = DEFAULT_USER,
        ingestion = DEFAULT_INGESTION)
    ```

    Args:
        id (uuid.UUID): The Internal Database Id. Autogenerated if not passed,
            which is the recommended way.
        filename (str): Filename in Agent Manager File Storage.
        folder_id (uuid.UUID): The Folder Id. Either pass this or the
            associated relationship.
        original_path (str): Filepath On The Original Source.
        created_date (datetime.datetime | None): The Date The Document Was Created On.
        created_by_id (uuid.UUID): The Id Of User That Created The Document.
            Either pass this or the associated relationship.
        size (int): The Size of the Document file.
        type (str): The type of this file.
        kind (Literal["master", "slave"]): Kind of this document: master or slave
        meta (dict[str, Any] | None): Json Metadata For This Document.
        last_modified_by_id (uuid.UUID): The Id Of The last User
            That Modified The Document.
            Either pass this or the associated relationship.
        modification_date (datetime.datetime): The Data When This Document Was Modified.
        ingestion_id (uuid.UUID): The id for the associate ingestion.
            Either pass this or the associated relationship.
        document_status (Literal["Pending","Syncing Storage","Pending VectorDB",
            "Syncing VectorDB","All Synced","Error"]): The Document Status.
        deleted_date (datetime.datetime | None): The Deletion Date For This File.

        tags (list[Tag]|None): The list of Tags, a N tags to N docs,
            relationship meaning a join with another table via a N:N link table.
            Either pass this or the associated id.
        created_by (User|None): The user that created this doc, a 1 User to N docs
            relationship, meaning a join with another table.
            Either pass this or the associated id.
        last_modified_by (User|None): The user that last modified this doc, a 1 User to
            N docs relationship, meaning a join with another table. Either pass this or
            the associated id.
        ingestion (Ingestion|None): The ingestion associated with this doc, a 1
            ingestion to N docs relationship, meaning a join with another table.
            Either pass this or the associated id.
        folder (Folder|None): The folder for this doc, a 1 folder to N docs
            relationship, meaning a join with another table
            Either pass this or the associated id.

    """

    __tablename__ = "document"  # type: ignore

    __table_args__ = (
        UniqueConstraint("filename", "folder_id", name="unique_filename_folder_id"),
        Index(
            "ix_folder_id_hash",
            "folder_id",
            "hash",
            postgresql_where="hash IS NOT NULL",
        ),
        {"extend_existing": True},
    )

    _DOC_STATUS_TYPE: ClassVar[str] = Literal[
        "Pending",
        "Syncing Storage",
        "Pending VectorDB",
        "Syncing VectorDB",
        "Synced",
        "Error",
    ]

    DOC_STATUS_PENDING: ClassVar[_DOC_STATUS_TYPE] = "Pending"
    DOC_STATUS_SYNCING_STORAGE: ClassVar[_DOC_STATUS_TYPE] = "Syncing Storage"
    DOC_STATUS_PENDING_VECTORDB: ClassVar[_DOC_STATUS_TYPE] = "Pending VectorDB"
    DOC_STATUS_SYNCING_VECTORDB: ClassVar[_DOC_STATUS_TYPE] = "Syncing VectorDB"
    DOC_STATUS_ALL_SYNCED: ClassVar[_DOC_STATUS_TYPE] = "Synced"
    DOC_STATUS_ERROR: ClassVar[_DOC_STATUS_TYPE] = "Error"

    valid_doc_statuses: ClassVar[set[_DOC_STATUS_TYPE]] = {
        DOC_STATUS_PENDING,
        DOC_STATUS_SYNCING_STORAGE,
        DOC_STATUS_PENDING_VECTORDB,
        DOC_STATUS_SYNCING_VECTORDB,
        DOC_STATUS_ALL_SYNCED,
        DOC_STATUS_ERROR,
    }

    id: uuid.UUID = Field(
        default_factory=uuid.uuid4,
        primary_key=True,
        nullable=False,
        description="The Internal Database Id. "
        "Autogenerated if not passed, which is the recommended way",
    )
    """The Internal Database Id.
    Autogenerated if not passed, which is the recommended way"""

    filename: str = Field(
        nullable=False, description="Filename in Agent Manager File Storage"
    )
    """Filename in Agent Manager File Storage"""

    folder_id: uuid.UUID = Field(
        default=None,
        nullable=True,
        description="The Folder Id",
        foreign_key="folder.id",
    )
    """The Folder Id in the agent manager system"""

    original_path: str = Field(
        default=None, nullable=True, description="Filepath On The Original Source"
    )
    """Filepath On The Original Source"""

    created_date: datetime.datetime | None = Field(
        default=None,  # None so we don't have to set it in python
        sa_column=Column(
            DateTime,
            default=func.now(),
            nullable=False,  # false so the DB side generator runs
        ),
        description="The Date The Document Was Created On",
    )
    """The Date The Document Was Created On"""

    created_by_id: uuid.UUID = Field(
        foreign_key="user.id",
        nullable=False,
        description="The Id Of User That Created The Document",
    )
    """The Id Of User That Created The Document"""

    size: int = Field(
        sa_column=Column(BigInteger, nullable=False),
        description="The Size of the Document file",
    )
    """The Size of the Document file"""

    type: str = Field(nullable=False, description="The type of this file")
    """The type of this file"""

    kind: Literal["master", "slave"] = Field(
        sa_column=Column(TEXT, nullable=False),
        description="Kind of this document: master or slave",
    )
    """Kind of this document: master or slave"""

    meta: dict[str, Any] | None = Field(
        sa_column=Column(
            JSON,
            default=None,
            nullable=True,
        ),
        description="Json Metadata For This Document",
    )
    """Json Metadata For This Document"""

    last_modified_by_id: uuid.UUID = Field(
        foreign_key="user.id",
        nullable=False,
        description="The Id Of The last User That Modified The Document",
    )
    """The Id Of The last User That Modified The Document"""

    modification_date: datetime.datetime = Field(
        default=None,  # None so we don't have to set it in python
        sa_column=Column(
            DateTime,
            default=func.now(),
            onupdate=func.now(),
            nullable=False,  # false so the DB side generator runs
        ),
        description="The Data When This Document Was Modified",
    )
    """The Data When This Document Was Modified"""

    ingestion_id: uuid.UUID = Field(
        foreign_key="ingestion.id",
        nullable=False,
        description="The id for the associate ingestion",
    )
    """The id for the associate ingestion"""

    hash: int = Field(
        sa_column=Column(BigInteger, nullable=False),
        description="xxhash64 of the file",
    )
    """xxhash64 of the file"""

    document_status: _DOC_STATUS_TYPE = Field(
        sa_column=Column(TEXT, default=DOC_STATUS_PENDING, nullable=False, index=True),
        description=f"The Document Document Status in {valid_doc_statuses}",
    )
    f"""The Document Status in {valid_doc_statuses}"""

    @validates("document_status")
    def validate_document_status(self, _, v):
        """Check status against the valid statuses."""
        if v not in Document.valid_doc_statuses:
            raise ValueError(
                f"Invalid value for field document.document_status: {v}. "
                f"Must be one of {Document.valid_doc_statuses}"
            )

    deleted_date: datetime.datetime | None = Field(
        nullable=True,
        default=None,
        description="The Deletion Date For This File",
        index=True,
    )
    """The Deletion Date For This File"""

    draft: bool = Field(
        default=False,
        description="If the document is in draft mode or not",
        index=True,
    )
    """If the document is in draft mode or not"""

    tags: list[Tag] = Relationship(back_populates="documents", link_model=DocumentTags)
    """The list of Tags, a N tags to N docs relationship, meaning a join with another
    table via a N:N link table. Either pass this or the associated id."""

    created_by: "User" = Relationship(  # type: ignore since pylance doesn't do forward references
        back_populates="created_documents",
        sa_relationship_kwargs={"foreign_keys": "Document.created_by_id"},
    )
    """The user that created this doc, a 1 User to N docs relationship, meaning a join
    with another table. Either pass this or the associated id."""

    last_modified_by: "User" = Relationship(  # type: ignore since pylance doesn't do forward references
        back_populates="modified_documents",
        sa_relationship_kwargs={"foreign_keys": "Document.last_modified_by_id"},
    )
    """The user that last modified this doc, a 1 User to N docs relationship, meaning
    a join with another table. Either pass this or the associated id."""

    ingestion: "Ingestion" = Relationship(back_populates="documents")  # type: ignore since pylance doesn't do forward references
    """The ingestion associated with this doc, a 1 ingestion to N docs relationship,
    meaning a join with another table. Either pass this or the associated id."""

    folder: "Folder" = Relationship(back_populates="documents")  # type: ignore since pylance doesn't do forward references
    """The folder for this doc, a 1 folder to N docs relationship, meaning a join
    with another table. Either pass this or the associated id."""
