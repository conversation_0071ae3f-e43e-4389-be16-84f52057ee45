services:
  postgres:
    container_name: gen_os_agent_manager_postgres
    image: gen_os_agent_manager_postgres
    build:
      context: ./postgres
      dockerfile: Dockerfile
      network: host
    environment:
      POSTGRES_USER: gen_os_agent_manager
      POSTGRES_PASSWORD: gen_os_agent_manager_pass
      POSTGRES_PORT: 5432
      POSTGRES_DB: gen_os_agent_manager_db
    volumes:
      - ./postgres/scripts/:/docker-entrypoint-initdb.d/
      - ./postgres/postgresql.conf:/etc/postgresql.conf
    ports:
      - ${POSTGRES_PORT:-5432}:5432
    command: -c config_file=/etc/postgresql.conf
    healthcheck:
      test:
        [
          'CMD-SHELL',
          'pg_isready -U gen_os_agent_manager -d gen_os_agent_manager_db',
        ]
      interval: 15s
      timeout: 15s
      retries: 10
