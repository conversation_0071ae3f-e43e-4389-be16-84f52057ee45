# Utils

## Logging

### `init_logger` - Customizable Logging Setup

The `init_logger` function initializes a flexible logger with support for structured logging and multiple output destinations.

#### **Supported Formats**
- **`ecs`** → Structured logging for Elasticsearch (ECS format).
- **`otel`** → OpenTelemetry-compatible logging.
- **`default`** → Standard Python logging format.
- Any custom formatter via `formatter`  arg

#### **Supported Handlers**
- **`stdout`** → Logs to standard output (console).
- **`stderr`** → Logs to standard error stream.
- **`file_rotating`** → Logs to a file with size-based rotation. Extra params setting availabale
- **`file_timed_rotating`** → Logs to a file with time-based rotation. Extra params setting availabale
- Any custom set of handlers passed as list into **`handlers`**

#### **Usage Example**
```python
from log.logging import init_logger

logger = init_logger(name="my_app", log_format="ecs", destination="stdout")
logger.info("Logging in ECS format to stdout.")
```

