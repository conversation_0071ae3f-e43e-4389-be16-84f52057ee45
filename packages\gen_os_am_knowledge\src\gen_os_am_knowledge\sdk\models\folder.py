"""Module with folder data schema."""

import datetime
import typing
import uuid

from sqlalchemy.orm import validates
from sqlmodel import (
    Column,
    DateTime,
    Field,
    Relationship,
    SQLModel,
    UniqueConstraint,
    func,
)

from gen_os_am_knowledge.sdk.models.default_uuid import _DEFAULT_UUID


class Folder(SQLModel, table=True):
    """The Folder Table that holds information about the logical Folder hierarchy.

    Args:
        id (uuid.UUID): The Internal Database Id.
        name (str): The Folder Name.
        parent_id (uuid.UUID | None): The Parent Folder Id, if any.
        parent (Folder| None): The parent Folder of this Folder if any.
            A 1 parent to N children relationship, meaning a selfjoin
            with this table on access.
            Use for access or to create a subfolders and its parent.
        children (list["Folder"]): The child Folders if any.
            A 1 parent to N children relationship, meaning a selfjoin with this
            table on access.
            Use for access or to create multiple subfolders when creating the
            parent Folder.
        documents (list["Document"]): The documents in this folder.
            A 1 folder to N docs relationship, meaning a join
            with another table on access. Either pass this or the associated id.

    """

    __tablename__ = "folder"  # type: ignore

    __table_args__ = (
        UniqueConstraint("name", "parent_id", name="unique_folder_name_parent_id"),
        {"extend_existing": True},
    )

    id: uuid.UUID = Field(
        default_factory=uuid.uuid4,
        primary_key=True,
        nullable=False,
        description="The Internal Database Id. "
        "Autogenerated if not passed, which is the recommended way",
    )
    """The Internal Database Id.
    Autogenerated if not passed, which is the recommended way"""

    name: str = Field(nullable=False, description="The Folder Name")
    """The Folder Name"""

    created_date: datetime.datetime | None = Field(
        default=None,  # None so we don't have to set it in python
        sa_column=Column(
            DateTime,
            default=func.now(),
            nullable=False,  # false so the DB side generator runs
        ),
        description="The Date The Folder Was Created On",
    )
    """The Date The Folder Was Created On"""

    created_by_id: uuid.UUID = Field(
        foreign_key="user.id",
        nullable=False,
        description="The Id Of User That Created The Folder",
    )
    """The Id Of User That Created The Folder"""

    last_modified_by_id: uuid.UUID = Field(
        foreign_key="user.id",
        nullable=False,
        description="The Id Of The last User That Modified The Folder",
    )
    """The Id Of The last User That Modified The Folder"""

    modification_date: datetime.datetime = Field(
        default=None,  # None so we don't have to set it in python
        sa_column=Column(
            DateTime,
            default=func.now(),
            onupdate=func.now(),
            nullable=False,  # false so the DB side generator runs
        ),
        description="The Data When This Folder Was Modified",
    )
    """The Data When This Folder Was Modified"""

    deleted_date: datetime.datetime | None = Field(
        nullable=True,
        default=None,
        description="The Deletion Date For This File",
        index=True,
    )
    """The Deletion Date For This File"""

    @validates("name")
    def validate_tag(self, _, v):
        """Check folder_name for '/'."""
        if "/" in v:
            raise ValueError("Folder name cannot contain '/'")

    parent_id: uuid.UUID | None = Field(
        default=None,
        nullable=True,
        description="The Parent Folder Id, if any",
        foreign_key="folder.id",
    )
    """The Parent Folder Id, if any"""

    parent: typing.Union["Folder", None] = (
        Relationship(  # needs to be union since | does not seem to support forward references  # noqa: E501
            back_populates="children",
            sa_relationship_kwargs={"remote_side": "Folder.id"},
        )
    )  # type: ignore since pylance doesn't do forward references
    """The parent Folder of this Folder if any.
    A 1 parent to N children relationship, meaning a selfjoin
    with this table on access.
    Use for access or to create a subfolders and its parent."""

    children: list["Folder"] = Relationship(back_populates="parent")  # type: ignore since pylance doesn't do forward references
    """The child Folders if any. A 1 parent to N children relationship,
    meaning a selfjoin with this table on access. Use for access or to
    create multiple subfolders when creating the parent Folder."""

    documents: list["Document"] = Relationship(back_populates="folder")  # type: ignore since pylance doesn't do forward references
    """The docs on this Folder. A 1 Folder to N docs relationship, meaning a
    join with another table on access. Either pass this or the associated id."""

    ingestion: "Ingestion" = Relationship(back_populates="folder")  # type: ignore since pylance doesn't do forward references

    def __init__(self, **data):
        """Initialize the Folder model."""
        if "parent_id" not in data:
            data["parent_id"] = _DEFAULT_UUID
        super().__init__(**data)

    created_by: "User" = Relationship(  # type: ignore since pylance doesn't do forward references
        back_populates="created_folders",
        sa_relationship_kwargs={"foreign_keys": "Folder.created_by_id"},
    )
    """The user that created this folder, a 1 User to N folderss relationship,
    meaning a join with another table. Either pass this or the associated id."""

    last_modified_by: "User" = Relationship(  # type: ignore since pylance doesn't do forward references
        back_populates="modified_folders",
        sa_relationship_kwargs={"foreign_keys": "Folder.last_modified_by_id"},
    )
    """The user that last modified this folder, a 1 User to N folders relationship,
    meaning a join with another table. Either pass this or the associated id."""
