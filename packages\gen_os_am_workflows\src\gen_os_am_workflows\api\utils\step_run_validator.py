"""Validators for the Case Management API."""

from fastapi import <PERSON><PERSON><PERSON><PERSON>xception

from gen_os_am_workflows.api.models.request import Step<PERSON><PERSON><PERSON>un, StepRunCreate
from gen_os_am_workflows.database.models import Step


class StepRunValidator:
    """Validator for StepRun creation and updates."""

    def __init__(self, step: Step, step_run_data: StepRunCreate):
        """Initialize the validator.

        Args:
            step: The Step model instance that defines the structure
            step_run_data: The StepRunCreate data to validate

        """
        self.step = step
        self.step_run_data = step_run_data

        self.step_input_map = {
            input_block.step_block_name: (input_block.step_block_type, input_block.order_number)
            for input_block in step.inputs
        }
        self.step_output_map = {
            output_block.step_block_name: (output_block.step_block_type, output_block.order_number)
            for output_block in step.outputs
        }

    def validate(self) -> None:
        """Validate both inputs and outputs."""
        self._validate_inputs()
        self._validate_outputs()

    def _validate_block_structure(
        self,
        provided_blocks: list[StepBlockRun] | None,
        expected_blocks: dict[str, tuple[str, int]],
        block_type: str,
        require_all: bool = True,
    ) -> None:
        """Validate that provided blocks match the expected structure.

        Args:
            provided_blocks: List of blocks to validate
            expected_blocks: Map of expected block names to their types and order numbers
            block_type: Type of block being validated ('input' or 'output')
            require_all: Whether all expected blocks must be provided

        """
        # Skip validation if no blocks provided
        if provided_blocks is None:
            return

        provided_map = {
            block.step_block_name: (block.step_block_type, block.order_number)
            for block in provided_blocks
        }

        # Check for missing required blocks
        if require_all:
            missing = set(expected_blocks.keys()) - set(provided_map.keys())
            if missing:
                raise HTTPException(
                    status_code=422,
                    detail=f"Missing required {block_type}s: {', '.join(missing)}",
                )

        # Check for unexpected blocks
        unexpected = set(provided_map.keys()) - set(expected_blocks.keys())
        if unexpected:
            raise HTTPException(
                status_code=422,
                detail=f"Unexpected {block_type}s provided: {', '.join(unexpected)}",
            )

        # Validate types and order numbers
        for name, (type_, order_number) in provided_map.items():
            expected_type, expected_order = expected_blocks[name]
            if type_ != expected_type:
                raise HTTPException(
                    status_code=422,
                    detail=f"Invalid type for {block_type} {name}."
                    f"Expected {expected_type}, got {type_}",
                )
            if order_number != expected_order:
                raise HTTPException(
                    status_code=422,
                    detail=f"Invalid order number for {block_type} {name}."
                    f"Expected {expected_order}, got {order_number}",
                )

    def _validate_inputs(self) -> None:
        """Validate that the provided inputs match the step's input structure."""
        self._validate_block_structure(
            self.step_run_data.inputs,
            self.step_input_map,
            "input",
            require_all=True,
        )

    def _validate_outputs(self) -> None:
        """Validate that the provided outputs match the step's output structure."""
        self._validate_block_structure(
            self.step_run_data.outputs,
            self.step_output_map,
            "output",
            require_all=False,
        )
