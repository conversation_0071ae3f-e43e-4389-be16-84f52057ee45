"""Add file name column to file table.

Revision ID: 57f8dd96113f
Revises: 9e1222b76ef0
Create Date: 2025-03-19 21:57:32.614936

This migration adds a file_name column to the file table to store the original
name of uploaded files.
"""

from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "57f8dd96113f"
down_revision: str | None = "9e1222b76ef0"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Add file_name column to file table."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("file", sa.Column("file_name", sa.String(), nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Remove file_name column from file table."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("file", "file_name")
    # ### end Alembic commands ###
