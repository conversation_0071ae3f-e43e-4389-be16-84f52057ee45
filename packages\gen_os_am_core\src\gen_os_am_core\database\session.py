"""Database Sessions for Agent Manager."""

from sqlalchemy import create_engine, select
from sqlalchemy.ext.asyncio import (
    AsyncSession,
    async_sessionmaker,
    create_async_engine,
)
from sqlalchemy.orm import Session, sessionmaker

from gen_os_am_core.settings import Settings

# TODO(Henrique): Use session manager from `gen-os-sdk`


def create_sqlalchemy_pg_connection_string(
    database_host: str | None,
    database_password: str,
    database_user: str,
    database_port: int,
    database_socket: str | None = None,
    database_name: str | None = None,
    driver: str = "psycopg",
) -> str:
    """Create a SQLAlchemy connection string.

    Args:
        database_host(str): Database host address
        database_password(str): Database password
        database_user(str): Database username
        database_port(str): Database port
        database_socket(str): Optional socket path (for e.g. Google Cloud Run).
            Default None.
        database_name(str): Optional database name. Default None means username.
        driver(str): The driver to use (psycopg compatible). Default psycopg.

    Returns:
        SQLAlchemy connection string using either TCP or unix sockets

    """
    db_name = database_name or database_user
    if database_socket is not None:
        connection_string = (
            f"postgresql+{driver}://{database_user}:{database_password}@/"
            f"{db_name}?host={database_socket}&port={database_port}"
        )
    else:
        connection_string = (
            f"postgresql+{driver}://{database_user}:{database_password}"
            f"@{database_host}:{database_port}/{db_name}"
        )
    return connection_string


class SessionManager:
    """Manage Sessions for the Database."""

    session_manager: "SessionManager | None" = None

    def __init__(self):
        """Create a new session manager."""
        self.database_pool_size = Settings.get_settings().DATABASE_POOL_SIZE
        database_url = SessionManager.get_database_url()
        sync_database_url = SessionManager.get_database_url(sync=True)

        self.async_engine = create_async_engine(
            database_url,
            pool_size=self.database_pool_size,
            max_overflow=0,
        )
        self.sync_engine = create_engine(
            sync_database_url,
            pool_size=self.database_pool_size,
            max_overflow=0,
        )
        self.async_session_maker = async_sessionmaker(
            self.async_engine, expire_on_commit=False
        )
        self.sync_session_maker = sessionmaker(self.sync_engine, expire_on_commit=False)

    def _get_session_maker(self) -> async_sessionmaker[AsyncSession]:
        return self.async_session_maker

    def _get_sync_session_maker(self) -> sessionmaker[Session]:
        return self.sync_session_maker

    @staticmethod
    def get_database_url(sync: bool = False):
        """Construct a Valid Database URL."""
        settings = Settings.get_settings()
        match settings.DATABASE_TYPE:
            case "sqlite":
                if sync:
                    return f"sqlite:///{settings.DATABASE_NAME}"
                else:
                    return f"sqlite+aiosqlite:///{settings.DATABASE_NAME}"
            case "postgresql":
                if not all(
                    [
                        settings.DATABASE_USER,
                        settings.DATABASE_PASSWORD,
                        settings.DATABASE_PORT,
                    ]
                ):
                    raise ValueError("PostgreSQL requires user, password, and port.")
                assert settings.DATABASE_USER
                assert settings.DATABASE_PASSWORD
                assert settings.DATABASE_PORT
                return create_sqlalchemy_pg_connection_string(
                    database_host=settings.DATABASE_HOST,
                    database_name=settings.DATABASE_NAME,
                    database_user=settings.DATABASE_USER,
                    database_password=settings.DATABASE_PASSWORD,
                    database_port=settings.DATABASE_PORT,
                    database_socket=settings.INSTANCE_UNIX_SOCKET,
                )
            case _:
                raise ValueError(f"Unsupported database type: {settings.DATABASE_TYPE}")

    @staticmethod
    def get_session_manager() -> "SessionManager":
        """Get the session manager or initializes it if not Initialized.

        Returns:
            SessionManager: the session manager

        """
        if SessionManager.session_manager is None:
            SessionManager.session_manager = SessionManager()
        return SessionManager.session_manager

    @classmethod
    def get_session(cls) -> AsyncSession:
        """Return an Async Database Session."""
        return SessionManager.get_session_manager()._get_session_maker()()

    @classmethod
    def get_sync_session(cls) -> Session:
        """Return an Sync Database Session."""
        return SessionManager.get_session_manager()._get_sync_session_maker()()

    @classmethod
    async def check_connection(cls) -> bool:
        """Check if the database connection is working.

        Returns:
            bool: True if connection is working, False otherwise

        """
        try:
            async with cls.get_session() as session:
                await session.execute(select(1))
                return True
        except Exception:
            return False
