"""Module for the base file system connector."""

import inspect
from abc import ABC, abstractmethod
from pathlib import Path

from pydantic.dataclasses import dataclass

from gen_os_am_knowledge.sdk.utils.protocols import NamedFileLike


@dataclass
class FSEntity:
    """Class for the file system entity."""

    path: str
    is_folder: bool
    is_file: bool


class BaseFileSystemConnector(ABC):
    """Base class for the file system connectors."""

    @staticmethod
    def ensure_folder_path(param_names: list[str]):
        """Decorate to ensure the input parameters are treated as folder paths.

        Args:
            param_names : list : List of parameter names that should be treated as
            folder paths

        """

        def decorator(func):
            def wrapper(self, *args, **kwargs):
                bound = inspect.signature(func).bind(self, *args, **kwargs)
                bound.apply_defaults()
                for param in param_names:
                    value = bound.arguments.get(param)
                    bound.arguments[param] = (
                        Path(value).as_posix() + "/" if len(value) > 0 else ""
                    )
                return func(*bound.args, **bound.kwargs)

            return wrapper

        return decorator

    @abstractmethod
    def create_folder(
        self,
        folder_path: str,
        create_parent_folders_if_not_exist: bool = True,
    ) -> FSEntity:
        """Create a folder in the filesystem.

        Args:
            folder_path (str): The path of the folder to be created.
                The path should be relative to the root of the filesystem.
            create_parent_folders_if_not_exist (bool): If True, create parent folders
                if they don't exist. If False, raise an error if parent
                folders don't exist.

        Returns:
            FSEntity: The created folder entity.

        """
        pass

    @abstractmethod
    def delete_folder(self, folder_path: str) -> FSEntity:
        """Delete a folder in the filesystem.

        Args:
            folder_path : str : The path of the folder to be deleted. The path should
            be relative to the root of the filesystem.

        Returns:
            FSEntity : The entity that was deleted

        """
        pass

    @abstractmethod
    def download_file(self, folder_path: str, filename: str) -> NamedFileLike:
        """Download a file from the filesystem.

        Args:
            folder_path : str : The path of the folder containing the file
            to be downloaded. The path should be relative to the root of the filesystem.
            filename : str : The name of the file to be downloaded.

        Returns:
            NamedFileLike : A file-like object that can be read from

        """
        pass

    @abstractmethod
    def upload_file(
        self,
        folder_path: str,
        filename: str,
        file: NamedFileLike,
        create_parent_folders_if_not_exists: bool = True,
        overwrite: bool = False,
    ) -> FSEntity:
        """Upload a file to the filesystem.

        Args:
            folder_path : str : The path of the folder to which the file
                should be uploaded. The path should be relative to the
                root of the filesystem.
            filename : str : The name of the file to be uploaded.
            file : NamedFileLike : A file-like object that can be read from.
            create_parent_folders_if_not_exists : bool : If True, create
                the parent folders if they do not exist. If False, raise an error if
                the parent folders do not exist.
            overwrite : bool : If True, overwrite the file if it already exists.
                If False, raise an error if the file already exists.

        Returns:
            FSEntity : The entity that was uploaded

        """
        pass

    @abstractmethod
    def delete_file(self, folder_path: str, filename: str) -> FSEntity:
        """Delete a file from the filesystem.

        Args:
            folder_path : str : The path of the folder containing the file
                to be deleted. The path should be relative to the root
                of the filesystem.
            filename : str : The name of the file to be deleted.

        Returns:
            FSEntity : The entity that was deleted

        """
        pass

    @abstractmethod
    def copy_file(
        self,
        original_file_path: str,
        destination_file_path: str,
        overwrite: bool = False,
    ) -> FSEntity:
        """Copy a file in the filesystem.

        Args:
            original_file_path : str : The path of the file to be copied.
                The path should be relative to the root of the filesystem.
            destination_file_path : str : The path to which the file should be copied.
                The path should be relative to the root of the filesystem.
            overwrite : bool : If True, overwrite the file if it already exists.
                If False, raise an error if the file already exists.

        Returns:
            FSEntity : The entity that was copied

        """
        pass

    @abstractmethod
    def move_file(
        self,
        original_file_path: str,
        destination_file_path: str,
        overwrite: bool = False,
    ) -> FSEntity:
        """Move a file in the filesystem.

        Args:
            original_file_path: The path of the file to be moved.
                The path should be relative to the root of the filesystem.
            destination_file_path : The path to which the file should be moved.
                The path should be relative to the root of the filesystem.
            overwrite : If True, overwrite the file if it already exists.
                If False, raise an error if the file already exists.

        Returns:
            FSEntity : The entity that was moved

        """
        pass

    @abstractmethod
    def move_folder(
        self,
        original_folder_path: str,
        destination_folder_path: str,
        overwrite: bool = False,
        with_folder: bool = True,
    ) -> FSEntity:
        r"""Move a folder in the filesystem.

        Args:
            original_folder_path : The path of the folder to be moved.
                The path should be relative to the root of the filesystem.
            destination_folder_path : The path to which the folder should be moved.
                The path should be relative to the root of the filesystem.
            overwrite : If True, overwrite the folder if it already exists.
                If False, raise an error if the folder already exists.
                No-op if with_folder is False because the folder is not moved,
                just contents.
            with_folder : If True, move the folder along with its contents.
                If False, move only the contents of the folder.

        Example:
                        with True - move_folder('folder1', 'folder2'):
                            folder1/contents -> folder2/folder1/contents
                        with False - move_folder('folder1', 'folder2'):
                            folder1/contents -> folder2/contents

        Returns:
            FSEntity : The entity that was moved

        """
        pass

    @abstractmethod
    def copy_folder(
        self,
        original_folder_path: str,
        destination_folder_path: str,
        overwrite: bool = False,
        with_folder: bool = True,
    ) -> FSEntity:
        """Copy a folder in the filesystem.

        Args:
            original_folder_path : str : The path of the folder to be copied.
                The path should be relative to the root of the filesystem.
            destination_folder_path : str : The path to which the
                folder should be copied. The path should be relative
                to the root of the filesystem.
            overwrite : bool : If True, overwrite the folder if
                it already exists. If False, raise an error if the
                folder already exists.
            with_folder : bool : If True, copy the folder along with its
                contents. If False, copy only the contents of the folder.
                Example: with True - copy_folder('folder1', 'folder2'):
                                    folder1/contents -> folder2/folder1/contents
                         with False - copy_folder('folder1', 'folder2'):
                                    folder1/contents -> folder2/contents

        Returns:
            FSEntity : The entity that was copied

        """
        pass

    @abstractmethod
    def ls(self, folder_path: str) -> list:
        """List the contents of a folder in the filesystem.

        Args:
            folder_path : str : The path of the folder to be listed.
            The path should be relative to the root of the filesystem.

        Returns:
            list : A list of objects representing the contents of the folder

        """
        pass

    @abstractmethod
    def generate_presigned_url(self, folder_path: str, filename: str) -> str:
        """Generate a presigned URL for a file in the filesystem for downloading.

        Args:
            folder_path : str : The path of the folder containing the file.
                The path should be relative to the root of the filesystem.
            filename : str : The name of the file for which to generate the
                presigned URL.

        Returns:
            str: The presigned URL for the file.

        """
        pass
