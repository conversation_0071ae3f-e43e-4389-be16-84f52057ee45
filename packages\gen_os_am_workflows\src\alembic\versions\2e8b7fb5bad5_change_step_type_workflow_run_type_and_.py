"""change step type, workflow run type and add step block.

Revision ID: 2e8b7fb5bad5
Revises: e38a72229471
Create Date: 2025-05-27 09:04:06.517740

"""

from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "2e8b7fb5bad5"
down_revision: str | None = "e38a72229471"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Upgrade."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "step_input",
        sa.Column("id", sa.Uuid(), nullable=False),
        sa.Column("step_id", sa.Uuid(), nullable=False),
        sa.Column("step_block_type", sa.String(), nullable=False),
        sa.Column("status", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["step_id"],
            ["step.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "step_output",
        sa.Column("id", sa.Uuid(), nullable=False),
        sa.Column("step_id", sa.Uuid(), nullable=False),
        sa.Column("step_block_type", sa.String(), nullable=False),
        sa.Column("status", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["step_id"],
            ["step.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )

    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("step_output")
    op.drop_table("step_input")
    # ### end Alembic commands ###
