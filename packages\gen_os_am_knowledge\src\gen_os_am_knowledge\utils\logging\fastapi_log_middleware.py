"""FastAPI logging middleware."""

import logging
import os
import traceback
from datetime import datetime, timezone

from starlette.concurrency import iterate_in_threadpool
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request

from gen_os_am_knowledge.utils.logging.log_models import (
    BaseLog,
    ErrorLog,
    HTTPRequest,
    HTTPResponse,
)

ENVIRONMENT = os.getenv("ENVIRONMENT", "DEV")


class APILoggingMiddleware(BaseHTTPMiddleware):
    """API logging middleware."""

    def __init__(self, app, logger: logging.Logger):
        """Initialize the middleware."""
        super().__init__(app)
        self.logger = logger

    async def dispatch(self, request: Request, call_next):
        """Dispatch the request."""
        error_log = None

        start_dt = datetime.now(timezone.utc)

        try:
            response = await call_next(request)
            end_dt = datetime.now(timezone.utc)
        except Exception as e:
            end_dt = datetime.now(timezone.utc)
            error_log = ErrorLog(
                code=500,
                message=str(e),
                type_=type(e).__name__,
                stack_trace=traceback.format_exc(),
            )

        req_size = (
            int(request.headers.get("content-length"))
            if request.headers.get("content-length")
            else None
        )
        res_size = (
            int(response.headers.get("content-length"))
            if response.headers.get("content-length")
            else None
        )

        if response.status_code >= 400:
            try:
                req_body = str(await request.json())
                req_size = len(req_body)
            except Exception:
                req_body = None
            try:
                res_chunks = [section async for section in response.body_iterator]
                response.body_iterator = iterate_in_threadpool(iter(res_chunks))
                res_body = res_chunks[0].decode() if res_chunks else ""
                res_size = len(res_body)
            except Exception:
                res_body = None

            try:
                error_message = res_body.get(
                    "detail", f"Failed {request.method} call for {request.url}"
                )
            except Exception:
                error_message = f"Failed {request.method} call for {request.url}"
            error_log = ErrorLog(
                code=response.status_code,
                message=error_message,
                type_="HTTPException",
                stack_trace=None,
            )
        else:
            req_body = None
            res_body = None

        http_req = HTTPRequest(
            method=request.method,
            bytes=req_size,
            mime_type=request.headers.get("content-type"),
            body_content=req_body,
        )
        http_res = HTTPResponse(
            status_code=response.status_code,
            bytes=res_size,
            mime_type=response.headers.get("content-type"),
            body_content=res_body,
        )

        base_log = BaseLog(
            environment=ENVIRONMENT,
            start=start_dt,
            end=end_dt,
            request=http_req,
            response=http_res,
        )

        if response.status_code < 400:
            self.logger.info(
                msg=f"Successful {request.method} call for {request.url}",
                extra=base_log.model_dump(mode="json"),
            )
        else:
            base_log.error = error_log
            self.logger.error(
                msg=f"Failed {request.method} call for {request.url}",
                extra=base_log.model_dump(mode="json"),
            )
        return response
