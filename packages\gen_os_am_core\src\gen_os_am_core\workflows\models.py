"""Models for step execution responses."""

from typing import Any

from gen_os_am_workflows.database.models import (
    OccurrenceReasons,
    StepRunStatus,
)
from pydantic import BaseModel


class StepExecutionResponse(BaseModel):
    """Response from step execution indicating the outcome and next actions."""

    reason: OccurrenceReasons | None = None
    status: StepRunStatus | None = None
    outputs: list[dict[str, Any]] = []
    context_id: str | None = None
