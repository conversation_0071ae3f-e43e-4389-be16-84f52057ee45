# ruff: noqa: I001, E402
"""Utility functions for A2A protocol integration with workflow orchestration.

This module contains helper functions for converting between Agent Manager workflow
step blocks and A2A protocol message parts and artifacts.
"""

from typing import Any

from a2a.types import (
    Artifact,
    DataPart,
    FilePart,
    FileWithBytes,
    FileWithUri,
    Part,
    TextPart,
)
from gen_os_am_workflows.database.models import Step, StepRun


def get_default_block_data(step_block_type: str) -> dict[str, Any]:
    """Get default empty data for a given step block type.

    Args:
        step_block_type: The type of the step block

    Returns:
        Default empty data structure for the block type

    """
    default_data_map = {
        "dataset": {"dataset": []},
        "file": {"files": []},
        "selection": {"selection": {"selected_option": None, "custom_input": None}},
        "text_structured": {"text_structured": {}},
        "text_unstructured": {"text_unstructured": ""},
        "tool": {"tool": {"name": "", "parameters": {}, "output": None}},
        "email": {"email": {"to": [], "cc": [], "bcc": [], "subject": "", "body": ""}},
    }
    return default_data_map.get(step_block_type, {})


def create_message_parts_from_step_run(step_run: StepRun) -> list[Part]:
    """Create A2A message parts from StepRun inputs.

    Mapping A2A part types to AM step block types:
    - dataset: list[DataPart]
    - file: FilePart
    - selection: TextPart
    - text_structured: DataPart
    - text_unstructured: TextPart
    - tool: DataPart (no specific A2A type)
    - email: DataPart

    Args:
        step_run: StepRun object containing input data

    Returns:
        List of A2A message parts

    """
    parts = []

    # Process each input block
    for input_block in step_run.inputs:
        step_block_name = input_block.get("step_block_name")
        step_block_type = input_block.get("step_block_type")
        block_data = input_block.get("data", {})

        if step_block_type == "text_unstructured":
            # TextPart for unstructured text
            text_content = block_data.get("text_unstructured", "")
            if text_content:
                parts.append(
                    TextPart(
                        kind="text",
                        text=text_content,
                        metadata={"name": step_block_name},
                    )
                )

        elif step_block_type == "text_structured":
            # DataPart for structured text
            structured_data = block_data.get("text_structured", {})
            if structured_data:
                parts.append(
                    DataPart(
                        kind="data",
                        data=structured_data,
                        metadata={"name": step_block_name},
                    )
                )

        elif step_block_type == "dataset":
            # List of DataParts for dataset items
            dataset = block_data.get("dataset", [])
            for item in dataset:
                parts.append(
                    DataPart(kind="data", data=item, metadata={"name": step_block_name})
                )

        elif step_block_type == "selection":
            # TextPart for selection (as per your mapping)
            selection_data = block_data.get("selection", {})
            if selection_data:
                # Convert selection to text representation
                selected_option = selection_data.get("selected_option", "")
                custom_input = selection_data.get("custom_input", "")
                text_content = f"Selected: {selected_option}"
                if custom_input:
                    text_content += f", Custom: {custom_input}"

                parts.append(
                    TextPart(
                        kind="text",
                        text=text_content,
                        metadata={"name": step_block_name},
                    )
                )

        elif step_block_type == "file":
            # FilePart for files
            files = block_data.get("files", [])
            for file_info in files:
                if isinstance(file_info, dict):
                    # Handle file object with metadata
                    if "data" in file_info:
                        # Binary file with base64 data
                        file_obj = FileWithBytes(
                            name=file_info.get("name", ""),
                            mimeType=file_info.get(
                                "mime_type", "application/octet-stream"
                            ),
                            bytes=file_info.get("data", ""),
                        )
                    elif "url" in file_info:
                        # URL file
                        file_obj = FileWithUri(
                            name=file_info.get("name", ""),
                            mimeType=file_info.get(
                                "mime_type", "application/octet-stream"
                            ),
                            uri=file_info.get("url", ""),
                        )
                    else:
                        # Default to empty binary file
                        file_obj = FileWithBytes(
                            name=file_info.get("name", "unknown"),
                            mimeType=file_info.get(
                                "mime_type", "application/octet-stream"
                            ),
                            bytes="",
                        )

                    file_part = FilePart(
                        kind="file",
                        file=file_obj,
                        metadata={"name": step_block_name},
                    )
                    parts.append(file_part)
                elif isinstance(file_info, str):
                    # Handle file path as text (fallback)
                    parts.append(
                        TextPart(
                            kind="text",
                            text=file_info,
                            metadata={"name": step_block_name},
                        )
                    )

        elif step_block_type == "tool":
            # DataPart for tool data (no specific A2A type)
            tool_data = block_data.get("tool", {})
            if tool_data:
                parts.append(
                    DataPart(
                        kind="data",
                        data=tool_data,
                        metadata={"name": step_block_name},
                    )
                )

        elif step_block_type == "email":
            # DataPart for email data
            email_data = block_data.get("email", {})
            if email_data:
                parts.append(
                    DataPart(
                        kind="data",
                        data=email_data,
                        metadata={"name": step_block_name},
                    )
                )

    # If no parts were created, add a default text part
    if not parts:
        parts.append(
            TextPart(
                kind="text",
                text="Execute step with provided inputs",
                metadata={"name": "default_input"},
            )
        )

    return parts


def extract_outputs_from_artifacts(
    artifacts: list[Artifact], step: Step
) -> list[dict[str, Any]]:
    """Extract outputs from A2A artifacts based on step definition.

    Args:
        artifacts: List of A2A artifacts from agent response
        step: Current workflow step

    Returns:
        List of output blocks with extracted data

    """
    outputs = []

    # Create a map of expected output block names from step definition
    expected_outputs = {}
    if hasattr(step, "outputs") and step.outputs:
        for output_def in step.outputs:
            expected_outputs[output_def.step_block_name] = output_def.step_block_type

    # Process each artifact
    for artifact in artifacts:
        artifact_name = artifact.name

        # Check if this artifact matches an expected output
        if artifact_name in expected_outputs:
            output_block_type = expected_outputs[artifact_name]

            # Extract data based on artifact parts
            extracted_data = _extract_data_from_artifact_parts(
                parts=artifact.parts, block_type=output_block_type
            )

            if extracted_data:
                output_block = {
                    "step_block_name": artifact_name,
                    "step_block_type": output_block_type,
                    "order_number": 1,  # Default order
                    "status": "non_editable",  # Default status
                    "edited": False,
                    "data": extracted_data,
                }
                outputs.append(output_block)

    return outputs


def _extract_data_from_artifact_parts(
    parts: list[Part], block_type: str
) -> dict[str, Any]:
    """Extract data from artifact parts based on block type.

    Args:
        parts: List of artifact parts
        block_type: Expected block type for data extraction

    Returns:
        Extracted data structure

    """
    if block_type == "text_unstructured":
        # Extract text from text parts
        texts = []
        for part in parts:
            if part.root.kind == "text":
                texts.append(part.root.text)
        return {"text_unstructured": " ".join(texts) if texts else ""}

    elif block_type == "text_structured":
        # Extract structured data from data parts
        structured_data = {}
        for part in parts:
            if part.root.kind == "data":
                structured_data.update(part.root.data)
        return {"text_structured": structured_data}

    elif block_type == "dataset":
        # Extract dataset from data parts
        dataset = []
        for part in parts:
            if part.root.kind == "data":
                dataset.append(part.root.data)
        return {"dataset": dataset}

    elif block_type == "selection":
        # Extract selection from text parts (as per your mapping)
        selection_text = ""
        for part in parts:
            if part.root.kind == "text":
                selection_text = part.root.text
                break

        # Try to parse selection text back to selection format
        # This is a simple parser - you might want to enhance this
        selection_data = {"selected_option": selection_text, "custom_input": None}
        if "Custom:" in selection_text:
            parts_split = selection_text.split("Custom:")
            if len(parts_split) == 2:
                selection_data["selected_option"] = (
                    parts_split[0].replace("Selected:", "").strip()
                )
                selection_data["custom_input"] = parts_split[1].strip()

        return {"selection": selection_data}

    elif block_type == "email":
        # Extract email from data parts
        email_data = {}
        for part in parts:
            if part.root.kind == "data":
                email_data = part.root.data
                break
        return {"email": email_data}

    elif block_type == "tool":
        # Extract tool data from data parts
        tool_data = {}
        for part in parts:
            if part.root.kind == "data":
                tool_data = part.root.data
                break
        return {"tool": tool_data}

    elif block_type == "file":
        # Extract file info from file parts
        files = []
        for part in parts:
            if part.root.kind == "file":
                files.append(
                    {
                        "name": part.root.file.name,
                        "mime_type": part.root.file.mimeType,
                        "data": part.root.file.bytes
                        if hasattr(part.root.file, "bytes")
                        else None,
                        "url": part.root.file.uri
                        if hasattr(part.root.file, "uri")
                        else None,
                    }
                )
            elif part.root.kind == "text":
                # Fallback for text-based file paths
                files.append({"path": part.root.text})
        return {"files": files}

    # Default: return empty structure
    return {}
