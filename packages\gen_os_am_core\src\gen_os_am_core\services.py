"""Handling dynamic router inclusion from KM and CM services."""

import importlib.util
from logging import Logger

from fastapi import FastAPI

# Tags for the routers
WorkflowTags = ["Workflow Agents Functionalities"]
KnowledgeManagementTags = ["Knowledge Management"]


def services_availability_check() -> dict[str, bool]:
    """Check if KM, CM, and Conversational services are available."""
    km_available = False
    cm_available = False
    conversational_available = False

    try:
        km_available = (
            importlib.util.find_spec("gen_os_am_knowledge.api.main") is not None
        )
    except Exception:
        pass

    try:
        cm_available = importlib.util.find_spec("gen_os_am_workflows.api") is not None
    except Exception:
        pass

    try:
        conversational_available = (
            importlib.util.find_spec("gen_os_am_conversational.api.router") is not None
        )
    except Exception:
        pass

    return {
        "km": km_available,
        "cm": cm_available,
        "conversational": conversational_available,
    }


def services_router_inclusion(app: FastAPI, logger: Logger):
    """Dynamically include routes from other modules based on availability."""
    services_availability = services_availability_check()

    if services_availability["km"]:
        try:
            from gen_os_am_knowledge.api.main import app as km_app

            if hasattr(km_app, "router"):
                app.include_router(km_app.router, tags=KnowledgeManagementTags)
                logger.info("Successfully included Knowledge Management (KM) routes")
            else:
                logger.warning(
                    "KM module found,'router' attribute not found. Skipping KM routes."
                )
        except Exception as e:
            logger.error(f"Error importing KM router: {e}")
    else:
        logger.info("Knowledge Management (KM) module not found. Skipping KM routes.")

    if services_availability["cm"]:
        try:
            from gen_os_am_workflows.api import API

            cm_router = API().create_api().router
            app.include_router(cm_router, tags=WorkflowTags)
            logger.info("Successfully included Case Management (CM) routes")
        except Exception as e:
            logger.error(f"Error importing CM router: {e}")
    else:
        logger.info("Case Management (CM) module not found. Skipping CM routes.")

    if services_availability["conversational"]:
        try:
            from gen_os_am_conversational.api.router import (
                router as conversational_router,
            )
            from gen_os_am_conversational.api.router import telemetry_router

            app.include_router(
                conversational_router,
                tags=["Conversational Agents Functionalities"],
            )
            app.include_router(telemetry_router)
            logger.info(
                "Successfully included Conversational "
                "(conversations & telemetry) routes"
            )

        except Exception as e:
            logger.error(f"Error importing Conversational routers: {e}")
    else:
        logger.info("Conversational module not found. Skipping Conversational routes.")


async def services_database_check() -> dict[str, bool]:
    """Check if KM, CM, and Conversational databases are available."""
    services_availability = services_availability_check()

    # Check AM database first
    am_database_available = False
    try:
        from gen_os_am_core.database.session import SessionManager

        am_database_available = await SessionManager.check_connection()
    except Exception:
        pass

    km_database_available = False
    cm_database_available = False
    conversational_database_available = False

    if services_availability["km"]:
        try:
            from gen_os_am_knowledge.config.database import (
                SessionManager as KMSessionManager,
            )

            km_database_available = await KMSessionManager.check_connection()
        except ImportError:
            pass

    if services_availability["cm"]:
        try:
            from gen_os_am_workflows.database.session import (
                SessionManager as CMSessionManager,
            )

            cm_database_available = await CMSessionManager.check_connection()
        except Exception:
            pass

    if services_availability["conversational"]:
        try:
            from gen_os_am_conversational.database.session import (
                SessionManager as ConversationalSessionManager,
            )

            conversational_database_available = (
                await ConversationalSessionManager.check_connection()
            )
        except Exception:
            pass

    return {
        "am_db": am_database_available,
        "km": km_database_available,
        "cm": cm_database_available,
        "conversational": conversational_database_available,
    }
