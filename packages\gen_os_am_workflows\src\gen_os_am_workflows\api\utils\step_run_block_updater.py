"""Utils for the Case Management API."""

from typing import Literal

from gen_os_am_workflows.api.models.request import StepBlockRun
from gen_os_am_workflows.database.models import StepRun


class StepRunBlockUpdater:
    """A class to efficiently update blocks in a StepRun."""

    def __init__(self, step_run: StepRun):
        """Initialize the updater with a StepRun object."""
        self.step_run = step_run
        self._input_block_map = {
            block["step_block_name"]: i for i, block in enumerate(self.step_run.inputs)
        }
        self._output_block_map = {
            block["step_block_name"]: i for i, block in enumerate(self.step_run.outputs)
        }
        # Work with copies to ensure SQLAlchemy detects changes
        self._new_inputs = list(self.step_run.inputs)
        self._new_outputs = list(self.step_run.outputs)

    def _update_block(
        self, block_name: str, data: dict, block_type: Literal["input", "output"]
    ) -> None:
        """Update a single block's data."""
        if block_type == "input":
            block_map = self._input_block_map
            blocks = self._new_inputs
        else:
            block_map = self._output_block_map
            blocks = self._new_outputs

        block_index = block_map.get(block_name)
        if block_index is None:
            raise ValueError(f"Block with name {block_name} not found in step run {block_type}")

        # Get the original block to preserve other attributes
        original_block = blocks[block_index]
        original_block.pop("data", None)
        original_block.pop("edited", None)
        block_typed = StepBlockRun(**original_block, data=data, edited=True)
        blocks[block_index] = block_typed.model_dump()

    def update_input(self, block_name: str, data: dict) -> None:
        """Update an input block."""
        self._update_block(block_name, data, "input")

    def update_output(self, block_name: str, data: dict) -> None:
        """Update an output block."""
        self._update_block(block_name, data, "output")

    def get_updated_step_run(self) -> StepRun:
        """Get the StepRun with updated blocks."""
        self.step_run.inputs = self._new_inputs
        self.step_run.outputs = self._new_outputs
        return self.step_run
