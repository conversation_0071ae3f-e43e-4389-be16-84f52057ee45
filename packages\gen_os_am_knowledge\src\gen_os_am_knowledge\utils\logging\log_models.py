"""Log models for the KM."""

from datetime import datetime
from typing import Literal

from pydantic import BaseModel, field_validator


class ErrorLog(BaseModel):
    """Error log model."""

    code: int | None = None
    message: str | None = None
    stack_trace: str | None = None
    type_: str | None = None


class HTTPRequest(BaseModel):
    """HTTP request model."""

    method: Literal["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS", "HEAD"]
    bytes: int | None = None
    mime_type: str | None = None
    body_content: str | None = None  # Only include on DEBUG/ERROR logs


class HTTPResponse(BaseModel):
    """HTTP response model."""

    status_code: int
    bytes: int | None = None
    mime_type: str | None = None
    body_content: str | None = None  # Only include on DEBUG/ERROR logs

    @classmethod
    @field_validator("status_code")
    def check_status_code(cls, value: int) -> int:
        """Check the status code."""
        if not (100 <= value <= 599):
            raise ValueError("status_code must be between 100 and 599")
        return value


class BaseLog(BaseModel):
    """Base log model."""

    environment: str
    start: datetime
    end: datetime
    request: HTTPRequest | None = None
    response: HTTPResponse | None = None
    error: ErrorLog | None = None
