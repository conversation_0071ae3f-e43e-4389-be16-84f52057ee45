"""Knowledge management client."""

import httpx
from gen_os_sdk_emulator.agent_manager_client.common.auth_provider import AuthProvider
from gen_os_sdk_emulator.agent_manager_client.common.base_client import BaseClient


class KnowledgeManagementClient(BaseClient):
    """Knowledge management client."""

    def __init__(
        self,
        auth_provider: AuthProvider,
        service_url: str,
    ):
        """Initialize the knowledge management client."""
        super().__init__(auth_provider)
        self.service_url = service_url
        self._aclient = httpx.AsyncClient()

    async def create_tag(
        self, tag_name: str, trace_id: str | None = None, app_caller: str | None = None
    ) -> tuple[dict, str]:
        """Create a tag."""
        path = "/tag/"

        headers = await self._handle_headers(trace_id=trace_id, app_caller=app_caller)

        result = await self._aclient.post(
            f"{self.service_url}{path}",
            headers=headers,
            json={"tag_name": tag_name},
        )
        result.raise_for_status()
        return result.json()
