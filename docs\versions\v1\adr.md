**Status:** Accepted

**Date:** 2025-05-29

## Context

The GenOS platform is being developed as a unified environment for building and managing AI applications. As the system grows, it's critical to define clear boundaries and responsibilities for each core component and standardize how agents are invoked and monitored.

## Decision

We define the following architectural choices for core components:


#### GenOS Backend as Authorization and API Gateway Layer
The GenOS Backend acts as:
- The API gateway for frontend and external service communication.
- The authorization layer ensuring access control and request validation.
- It does not handle agent orchestration or business logic.


### Agent Manager as the Central Orchestrator
- The Agent Manager (AM) is responsible for orchestrating all agent executions.
- There is one Agent Manager a set of agents ranging from 1 to N.
- All agent invocations via the platform are routed through the AM using the Google's A2A communication protocol.
- AM handles both workflow and conversational agent flows.

### A2A Protocol for Agent Invocation
- The A2A protocol is used to:
    - Invoke an agent to execute a task (e.g., workflow step execution, answer a query).
- Pass contextual data and inputs/outputs between agents and AM.
- Request approvals (human-in-the-loop corrections) when needed.

### Observability via OpenTelemetry
OpenTelemetry is used to emit structured logs and traces.
- Agent activity (e.g., invocation times, decisions, inputs, output) is logged by the Agent to the GenOS Agent OTel Collector, that will forward them to some telemetry backend, as well as filter them to send only the relevant ones to the Agent Manager.

- Traces are critical for:
    - Tracking agent activity.
    - Debugging agent behavior.
    - Optimizing performance.
    - Auditing decisions for compliance.


## Consequences

- Separation of concerns ensures modularity and scalability.
- Agent Manager becomes a critical service and must be highly available.
- A2A standardization simplifies the integration of new agents.
- OpenTelemetry enables observability and future integration with tracing platforms (e.g., Grafana, Jaeger).
