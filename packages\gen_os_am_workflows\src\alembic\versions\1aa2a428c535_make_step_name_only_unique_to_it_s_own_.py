"""Make step name only unique to it's own workflow.

Revision ID: 1aa2a428c535
Revises: e7a58c11cce3
Create Date: 2025-06-02 14:04:04.572021

"""

from collections.abc import Sequence

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "1aa2a428c535"
down_revision: str | None = "e7a58c11cce3"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Make step name only unique to it's own workflow."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("step", schema=None) as batch_op:
        batch_op.drop_constraint("step_name_key", type_="unique")
        batch_op.create_unique_constraint("unique_step_name_per_workflow", ["name", "workflow_id"])

    # ### end Alembic commands ###


def downgrade() -> None:
    """Make step name unique to all steps in all workflows."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("step", schema=None) as batch_op:
        batch_op.drop_constraint("unique_step_name_per_workflow", type_="unique")
        batch_op.create_unique_constraint(
            "step_name_key", ["name"], postgresql_nulls_not_distinct=False
        )

    # ### end Alembic commands ###
