"""Tests for the agent conversation router."""

import uuid
from datetime import datetime, timezone

import pytest
from sqlalchemy import select

from gen_os_am_conversational.database.session import Session<PERSON>anager
from gen_os_am_conversational.models.models import Conversations, MessageTurn


class TestAgentConversationRouter:
    """Test cases for the agent conversation router."""

    @pytest.fixture
    def test_agent_id(self):
        """Test agent ID fixture."""
        return "test-agent-123"

    @pytest.fixture
    def test_conversation_data(self, test_agent_id):
        """Test conversation data fixture."""
        return {
            "agent_id": test_agent_id,
            "start_time": datetime.now(timezone.utc),
            "end_time": None,
            "custom_fields": {"test_field": "test_value"},
        }

    @pytest.fixture
    def test_message_data(self):
        """Test message data fixture."""
        return {
            "conversation_id": str(uuid.uuid4()),
            "trace_id": str(uuid.uuid4()),
            "timestamp": datetime.now(timezone.utc),
            "role": "user",
            "content_text": "Hello, how are you?",
            "raw_message": {"type": "text", "content": "Hello, how are you?"},
        }

    @pytest.mark.asyncio
    async def test_list_agent_conversations_success(self, client, test_agent_id):
        """Test successful listing of agent conversations."""
        # Create a test conversation in the database
        async with SessionManager.get_session() as db:
            conversation = Conversations(
                agent_id=test_agent_id,
                start_time=datetime.now(timezone.utc),
                custom_fields={"test": "data"},
            )
            db.add(conversation)
            await db.commit()
            await db.refresh(conversation)

        # Test the endpoint
        response = client.get(f"/agents/{test_agent_id}/conv/v1/conversations")
        assert response.status_code == 200

        data = response.json()
        assert "items" in data
        assert "pagination" in data
        assert len(data["items"]) >= 1

    @pytest.mark.asyncio
    async def test_list_agent_conversations_with_filters(self, client, test_agent_id):
        """Test listing conversations with filters."""
        response = client.get(
            f"/agents/{test_agent_id}/conv/v1/conversations",
            params={
                "offset": 0,
                "limit": 10,
                "sort_field": "start_time",
                "sort_order": "desc",
                "search": "test",
            },
        )
        assert response.status_code == 200

    @pytest.mark.asyncio
    async def test_get_conversation_metadata_success(self, client, test_agent_id):
        """Test successful retrieval of conversation metadata."""
        # Create a test conversation
        async with SessionManager.get_session() as db:
            conversation = Conversations(
                agent_id=test_agent_id,
                start_time=datetime.now(timezone.utc),
                custom_fields={"test": "data"},
            )
            db.add(conversation)
            await db.commit()
            await db.refresh(conversation)

        # Test the endpoint
        response = client.get(f"/agents/{test_agent_id}/conv/v1/conversations/{conversation.id}")
        assert response.status_code == 200

        data = response.json()
        assert data["id"] == str(conversation.id)
        assert data["agent_id"] == test_agent_id

    @pytest.mark.asyncio
    async def test_get_conversation_metadata_not_found(self, client, test_agent_id):
        """Test conversation metadata retrieval for non-existent conversation."""
        fake_conversation_id = str(uuid.uuid4())
        response = client.get(
            f"/agents/{test_agent_id}/conv/v1/conversations/{fake_conversation_id}"
        )
        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_get_conversation_metadata_wrong_agent(self, client):
        """Test conversation metadata retrieval for wrong agent."""
        # Create a conversation for one agent
        agent1_id = "agent-1"
        agent2_id = "agent-2"

        async with SessionManager.get_session() as db:
            conversation = Conversations(
                agent_id=agent1_id,
                start_time=datetime.now(timezone.utc),
            )
            db.add(conversation)
            await db.commit()
            await db.refresh(conversation)

        # Try to access it with a different agent ID
        response = client.get(f"/agents/{agent2_id}/conv/v1/conversations/{conversation.id}")
        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_list_conversation_messages_success(self, client, test_agent_id):
        """Test successful listing of conversation messages."""
        # Create a test conversation and message
        async with SessionManager.get_session() as db:
            conversation = Conversations(
                agent_id=test_agent_id,
                start_time=datetime.now(timezone.utc),
            )
            db.add(conversation)
            await db.commit()
            await db.refresh(conversation)

            message = MessageTurn(
                conversation_id=conversation.id,
                timestamp=datetime.now(timezone.utc),
                role="user",
                content_text="Test message",
            )
            db.add(message)
            await db.commit()

        # Test the endpoint
        response = client.get(
            f"/agents/{test_agent_id}/conv/v1/conversations/{conversation.id}/messages"
        )
        assert response.status_code == 200

        data = response.json()
        assert "items" in data
        assert "pagination" in data
        assert len(data["items"]) >= 1

    @pytest.mark.asyncio
    async def test_list_conversation_messages_with_filters(self, client, test_agent_id):
        """Test listing messages with filters."""
        # Create a test conversation
        async with SessionManager.get_session() as db:
            conversation = Conversations(
                agent_id=test_agent_id,
                start_time=datetime.now(timezone.utc),
            )
            db.add(conversation)
            await db.commit()
            await db.refresh(conversation)

        response = client.get(
            f"/agents/{test_agent_id}/conv/v1/conversations/{conversation.id}/messages",
            params={"offset": 0, "limit": 10},
        )
        assert response.status_code == 200

    @pytest.mark.asyncio
    async def test_list_conversation_messages_not_found(self, client, test_agent_id):
        """Test message listing for non-existent conversation."""
        fake_conversation_id = str(uuid.uuid4())
        response = client.get(
            f"/agents/{test_agent_id}/conv/v1/conversations/{fake_conversation_id}/messages"
        )
        assert response.status_code == 404
