[project]
name = "gen-os-sdk-emulator"
version = "0.1.0"
description = "Default template for PDM package"
authors = [
    {name = "DareData", email = "<EMAIL>"},
]
requires-python = ">=3.12"
readme = "README.md"
license = {text = "Proprietary"}

dependencies = ["pydantic>=2.10.5", "httpx>=0.28.1", "typing-inspect>=0.9.0"]

[project.optional-dependencies]
gcp = [
    "google-cloud-pubsub>=2.29.0",
    "google-cloud-storage>=2.18.2",
]

[dependency-groups]
# dev dependencies that won't appear in the package distribution metadata
tests = [
    "pytest>=8.3.5",
    "pytest-cov<7.0.0,>=6.0.0",
    "pytest-asyncio>=0.26.0",
]
dev = [
    "pre-commit<=4.0.0,>=3.8.0",
    "isort<6.0.0,>=5.13.2",
    "ruff>=0.11.6",
    "ipykernel<=7.0.0,>=6.29.5",
]

[tool.pytest.ini_options]
asyncio_default_fixture_loop_scope = "session"

[build-system]
requires = ["pdm-backend"]
build-backend = "pdm.backend"

[tool.pdm.build]
includes = ["src"]
package-dir = "src"

[tool.pdm]
distribution = true
