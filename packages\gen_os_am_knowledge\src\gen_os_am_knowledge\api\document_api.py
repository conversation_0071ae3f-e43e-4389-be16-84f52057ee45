"""API for handling document."""

import json
import logging
import mimetypes
import urllib
import uuid
from datetime import datetime
from io import BytesIO

from fastapi import APIRouter, File, Form, HTTPException, Query, UploadFile
from fastapi.responses import JSONResponse, StreamingResponse
from pydantic import BaseModel

from gen_os_am_knowledge.api.utils import error_handling_decorator, validate_boolean
from gen_os_am_knowledge.config.logger_config import get_api_logger
from gen_os_am_knowledge.sdk.filesystem_connectors.base import BaseFileSystemConnector
from gen_os_am_knowledge.sdk.models import Document
from gen_os_am_knowledge.sdk.models.common import DEFAULT_FOLDER
from gen_os_am_knowledge.sdk.operations import DocumentService
from gen_os_am_knowledge.sdk.operations.utils import NOT_PROVIDED


class SendDocumentIds(BaseModel):
    """Model for sending document IDs in requests."""

    document_ids: list[uuid.UUID]


class DocumentAPI:
    """API class for handling document.

    Args:
        storage_connector: The storage connector to use for storing the documents
        prefix: The prefix to use for the API routes

    Methods:
        get_router: returns the router for the API

    """

    def __init__(
        self,
        storage_connector: BaseFileSystemConnector,
        prefix: str = "",
        logger: logging.Logger = get_api_logger(),
    ):
        """Initialize the DocumentAPI class."""
        self.prefix = prefix
        self.document_service = DocumentService(
            storage_connector, logger=logger.getChild("sdk")
        )

    def get_router(self) -> APIRouter:
        """Get the router for the document API.

        Returns:
            APIRouter: The router for the document API

        """
        router = APIRouter(prefix=self.prefix + "/document")
        extra_router = APIRouter(prefix=self.prefix)

        master_router = APIRouter()

        @router.post("/")
        @error_handling_decorator
        async def create_document(
            file: UploadFile = File(),
            folder_id: uuid.UUID = Form(DEFAULT_FOLDER.id),
            original_path: str | None = Form(None),
            user_id: uuid.UUID | None = Form(None),
            meta: str | None = Form(None),
            ingestion_id: uuid.UUID | None = Form(None),
            tags: str | None = Form(None),
            draft: bool = Form(False),
        ) -> JSONResponse:
            """Create a new document.

            GET prefix/document/

            Additional fields are passed as form data.

            Either user_id or ingestion_id must be provided to create a document.

            Args:
                file: UploadFile: The file to upload
                original_path: str: The original path of the file, optional
                folder_id: uuid.UUID: The folder id to store the document, optional
                user_id: uuid.UUID: The user id who created the document, optional
                meta: str: The metadata of the document -
                    json serializable dict as str, optional
                ingestion_id: uuid.UUID: The ingestion id of the document, optional
                tags: str: The tags of the document -
                    json serializable list as str, optional
                draft: bool: Whether the document is a draft, optional

            Returns:
                JSONResponse: The created document instance

            """
            meta_dict = json.loads(meta) if meta else None
            tags = json.loads(tags) if tags else None
            draft = validate_boolean(draft)
            filename = file.filename
            file_bytes = BytesIO(await file.read())
            file_bytes.name = filename
            result = await self.document_service.create_document(
                file=file_bytes,
                original_path=original_path,
                folder_id=folder_id,
                user_id=user_id,
                meta=meta_dict,
                ingestion_id=ingestion_id,
                tags=tags,
                draft=draft,
            )
            # update hash to string not to have truncated stuff
            result.hash = str(result.hash)
            return JSONResponse(content=result.model_dump(mode="json"), status_code=201)

        @router.delete("/{document_id}/{user_id}")
        @error_handling_decorator
        async def delete_document(
            document_id: uuid.UUID, user_id: uuid.UUID, draft: bool = False
        ) -> Document:
            """Delete a document.

            DELETE prefix/document/{document_id}/{user_id}

            Args:
                document_id: uuid.UUID: The id of the document to delete
                user_id: uuid.UUID: The id of the user who is deleting the document
                draft: bool: Whether to delete the document as a draft, optional

            Returns:
                Document: The deleted document instance

            """
            draft = validate_boolean(draft)
            result = await self.document_service.delete_document(
                document_id=document_id, user_id=user_id, draft=draft
            )
            if result.success:
                # update hash to string not to have truncated stuff
                result.deleted_instance.hash = str(result.deleted_instance.hash)
                return result.deleted_instance
            else:
                raise HTTPException(status_code=404, detail=result.error)

        @router.post("/{document_id}/{user_id}/restore")
        @error_handling_decorator
        async def restore_document(
            document_id: uuid.UUID,
            user_id: uuid.UUID,
            draft: bool = False,
        ) -> Document:
            """Restore a document.

            POST prefix/document/{document_id}/{user_id}/restore

            Args:
                document_id: uuid.UUID: The id of the document to restore
                user_id: uuid.UUID: The id of the user who is restoring the document
                draft: bool: Whether to restore the document as a draft, optional

            Returns:
                Document: The restored document instance

            """
            result = await self.document_service.restore_document(
                document_id=document_id, user_id=user_id, draft=draft
            )
            if result.success:
                result.updated_instance.hash = str(result.updated_instance.hash)
                return result.updated_instance
            else:
                raise HTTPException(status_code=404, detail=result.error)

        @router.get("/{document_id}")
        @error_handling_decorator
        async def get_document(document_id: uuid.UUID) -> Document | None:
            """Get a document.

            GET prefix/document/{document_id}

            Args:
                document_id: uuid.UUID: The id of the document to get

            Returns:
                Document: The document instance

            """
            result = await self.document_service.get_document(document_id)
            if result:
                result.hash = str(result.hash)
            return result

        @router.get("/")
        @error_handling_decorator
        async def search_documents(
            filename: str | None = None,
            folder_name: str | None = None,
            kind: str | None = None,
            tags: str | None = None,
            created_by: str | None = None,
            last_modified_by: str | None = None,
            ingestion_id: uuid.UUID | None = None,
            document_status: Document._DOC_STATUS_TYPE | None = None,
            metadata_field: str | None = None,
            search_deleted: bool = False,
            draft: bool = False,
            draft_only: bool = False,
            limit: int = 20,
            offset: int = 0,
        ) -> list[Document]:
            """Search for documents based on the provided filters.

            GET prefix/document/

            If nothing is provided, all documents are returned with limit of 20.

            Args:
                filename: str: The filename to search for, optional
                folder_name: str: The folder name to search for, optional
                kind: str: The kind of document to search for, optional
                tags: str: The tags to search for, optional
                created_by: str: The user who created the document, optional
                last_modified_by: str: The user who last modified the document, optional
                ingestion_id: uuid.UUID: The ingestion id of the document, optional
                document_status: Document._DOC_STATUS_TYPE:
                    The status of the document, optional
                metadata_field: str: The metadata field to search for, optional
                search_deleted: bool: Whether to search for deleted documents, optional
                draft: bool: Whether to include draft documents, optional
                draft_only: bool: Whether to search for draft documents only, optional
                limit: int: The limit of documents to return, optional
                offset: int: The offset of documents to return, optional

            Returns:
                list[Document]: The list of documents that match the search criteria

            """
            if isinstance(tags, str):
                tags = json.loads(tags)
            if isinstance(metadata_field, str):
                metadata_field = json.loads(metadata_field)
            search_deleted = validate_boolean(search_deleted)
            draft = validate_boolean(draft)
            draft_only = validate_boolean(draft_only)
            result = await self.document_service.search_documents(
                filename=filename,
                folder_name=folder_name,
                tags=tags,
                created_by=created_by,
                last_modified_by=last_modified_by,
                ingestion_id=ingestion_id,
                document_status=document_status,
                metadata_field=metadata_field,
                kind=kind,
                limit=limit,
                offset=offset,
                search_deleted=search_deleted,
                draft=draft,
                draft_only=draft_only,
            )
            # update hash to str
            for doc in result:
                doc.hash = str(doc.hash)
            return result

        @router.patch("/status/{document_id}/{user_id}")
        @error_handling_decorator
        async def update_document_status(
            document_id: uuid.UUID,
            user_id: uuid.UUID,
            status: Document._DOC_STATUS_TYPE = Form(...),
        ):
            """Update the status of a document.

            PATCH prefix/document/status/{document_id}/{user_id}

            Args:
                document_id: uuid.UUID: The id of the document to update
                user_id: uuid.UUID: The id of the user who last modified the document
                status: Document._DOC_STATUS_TYPE: The new status of the document

            Returns:
                UpdateResult: The result of the update operation

            """
            result = await self.document_service.update_document_status(
                document_id=document_id,
                user_id=user_id,
                status=status,
            )
            if result.success:
                result.updated_instance.hash = str(result.updated_instance.hash)
            return result

        @router.patch("/{document_id}/{last_modified_by_id}")
        @error_handling_decorator
        async def update_document(
            document_id: uuid.UUID,
            last_modified_by_id: uuid.UUID,
            filename: str | type[NOT_PROVIDED] = Form(NOT_PROVIDED),
            folder_id: uuid.UUID | None | type[NOT_PROVIDED] = Form(NOT_PROVIDED),
            tags: str | None | type[NOT_PROVIDED] = Form(NOT_PROVIDED),
            meta: str | None | type[NOT_PROVIDED] = Form(NOT_PROVIDED),
            original_path: str | None | type[NOT_PROVIDED] = Form(NOT_PROVIDED),
            document_hash: str | type[NOT_PROVIDED] = Form(NOT_PROVIDED),
            size: int | type[NOT_PROVIDED] = Form(NOT_PROVIDED),
            deleted_date: str | None | type[NOT_PROVIDED] = Form(NOT_PROVIDED),
            draft: bool | type[NOT_PROVIDED] = Form(NOT_PROVIDED),
            file: UploadFile | None = File(None),
        ):
            """Update a document.

            PATCH prefix/document/{document_id}/{last_modified_by_id}

            Additional fields are passed as form data.

            Args:
                document_id: uuid.UUID: The id of the document to update
                last_modified_by_id: uuid.UUID: The id of the user who
                    last modified the document
                filename: str: The new filename of the document, optional
                folder_id: uuid.UUID: The new folder id of the document, optional
                tags: str: The new tags of the document - json serializable list
                    as str, optional
                meta: str: The new metadata of the document - json serializable
                    dict as str, optional
                original_path: str: The new original path of the document, optional
                document_hash: str: The new hash of the document, optional
                size: int: The new size of the document, optional
                deleted_date: str: The new deleted date of the document, optional
                draft: bool: Whether the document is a draft, optional
                file: UploadFile: The new file to upload, optional

            Returns:
                UpdateResult: The result of the update operation

            """
            if isinstance(tags, str):
                tags = json.loads(tags)
            if isinstance(meta, str):
                meta = json.loads(meta)
            if isinstance(deleted_date, str):
                if deleted_date == "null":
                    deleted_date = json.loads(deleted_date)
                else:
                    deleted_date = datetime.fromisoformat(deleted_date)
            if document_hash is not NOT_PROVIDED:
                document_hash = int(document_hash)  # type: ignore
            draft = validate_boolean(draft) if draft is not NOT_PROVIDED else draft  # type: ignore
            if file:
                name = file.filename
                file = BytesIO(await file.read())
                file.name = name

            result = await self.document_service.update_document(
                document_id=document_id,
                last_modified_by_id=last_modified_by_id,
                filename=filename,
                folder_id=folder_id,
                tags=tags,
                meta=meta,
                original_path=original_path,
                file=file,
                hash_=document_hash,  # type: ignore
                size=size,  # type: ignore
                deleted_date=deleted_date,  # type: ignore
                draft=draft,  # type: ignore
            )

            return result

        @router.get("/{document_id}/download")
        @error_handling_decorator
        async def download_document(document_id: uuid.UUID) -> StreamingResponse:
            """Download a document.

            GET prefix/document/{document_id}/download

            Args:
                document_id: uuid.UUID: The id of the document to download

            Returns:
                StreamingResponse: The streaming response of the document file

            """
            result = await self.document_service.download_document(document_id)
            return StreamingResponse(
                result[0],
                media_type=mimetypes.guess_type(result[1])[0]
                or "application/octet-stream",
                headers={
                    "Content-Disposition": (
                        f"attachment; filename={urllib.parse.quote(result[1])}"
                    )
                },
            )

        @router.get("/{document_id}/download_presigned_url")
        @error_handling_decorator
        async def presigned_url_download(document_id: uuid.UUID) -> str:
            """Get a presigned URL to download a document.

            GET prefix/document/{document_id}/download_presigned_url

            Args:
                document_id: uuid.UUID: The id of the document to download

            Returns:
                str: The presigned URL to download the document

            """
            url = await self.document_service.get_presigned_url_download(document_id)
            return url

        # GET /updated_documents/?from={somedate}&to{optional_somedate} for the agent to quickly get a list of documents that were updated/created # noqa: E501
        @extra_router.get("/updated_documents")
        @error_handling_decorator
        async def search_updated_documents(  # noqa: D417
            from_: str = Query(..., alias="from"),
            to: str | None = None,
            limit: int = 20,
            offset: int = 0,
        ) -> list[Document]:
            """Search for documents based on the provided filters.

            GET prefix/updated_documents/

            If nothing is provided, all documents are returned with limit of 20.

            Args:
                from: str: The date to search for in iso format
                to: str: The date to search for, optional
                limit: int: The limit of documents to return, optional
                offset: int: The offset of documents to return, optional

            Returns:
                list[Document]: The list of documents that match the search criteria

            """
            from_ = datetime.fromisoformat(from_)
            if to:
                to = datetime.fromisoformat(to)
            result = await self.document_service.search_documents(
                updated_from=from_, updated_to=to, limit=limit, offset=offset
            )
            # update hash to str
            for doc in result:
                doc.hash = str(doc.hash)
            return result

        @extra_router.delete("/cleanup_deleted_documents")
        @error_handling_decorator
        async def cleanup_deleted_documents():
            """Cleanup documents.

            Cleanup documents from storage and DB that
            have deleted_date before current moment.

            DELETE prefix/cleanup_deleted_documents/

            Returns:
                List[Document]: The list of deleted documents

            """
            result = await self.document_service.cleanup_deleted_documents()
            return result

        @extra_router.get("/ingestions_sync_status")
        @error_handling_decorator
        async def get_ingestions_sync_status(
            ingestion_ids: str | None = None,
            ingestion_descriptions: str | None = None,
        ) -> dict[uuid.UUID, dict[str, int]]:
            """Get the ingestion sync status.

            GET prefix/ingestions_sync_status/

            Args:
                ingestion_ids:  The ingestion ids to get the sync status for
                    passed as str with (list[uuid.UUID]) inside, optional
                ingestion_descriptions:  The ingestion descriptions to get the
                    sync status for passed as str with (list[str]) inside, optional

            Returns:
                Dict: The ingestions_x_documents sync status
                Example:
                {
                    "ingestion_id": {
                        "Syncing": 1,
                        "Sync error": 2,
                        "Synced": 3
                    }
                }

            """
            if isinstance(ingestion_ids, str):
                ingestion_ids = json.loads(ingestion_ids)
            if isinstance(ingestion_descriptions, str):
                ingestion_descriptions = json.loads(ingestion_descriptions)

            result = await self.document_service.get_ingestions_stats(
                ingestion_ids=ingestion_ids,
                ingestion_descriptions=ingestion_descriptions,
            )
            return result

        @extra_router.patch("/publish/{user_id}")
        @error_handling_decorator
        async def publish_documents(
            user_id: uuid.UUID,
            payload: SendDocumentIds,
        ) -> list[uuid.UUID]:
            """Publish several documents.

            PATCH prefix/publish/{user_id}
            Body example:
            {
                "document_ids": ["uuid1", "uuid2"]
            }
            """
            document_ids = payload.document_ids
            published_documents = []
            for doc_id in document_ids:
                result = await self.document_service.publish_document(
                    document_id=doc_id, user_id=user_id
                )
                if result.success:
                    published_documents.append(result.updated_instance.id)
            return published_documents

        # @extra_router.patch("/draft/{user_id}")
        # @error_handling_decorator
        # async def draft_documents(
        #     user_id: uuid.UUID,
        #     payload: SendDocumentIds,
        # ) -> list[uuid.UUID]:
        #     """
        #     Draft several documents.

        #     PATCH prefix/draft/{user_id}
        #     Body example:
        #     {
        #         "document_ids": ["uuid1", "uuid2"]
        #     }
        #     """
        #     document_ids = payload.document_ids
        #     drafted_documents = []
        #     for doc_id in document_ids:
        #         result = await self.document_service.draft_document(
        #             document_id=doc_id, user_id=user_id
        #         )
        #         if result.success:
        #             drafted_documents.append(result.updated_instance.id)
        #     return drafted_documents

        master_router.include_router(router)
        master_router.include_router(extra_router)
        return master_router
