"""Agent validation middleware for the Agent Manager API."""

import logging
from collections.abc import Callable

from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, Request, Response
from sqlalchemy import select
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse

from gen_os_am_core.database.models.agents import Agent
from gen_os_am_core.database.session import SessionManager


class AgentValidationMiddleware(BaseHTTPMiddleware):
    """Middleware to validate agent_id from path parameters."""

    def __init__(self, app, logger: logging.Logger | None = None):
        """Initialize the middleware.

        Args:
            app: The FastAPI application
            logger: Optional logger instance

        """
        super().__init__(app)
        self.logger = logger or logging.getLogger(__name__)

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Dispatch the request with agent validation.

        Args:
            request: The incoming request
            call_next: The next middleware or endpoint

        Returns:
            Response: The response from the next middleware or endpoint

        Raises:
            HTTPException: If agent_id is missing or agent not found

        """
        # Extract agent_id from URL path pattern /agents/{agent_id}/
        agent_id = None
        path = request.url.path

        if path.startswith("/agents/"):
            path_parts = path.split("/")
            if len(path_parts) >= 3:
                agent_id = path_parts[2]

        if "/agents/" in path and not agent_id:
            return JSONResponse(
                status_code=400, content={"detail": "Agent ID is required"}
            )

        # Validate agent_id if found
        if agent_id:
            try:
                async with SessionManager.get_session() as db:
                    agent = await db.execute(select(Agent).where(Agent.id == agent_id))
                    agent = agent.scalars().first()
                    if not agent:
                        raise HTTPException(
                            status_code=404,
                            detail=f"Agent with ID {agent_id} not found",
                        )
            except HTTPException:
                return JSONResponse(
                    status_code=404,
                    content={"detail": f"Agent with ID {agent_id} not found"},
                )
            except Exception as e:
                # Catch database errors and other exceptions
                self.logger.error(f"Error validating agent {agent_id}: {str(e)}")
                return JSONResponse(
                    status_code=400,
                    content={"detail": f"Error validating agent {agent_id}: {str(e)}"},
                )

        # Continue with the request
        response = await call_next(request)
        return response
