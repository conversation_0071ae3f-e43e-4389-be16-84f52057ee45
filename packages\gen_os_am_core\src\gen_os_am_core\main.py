"""Main application for the Agent Manager (AM).

This FastAPI application dynamically aggregates the APIs from the
Knowledge Management (KM) and Case Management (CM) submodules if
they are available in the environment.
"""

import logging

import uvicorn

from gen_os_am_core.api import AgentManagerAPI
from gen_os_am_core.settings import Settings

settings = Settings.get_settings()

# Configure logging
logging.basicConfig(level=settings.LOG_LEVEL)
logger = logging.getLogger(__name__)

# Create the FastAPI application using the AgentManagerAPI class
am_api = AgentManagerAPI(logger=logger)
am_app = am_api.create_api()

if __name__ == "__main__":
    logger.info(f"Starting API with {settings.API_WORKERS} workers")

    config = uvicorn.Config(
        app=am_app,
        host=settings.API_LISTEN_HOST,
        port=settings.API_LISTEN_PORT,
        workers=settings.API_WORKERS,
        timeout_keep_alive=settings.API_TIMEOUT_KEEP_ALIVE,
        log_level=settings.LOG_LEVEL.lower(),
        reload=settings.API_RELOAD,
    )
    server = uvicorn.Server(config)
    server.run()
