"""Tests for the agent issues router."""

import uuid

import pytest
import pytest_asyncio
from sqlalchemy import select

from gen_os_am_core.database.models.agent_issue import (
    Issue,
    IssueLog,
    ReportedIncidentConversation,
    ReportedIncidentWorkflow,
)
from gen_os_am_core.database.models.agents import Agent
from gen_os_am_core.database.session import SessionManager


class TestAgentIssuesRouter:
    """Test cases for the agent issues router."""

    @pytest.fixture
    def test_agent_id(self):
        """Test agent ID fixture."""
        return "test-agent-issues-456"

    @pytest.fixture
    def test_user_id(self):
        """Test user ID for created_by field."""
        return "test-user-789"

    @pytest_asyncio.fixture
    async def create_test_agent(self, test_agent_id):
        """Create a test agent in the database."""
        async with SessionManager.get_session() as db:
            agent = Agent(
                id=test_agent_id,
                name="Test Agent",
                url="http://test-agent",
                description="Test agent for issues router tests",
            )
            db.add(agent)
            await db.commit()
            await db.refresh(agent)

            yield agent

            # Cleanup
            await db.delete(agent)
            await db.commit()

    @pytest.fixture
    def conversation_incident_data(self):
        """Sample conversation incident data."""
        return {
            "conversation_id": str(uuid.uuid4()),
            "answer": "This is a sample answer from a conversation.",
            "description": "Description of the conversation incident.",
            "severity": "high",
        }

    @pytest.fixture
    def workflow_incident_data(self):
        """Sample workflow incident data."""
        return {
            "workflow_execution_id": str(uuid.uuid4()),
            "step": "step_name_example",
            "description": "Description of the workflow incident.",
            "severity": "medium",
        }

    @pytest.fixture
    def issue_create_request_data(
        self, conversation_incident_data, workflow_incident_data
    ):
        """Sample issue creation request data."""
        return {
            "description": "A test issue description.",
            "issue_type": "GenAI",
            "state": "open",
            "reported_incidents_conversations": [conversation_incident_data],
            "reported_incidents_workflows": [workflow_incident_data],
        }

    @pytest_asyncio.fixture
    async def create_test_issue(
        self,
        test_agent_id,
        issue_create_request_data,
        test_user_id,
        create_test_agent,
    ):
        """Create a test issue with incidents for testing get/update/delete."""
        async with SessionManager.get_session() as db:
            issue = Issue(
                agent_id=test_agent_id,
                description=issue_create_request_data["description"],
                issue_type=issue_create_request_data["issue_type"],
                state=issue_create_request_data["state"],
                deleted=False,
            )

            for conv_data in issue_create_request_data[
                "reported_incidents_conversations"
            ]:
                issue.reported_incidents_conversations.append(
                    ReportedIncidentConversation(**conv_data, created_by=test_user_id)
                )

            for wf_data in issue_create_request_data["reported_incidents_workflows"]:
                issue.reported_incidents_workflows.append(
                    ReportedIncidentWorkflow(**wf_data, created_by=test_user_id)
                )

            db.add(issue)
            await db.commit()
            await db.refresh(
                issue,
                ["reported_incidents_conversations", "reported_incidents_workflows"],
            )
            yield issue

    @pytest.mark.asyncio
    async def test_create_issue_success(
        self,
        client,
        test_agent_id,
        test_user_id,
        issue_create_request_data,
        create_test_agent,
    ):
        """Test successful creation of an issue with all related data."""
        headers = {"X-User-ID": test_user_id}
        response = client.post(
            f"/agents/{test_agent_id}/core/v1/issue",
            json=issue_create_request_data,
            headers=headers,
        )

        assert response.status_code == 201
        data = response.json()

        # Verify response body
        assert data["description"] == issue_create_request_data["description"]
        assert data["agent_id"] == test_agent_id
        assert data["state"] == "open"
        assert len(data["reported_incidents_conversations"]) == 1
        assert len(data["reported_incidents_workflows"]) == 1

        conv_incident = data["reported_incidents_conversations"][0]
        assert conv_incident["created_by"] == test_user_id
        assert (
            conv_incident["conversation_id"]
            == issue_create_request_data["reported_incidents_conversations"][0][
                "conversation_id"
            ]
        )

        # Verify database state
        async with SessionManager.get_session() as db:
            # Convert string UUID to UUID object for database query
            issue_id = uuid.UUID(data["id"])
            stmt = select(Issue).where(Issue.id == issue_id)
            result = await db.execute(stmt)
            issue_in_db = result.scalar_one_or_none()

            assert issue_in_db is not None
            assert issue_in_db.agent_id == test_agent_id
            assert issue_in_db.description == issue_create_request_data["description"]

            # Verify relationships were created
            await db.refresh(
                issue_in_db,
                ["reported_incidents_conversations", "reported_incidents_workflows"],
            )
            assert len(issue_in_db.reported_incidents_conversations) == 1
            assert len(issue_in_db.reported_incidents_workflows) == 1
            assert (
                issue_in_db.reported_incidents_conversations[0].created_by
                == test_user_id
            )

    @pytest.mark.asyncio
    async def test_create_issue_missing_user_id_header(
        self, client, test_agent_id, issue_create_request_data, create_test_agent
    ):
        """Test issue creation when the X-User-ID header is missing."""
        response = client.post(
            f"/agents/{test_agent_id}/core/v1/issue", json=issue_create_request_data
        )

        assert response.status_code == 201
        data = response.json()
        assert data["reported_incidents_conversations"][0]["created_by"] is None
        assert data["reported_incidents_workflows"][0]["created_by"] is None

        # Verify database state
        async with SessionManager.get_session() as db:
            # Convert string UUID to UUID object for database query
            issue_id = uuid.UUID(data["id"])
            stmt = select(ReportedIncidentConversation).where(
                ReportedIncidentConversation.issue_id == issue_id
            )
            result = await db.execute(stmt)
            incident_in_db = result.scalar_one()
            assert incident_in_db.created_by is None

    @pytest.mark.asyncio
    async def test_create_issue_invalid_data(
        self, client, test_agent_id, create_test_agent
    ):
        """Test issue creation with an invalid request body."""
        invalid_data = {
            "description": "This is a description",
            # Missing issue_type, state, and incidents
        }
        response = client.post(
            f"/agents/{test_agent_id}/core/v1/issue", json=invalid_data
        )
        assert response.status_code == 422

    @pytest.mark.asyncio
    async def test_create_issue_no_incidents(
        self, client, test_agent_id, test_user_id, create_test_agent
    ):
        """Test creating an issue with no incidents."""
        issue_data = {
            "description": "A test issue with no incidents.",
            "issue_type": "GenAI",
            "state": "open",
            "reported_incidents_conversations": [],
            "reported_incidents_workflows": [],
        }
        headers = {"X-User-ID": test_user_id}
        response = client.post(
            f"/agents/{test_agent_id}/core/v1/issue", json=issue_data, headers=headers
        )

        assert response.status_code == 201
        data = response.json()
        assert data["description"] == issue_data["description"]
        assert len(data["reported_incidents_conversations"]) == 0
        assert len(data["reported_incidents_workflows"]) == 0

        # Verify in DB
        async with SessionManager.get_session() as db:
            # Convert string UUID to UUID object for database query
            issue_id = uuid.UUID(data["id"])
            issue = await db.get(Issue, issue_id)
            assert issue is not None
            await db.refresh(
                issue,
                ["reported_incidents_conversations", "reported_incidents_workflows"],
            )
            assert len(issue.reported_incidents_conversations) == 0
            assert len(issue.reported_incidents_workflows) == 0

    @pytest.mark.asyncio
    async def test_get_issue_detail_not_found(
        self, client, test_agent_id, create_test_agent
    ):
        """Test retrieving a non-existent issue."""
        non_existent_issue_id = uuid.uuid4()
        response = client.get(
            f"/agents/{test_agent_id}/core/v1/issue/{non_existent_issue_id}"
        )
        assert response.status_code == 404

    @pytest.mark.asyncio
    async def test_update_issue_success(self, create_test_issue, client, test_agent_id):
        """Test successfully updating an issue's state and description."""
        issue = create_test_issue
        issue_id = issue.id

        update_payload = {
            "state": "closed_fixed",
            "close_desc": "Resolved by test case.",
        }

        response = client.patch(
            f"/agents/{test_agent_id}/core/v1/issue/{issue_id}",
            json=update_payload,
        )

        assert response.status_code == 200
        data = response.json()
        assert data["state"] == "closed_fixed"
        assert data["close_desc"] == "Resolved by test case."

        # Verify in DB
        async with SessionManager.get_session() as db:
            updated_issue = await db.get(Issue, issue_id)
            assert updated_issue.state == "closed_fixed"
            assert updated_issue.close_desc == "Resolved by test case."

    @pytest.mark.asyncio
    async def test_soft_delete_incident_success(
        self, client, test_agent_id, create_test_issue, test_user_id
    ):
        """Test successfully soft-deleting an incident."""
        issue = create_test_issue
        incident_to_delete = issue.reported_incidents_conversations[0]
        incident_id = incident_to_delete.id

        delete_payload = {"is_deleted": True, "deleted_by": test_user_id}

        response = client.patch(
            f"/agents/{test_agent_id}/core/v1/incident/{incident_id}",
            json=delete_payload,
        )

        assert response.status_code == 200
        data = response.json()
        assert data["id"] == str(incident_id)
        assert data["is_deleted"] is True
        assert data["deleted_by"] == test_user_id
        assert data["kind"] == "conversation"

        # Verify in DB
        async with SessionManager.get_session() as db:
            deleted_incident = await db.get(ReportedIncidentConversation, incident_id)
            assert deleted_incident.is_deleted is True
            assert deleted_incident.deleted_by == test_user_id

    @pytest.mark.asyncio
    async def test_soft_delete_incident_restore(
        self, client, test_agent_id, create_test_issue
    ):
        """Test restoring a soft-deleted incident."""
        # First, delete an incident
        issue = create_test_issue
        incident_to_modify = issue.reported_incidents_workflows[0]
        incident_id = incident_to_modify.id

        async with SessionManager.get_session() as db:
            # Use a direct query to get the object in the current session
            incident = await db.get(ReportedIncidentWorkflow, incident_id)
            incident.is_deleted = True
            incident.deleted_by = "another-user"
            await db.commit()

        # Now, restore it via the API
        restore_payload = {"is_deleted": False, "deleted_by": "restorer"}

        response = client.patch(
            f"/agents/{test_agent_id}/core/v1/incident/{incident_id}",
            json=restore_payload,
        )

        assert response.status_code == 200
        data = response.json()
        assert data["is_deleted"] is False
        assert data["deleted_by"] is None
        assert data["kind"] == "workflow"

        # Verify in DB
        async with SessionManager.get_session() as db:
            restored_incident = await db.get(ReportedIncidentWorkflow, incident_id)
            assert restored_incident.is_deleted is False
            assert restored_incident.deleted_by is None
