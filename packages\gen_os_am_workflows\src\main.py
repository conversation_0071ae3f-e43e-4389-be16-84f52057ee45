"""Main module for the Workflows service."""

import uvicorn

from gen_os_am_workflows.api import API
from gen_os_am_workflows.settings import Settings

settings = Settings.get_settings()
app = API().create_api()

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.API_LISTEN_HOST,
        port=settings.API_LISTEN_PORT,
        workers=settings.API_WORKERS,
        reload=True,
    )
