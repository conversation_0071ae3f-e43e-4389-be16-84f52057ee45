"""Add human_approval_required to step.

Revision ID: 3c39c8a5970c
Revises: eb90766062b6
Create Date: 2025-06-20 09:34:41.178225

"""

from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "3c39c8a5970c"
down_revision: str = "eb90766062b6"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Add human_approval_required to step."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("step", schema=None) as batch_op:
        batch_op.add_column(sa.Column("human_approval_required", sa.<PERSON>(), nullable=True))

    # ### end Alembic commands ###


def downgrade() -> None:
    """Remove human_approval_required from step."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("step", schema=None) as batch_op:
        batch_op.drop_column("human_approval_required")

    # ### end Alembic commands ###
