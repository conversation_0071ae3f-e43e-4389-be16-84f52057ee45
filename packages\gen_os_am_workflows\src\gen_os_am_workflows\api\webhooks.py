"""Workflow webhooks for interactions."""

import logging

import httpx
from pydantic import BaseModel

from gen_os_am_workflows.api.models.request import InteractionKind
from gen_os_am_workflows.database.models import Interaction

logger = logging.getLogger(__name__)


class WebhooksService:
    """Workflow webhooks for interactions."""

    webhook_suffix = "/agents/{agent_id}/core/v1/notifications/interaction"

    def __init__(self, notification_webhook_urls: list[str] | None = None):
        """Initialize the WorkflowWebhooks class.

        Args:
            notification_webhook_urls: The URLs to send the webhook to

        """
        self.notification_webhook_urls = notification_webhook_urls or []

    async def notify_on_interaction(self, interaction: Interaction, agent_id: str) -> None:
        """Notify the orchestrator that an interaction has been created/updated.

        Args:
            interaction: The interaction that was created/updated
            agent_id: The ID of the agent that the interaction belongs to

        """
        try:
            payload = InteractionNotificationRequest(
                execution_id=str(interaction.occurrence.step_run.workflow_execution_id),
                step_run_id=str(interaction.occurrence.step_run_id),
                occurrence_id=str(interaction.occurrence_id),
                interaction_id=str(interaction.id),
                interaction_kind=interaction.kind,
                interaction_data=InteractionData(
                    resolution=interaction.resolution,
                    edited_data=interaction.edited_data,
                    created_at=str(interaction.interacted_at),
                ),
            )

            logger.info(
                f"Notifying of interaction {interaction.id} "
                f"(kind: {interaction.kind}) for execution "
                f"{interaction.occurrence.step_run.workflow_execution_id}"
            )

            # Send webhook notifications
            for notification_webhook_url in self.notification_webhook_urls:
                async with httpx.AsyncClient() as client:
                    url = (
                        f"{notification_webhook_url}{self.webhook_suffix.format(agent_id=agent_id)}"
                    )
                    logger.info(f"Sending webhook to {url}")
                    response = await client.post(
                        url,
                        json=payload.model_dump(exclude_none=True),
                        headers={"Content-Type": "application/json"},
                    )

                if response.status_code >= 200 and response.status_code < 300:
                    logger.info(
                        f"Successfully notified of interaction {interaction.id} "
                        f"(HTTP {response.status_code})"
                    )
                else:
                    logger.warning(
                        f"Notification failed for interaction {interaction.id}: "
                        f"HTTP {response.status_code} - {response.text}"
                    )

        except TimeoutError:
            logger.warning(f"Notification timed out for interaction {interaction.id}")
        except Exception as e:
            logger.error(f"Failed to notify of interaction {interaction.id}: {e}")


class InteractionData(BaseModel):
    """Data model for interaction notifications from gen_os_am_workflows."""

    resolution: str | None = None
    edited_data: list[dict] = []
    created_at: str | None = None


class InteractionNotificationRequest(BaseModel):
    """Request model for interaction notifications from gen_os_am_workflows."""

    execution_id: str
    step_run_id: str
    occurrence_id: str
    interaction_id: str
    interaction_kind: InteractionKind
    interaction_data: InteractionData
