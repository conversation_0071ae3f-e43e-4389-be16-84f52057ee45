"""Tests for user operations functionality."""

import uuid

import pytest

from gen_os_am_knowledge.sdk.operations.user_ops import UserService

pytest_plugins = "pytest_asyncio"


@pytest.mark.asyncio
async def test_create_and_get_user():
    """Test creating, retrieving, updating, and deleting a user."""
    user_service = UserService()
    # Test variables
    user_name = "pytest_user"
    user_external_id = "12345"

    # Create a user
    created_user = await user_service.create_user(
        user_name=user_name, user_external_id=user_external_id
    )
    assert created_user is not None, "User creation failed"
    assert created_user.name == user_name, "User name mismatch after creation"
    assert (
        created_user.external_id == user_external_id
    ), "External ID mismatch after creation"

    retrieved_user = await user_service.get_user(user_id=created_user.id)
    assert retrieved_user is not None, "User retrieval failed"
    assert retrieved_user.name == user_name, "Retrieved user name mismatch"
    assert (
        retrieved_user.external_id == user_external_id
    ), "Retrieved external ID mismatch"

    retrieved_users = await user_service.search_users(
        user_name=user_name, user_external_id=user_external_id, limit=1, offset=0
    )

    assert (
        len(retrieved_users) == 1
    ), "User retrieval failed or returned unexpected results"
    assert retrieved_users == [created_user], "Retrieved users mismatch"

    # Update the user
    updated_user_name = "pytest_user_updated"
    updated_user_external_id = "123456"
    update_result = await user_service.update_user(
        user_id=created_user.id,
        user_name=updated_user_name,
        user_external_id=updated_user_external_id,
    )

    assert update_result.success
    assert update_result.updated_instance.name == updated_user_name
    assert update_result.updated_instance.external_id == updated_user_external_id
    assert update_result.error is None
    assert update_result.updated_values == {
        "name": (user_name, updated_user_name),
        "external_id": (user_external_id, updated_user_external_id),
    }

    deletion_result = await user_service.delete_user(user_id=created_user.id)

    assert deletion_result.success, "User deletion failed"
    assert (
        deletion_result.deleted_instance == update_result.updated_instance
    ), "User deletion failed"

    user_uuid = uuid.uuid4()
    deletion_result = await user_service.delete_user(user_id=user_uuid)

    assert not deletion_result.success, "User deletion succeeded unexpectedly"
    assert deletion_result.deleted_instance is None, "Deleted items not None"
    assert (
        deletion_result.error == f"User with id {user_uuid} not found."
    ), "Error message mismatch"
