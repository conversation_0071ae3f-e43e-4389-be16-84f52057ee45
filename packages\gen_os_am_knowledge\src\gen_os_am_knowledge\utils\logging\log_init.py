"""Logging initialization."""

import logging
import sys
from enum import Enum
from logging.handlers import RotatingFileHandler, TimedRotatingFileHandler

import ecs_logging
from opentelemetry.instrumentation.logging import LoggingInstrumentor


class LoggingFormat(str, Enum):
    """Logging format."""

    ECS = "ecs"
    OTEL = "otel"
    DEFAULT = "default"


class LoggingDestination(str, Enum):
    """Logging destination."""

    STDOUT = "stdout"
    STDERR = "stderr"
    FILE_ROTATING = "file_rotating"
    FILE_TIMED_ROTATING = "file_timed_rotating"


def init_logger(
    name: str,
    log_level: int = logging.INFO,
    log_format: LoggingFormat = LoggingFormat.DEFAULT,
    destination: LoggingDestination = LoggingDestination.STDOUT,
    file_path: str | None = None,
    backup_count: int = 100,
    max_bytes: int = 10000,
    when: str = "D",
    interval: int = 1,
    formatter: logging.Formatter | None = None,
    handlers: list[logging.Handler] | None = None,
) -> logging.Logger:
    """Init a logger with the specified name, log level, log format, and destination.

    Args:
        name (str): The name of the logger.
        log_level (int): The log level of the logger.
        log_format (LoggingFormat): The log format of the logger.
            Options are 'ecs', 'otel', and 'default'.
        destination (LoggingDestination): The destination of the logger.
            Options are 'stdout', 'stderr', 'file_rotating', and 'file_timed_rotating'.
        file_path (str): The file path for the log file.
            Required if destination is 'file_rotating' or 'file_timed_rotating'.
        backup_count (int): The number of backup log files to keep. Default is 100.
            Only for 'file_rotating' and 'file_timed_rotating'.
        max_bytes (int): The maximum size of the log file in bytes.
            Default is 10,000,000.
            Only for 'file_rotating'.
        when (str): The interval to rotate the log file. Default is 'D' (daily).
            Only for 'file_timed_rotating'.
        interval (int): The interval to rotate the log file. Default is 1.
            Only for 'file_timed_rotating'.
        formatter (logging.Formatter): The custom formatter to use
            for the logger if not using 'ecs' or 'otel' log format.
        handlers (list[logging.Handler]): The list of custom handlers to
            add to the logger if not using 'stdout', 'stderr', 'file_rotating',
            or 'file_timed_rotating' or need multiple handlers.

    Returns:
        logging.Logger: The logger with the specified configuration

    """
    if formatter is None:
        if log_format == LoggingFormat.ECS:
            formatter = ecs_logging.StdlibFormatter()
        elif log_format == LoggingFormat.OTEL:
            LoggingInstrumentor().instrument(set_logging_format=True)
            logger = logging.getLogger()
            formatter = logger.handlers[0].formatter
            logger.handlers = []
        else:
            formatter = logging.Formatter(
                "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            )

    if destination == LoggingDestination.STDOUT:
        handler = logging.StreamHandler(sys.stdout)
    elif destination == LoggingDestination.STDERR:
        handler = logging.StreamHandler(sys.stderr)
    elif destination == LoggingDestination.FILE_ROTATING:
        if not file_path:
            raise ValueError("File path must be provided for file logging.")
        handler = RotatingFileHandler(
            file_path, maxBytes=max_bytes, backupCount=backup_count
        )
    elif destination == LoggingDestination.FILE_TIMED_ROTATING:
        if not file_path:
            raise ValueError("File path must be provided for file logging.")
        handler = TimedRotatingFileHandler(
            file_path, when=when, interval=interval, backupCount=backup_count
        )

    logger = logging.getLogger(name)
    logger.setLevel(log_level)

    if handlers is not None:
        for h in handlers:
            if formatter is not None:
                h.setFormatter(formatter)
            logger.addHandler(h)
    else:
        if formatter is not None:
            handler.setFormatter(formatter)
        logger.addHandler(handler)

    return logger
