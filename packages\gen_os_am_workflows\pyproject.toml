[project]
name = "gen-os-am-workflows"
version = "0.1.0"
description = "Gen-OS Agent Manager Workflows Backend"
authors = [
    {name = "Nuno Cravino", email = "<EMAIL>"},
    {name = "DareData", email = "<EMAIL>"},
]

requires-python = ">=3.12"
readme = "README.md"
license = {text = "Proprietary"}

dependencies=[
    "pydantic>=2.10.5",
    "sqlalchemy>=2.0.38",
    "fastapi>=0.115.8",
    "pydantic-settings>=2.5.2",
    "aiosqlite>=0.21.0",
    "uvicorn>=0.30.0",
    "psycopg>=3.2.3",
    "python-dotenv==1.0.1",
    "alembic>=1.15.2",
    "dependency-injector>=4.46.0",
    "PyYAML>=6.0.2",
    "python-multipart>=0.0.20",
    "httpx>=0.28.1",
]

[dependency-groups]
# dev dependencies that won't appear in the package distribution metadata
test = [
    "pytest>=8.3.4",
    "pytest-asyncio>=0.25.3",
    "pytest-cov>=6.1.0",
]
docs=["mkdocs", "mkdocstrings", "mkdocstrings-python"]
dev=[
    "ruff>=0.8.0,<0.9.0",
    "pyright>=1.1.390,<1.4.0",
    "deptry>=0.23.0,<0.24.0",
    "ipykernel>=6.29.5",
    "pre-commit<=4.0.0,>=3.8.0",
]

[tool.pdm.dev-dependencies]
gen-os = [
    "-e file:///${PROJECT_ROOT}/../gen_os_sdk_emulator#egg=gen-os-sdk-emulator"
]

[build-system]
requires = ["pdm-backend"]
build-backend = "pdm.backend"

[tool.pdm.build]
includes = ["src", "scripts"]
package-dir = "src"

[tool.pdm]
distribution = true

[tool.ruff]
line-length=100
target-version="py312"

[tool.ruff.lint]
select=[
    "I",   # isort
    "A",   # flake8-builtins
    "N",   # pep8-naming
    "D",   # pydocstyle
    "E",   # pycodestyle (Error)
    "YTT", # flake8-2020
    "B",   # flake8-bugbear
    "T10", # flake8-debugger
    "T20", # flake8-print
    "C4",  # flake8-comprehensions
]

ignore=[
    "D203", #0ne-blank-line-before-class
    "D213", #multi-line-summary-second-line
    "D104", #package docstrings
]

[tool.ruff.lint.flake8-bugbear]
extend-immutable-calls = ["fastapi.Depends", "fastapi.params.Depends", "fastapi.Query", "fastapi.params.Query"]

[tool.pytest.ini_options]
asyncio_default_fixture_loop_scope = "session"

[tool.deptry.per_rule_ignores]
DEP002 = ["aiosqlite", "psycopg"]

[tool.pdm.scripts]
# bash -c so we don't affect the running environment
## you can also specify without it
# check dependencies, types and format source and tests
## needs dev dependency group
checkfix = "bash -c 'deptry ./src && ruff format ./ && ruff check ./src --fix && pyright --level error --threads 2'"
check = "bash -c 'deptry ./src && ruff check ./src && pyright --level error --threads 2'"

# needs doc depdency group
builddocs = "bash -c 'cd docs && mkdocs build'"

servedocs = "bash -c 'cd docs && mkdocs serve'"

precommit = "sh -c 'pre-commit run --show-diff-on-failure --color=always --files $(git ls-files)'"

# start a dev database
start-database="sh -c 'pwd && set -o allexport && . ./.env && set +o allexport && docker compose -f src/gen_os_am_workflows/database/local.yml up -d'"
stop-database="sh -c 'docker compose -f src/gen_os_am_workflows/database/local.yml down'"

# run/generate the alembic migrations
alembic-generate = "bash -c 'cd src && alembic revision --autogenerate'"
alembic-migrate = "bash -c 'cd src && alembic upgrade head'"

# populate database with mock data
populate-with-mock = {composite = ["source-env", "python -m scripts.populate_db_with_mock_data"]}

# run the api
api="sh -c 'cd src/gen_os_am_workflows && uvicorn main:app --host 0.0.0.0 --port 8000 --reload'"

# Environment setup scripts
source-env = "python scripts/update_env.py local"
docker-env = "python scripts/update_env.py docker"

# Run in local environment
cm-source = {composite = ["source-env", "start-database", "alembic-migrate", "api"]}

# Run in docker environment
cm-docker = {composite = ["docker-env", "docker compose up --build"]}

# run the tests
tests = "pytest --cov --cov-branch"

# TODO: We probably can remove the building of the wheels

# build the gen-os-sdk-emulator
build-gen-os-sdk-emulator = "sh -c 'pdm build -p ../gen_os_sdk_emulator && cp -r ../gen_os_sdk_emulator/dist/* shared/lib/'"
build-deps = {composite = ["build-gen-os-sdk-emulator"]}
