# Use specific version tag
FROM python:3.12-slim-bookworm

# Set Python environment variables
    # PYTHONDONTWRITEBYTECODE=1 - Prevents Python from writing .pyc files to disk, reducing container size
    # PYTHONUNBUFFERED=1 - Ensures Python output is sent straight to terminal without buffering for real-time logs
    # PDM_USE_VENV=false - Tells <PERSON>M not to create a virtual environment since we're already in a container
    # PYTHONPATH=/app/src - Adds /app/src directory to the Python module search path

ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV PDM_USE_VENV=false
ENV PYTHONPATH=/app/src

# Create a non-root user and group
RUN groupadd -r app && useradd -r -g app app

# Set working directory
WORKDIR /app

# Install only necessary system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    ca-certificates \
    libpq-dev \
    && pip install --no-cache-dir -U pdm \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Copy project-specific files and folders
COPY --chown=app:app ./api /app/api
COPY --chown=app:app ./shared /app/shared

# Change to api directory and install dependencies with PDM
WORKDIR /app/api
# Copy .env.example to .env if it exists
RUN if [ -f .env.example ]; then cp .env.example .env; fi

RUN pdm install --prod --frozen-lockfile --no-editable

# Switch back to main app directory and non-root user
USER app

# Set container entrypoint
CMD ["pdm", "run", "service"]
