"""Tests for folder contents functionality."""

import os

import pytest
import pytest_asyncio

from gen_os_am_knowledge.sdk.filesystem_connectors.local_fs_connector import (
    LocalFSConnector,
)
from gen_os_am_knowledge.sdk.models.common import _DEFAULT_UUID, DEFAULT_USER
from gen_os_am_knowledge.sdk.operations.document_ops import DocumentService
from gen_os_am_knowledge.sdk.operations.folder_ops import FolderService

pytest_plugins = "pytest_asyncio"


@pytest.fixture(scope="module")
def sample_file():
    """Create sample test files."""
    with open("test.txt", "w") as f:
        f.write("test")
    with open("test2.txt", "w") as f:
        f.write("test2")
    with open("test3.txt", "w") as f:
        f.write("test3")
    with open("test4.txt", "w") as f:
        f.write("test4")
    yield
    os.remove("test.txt")
    os.remove("test2.txt")
    os.remove("test3.txt")
    os.remove("test4.txt")


@pytest.fixture(scope="module")
def create_root_folder():
    """Create root folder for testing."""
    storage_path = "path-to-local-storage"
    root_folder = "root-folder"
    os.makedirs(f"{storage_path}/{root_folder}", exist_ok=True)
    yield root_folder
    # shutil.rmtree(storage_path)


@pytest.fixture(scope="module")
def local_fs_connector(create_root_folder):
    """Create local filesystem connector."""
    yield LocalFSConnector(root_folder=create_root_folder)


@pytest_asyncio.fixture(scope="module")
async def setup_storage(local_fs_connector, sample_file):
    """Set up storage with test folders and documents."""
    folder_service = FolderService(local_fs_connector)
    document_service = DocumentService(local_fs_connector)
    folder1 = await folder_service.create_folder(
        "folder1", user_id=DEFAULT_USER.id, parent_id=_DEFAULT_UUID
    )
    folder2 = await folder_service.create_folder(
        "folder2", parent_id=folder1.id, user_id=DEFAULT_USER.id
    )
    # deleted folder
    folder3 = await folder_service.create_folder(
        "folder3", parent_id=folder1.id, user_id=DEFAULT_USER.id
    )
    await folder_service.delete_folder(folder3.id, user_id=DEFAULT_USER.id)
    # published doc
    with open("test.txt", "rb") as f:
        doc1 = await document_service.create_document(
            file=f, user_id=DEFAULT_USER.id, folder_id=folder1.id
        )
    # not published doc
    with open("test2.txt", "rb") as f:
        doc2 = await document_service.create_document(
            file=f, user_id=DEFAULT_USER.id, folder_id=folder1.id, draft=True
        )
    # not published deleted
    with open("test3.txt", "rb") as f:
        doc3 = await document_service.create_document(
            file=f, user_id=DEFAULT_USER.id, folder_id=folder1.id, draft=True
        )
    await document_service.delete_document(doc3.id, DEFAULT_USER.id, draft=True)
    # published deleted
    with open("test4.txt", "rb") as f:
        doc4 = await document_service.create_document(
            file=f, user_id=DEFAULT_USER.id, folder_id=folder1.id
        )
    await document_service.delete_document(doc4.id, DEFAULT_USER.id)

    yield folder1, folder2, doc1, doc2
    await document_service.delete_document(doc1.id, DEFAULT_USER.id)
    await document_service.delete_document(doc2.id, DEFAULT_USER.id)
    await document_service.cleanup_deleted_documents()
    await folder_service.delete_folder(folder2.id, user_id=DEFAULT_USER.id)
    await folder_service.delete_folder(folder1.id, user_id=DEFAULT_USER.id)
    await folder_service.cleanup_deleted_folders()


@pytest.mark.asyncio
async def test_get_folder_contents(setup_storage, local_fs_connector):
    """Test getting folder contents with various options."""
    folder1, folder2, doc1, doc2 = setup_storage
    folder_service = FolderService(local_fs_connector)
    fold = await folder_service.get_folder(folder1.id, include_dependencies=True)
    assert fold.id == folder1.id
    assert fold.name == folder1.name
    assert fold.parent_id == folder1.parent_id
    assert len(fold.documents) == 3
    assert len(fold.children) == 1
    assert doc1.id in [doc.id for doc in fold.documents]
    assert doc2.id in [doc.id for doc in fold.documents]
    assert fold.children[0] == folder2
    assert fold.ingestion is None

    fold = await folder_service.get_folder(
        folder1.id, include_dependencies=True, include_deleted_folders=True
    )
    assert len(fold.documents) == 3
    assert len(fold.children) == 2

    fold = await folder_service.get_folder(
        folder1.id, include_dependencies=True, include_published_and_deleted=True
    )
    assert len(fold.documents) == 4
    assert len(fold.children) == 1


@pytest.mark.asyncio
async def test_get_recursive_folder_tree(setup_storage, local_fs_connector):
    """Test getting recursive folder tree."""
    folder_service = FolderService(local_fs_connector)
    # Get the folder tree
    folder_tree = await folder_service.recursive_folder_tree(_DEFAULT_UUID)

    assert folder_tree is not None
    assert len(folder_tree.documents_ids) == 3
    assert len(folder_tree.folder_ids) == 3

    folder_tree = await folder_service.recursive_folder_tree(
        folder_id=_DEFAULT_UUID, include_deleted_folders=True
    )
    assert folder_tree is not None
    assert len(folder_tree.documents_ids) == 3
    assert len(folder_tree.folder_ids) == 4

    folder_tree = await folder_service.recursive_folder_tree(
        folder_id=_DEFAULT_UUID, include_published_and_deleted=True
    )
    assert folder_tree is not None
    assert len(folder_tree.documents_ids) == 4
    assert len(folder_tree.folder_ids) == 3
