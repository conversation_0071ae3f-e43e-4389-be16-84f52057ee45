"""Module containing utility classes and constants for database operations."""

from typing import Any

from pydantic.dataclasses import dataclass
from sqlmodel import SQLModel


class NOT_PROVIDED:  # noqa: N801
    """Sentinel class used to distinguish between None and not provided values."""

    def __repr__(self) -> str:
        """Return string representation of NOT_PROVIDED."""
        return "NOT_PROVIDED"

    def __str__(self) -> str:
        """Return string representation of NOT_PROVIDED."""
        return "NOT_PROVIDED"

    def __eq__(self, other: Any) -> bool:
        """Check equality with another object."""
        if isinstance(other, NOT_PROVIDED):
            return True
        return False

    # Make it JSON serializable for Pydantic
    def __json__(self) -> str:
        """Return JSON representation of NOT_PROVIDED."""
        return "NOT_PROVIDED"


@dataclass
class DeletionResult:
    """Result of a deletion operation.

    Attributes:
        success: Whether the deletion was successful.
        deleted_instance: The deleted instance if successful, None otherwise.

    """

    success: bool
    deleted_instance: SQLModel | None = None
    error: str | None = None


@dataclass
class UpdateResult:
    """Result of an update operation.

    Attributes:
        success: Whether the update was successful.
        updated_instance: The updated instance if successful, None otherwise.

    """

    success: bool
    updated_instance: SQLModel | None = None
    updated_values: dict[str, tuple[Any, Any]] | None = (
        None  # dict of type: {field : {old value : new value},...}
    )
    error: str | None = None
