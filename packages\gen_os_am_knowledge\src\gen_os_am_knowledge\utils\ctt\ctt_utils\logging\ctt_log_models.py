from datetime import datetime, timezone
from typing import Annotated, Literal

from pydantic import (
    AnyUrl,
    BaseModel,
    Field,
    IPvAnyAddress,
    computed_field,
    field_serializer,
    field_validator,
)


class EventModel(BaseModel):
    kind: Literal[
        "alert",
        "assert",
        "enrichment",
        "event",
        "metric",
        "state",
        "pipeline_error",
        "signal",
    ] = "event"
    category: list[
        Literal[
            "api",
            "authentication",
            "configuration",
            "database",
            "driver",
            "email",
            "file",
            "host",
            "iam",
            "intrusion_detection",
            "library",
            "malware",
            "network",
            "package",
            "process",
            "registry",
            "session",
            "threat",
            "vulnerability",
            "web",
        ]
    ]
    type: list[
        Literal[
            "access",
            "admin",
            "allowed",
            "change",
            "connection",
            "creation",
            "deletion",
            "denied",
            "end",
            "error",
            "group",
            "indicator",
            "info",
            "installation",
            "protocol",
            "start",
            "user",
        ]
    ]
    outcome: Literal["failure", "success", "unknown"] = "success"
    start: datetime
    end: datetime

    @field_serializer("start", "end")
    def serialize_timestamp(self, value: datetime) -> str:
        return value.astimezone(timezone.utc).isoformat().replace("+00:00", "Z")

    @computed_field
    @property
    def duration(self) -> int:
        return int((self.end - self.start).total_seconds() * 1000)

    # provider: str
    # module: str


class HTTPRequest(BaseModel):
    method: Literal["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS", "HEAD"]
    bytes: int | None = None
    mime_type: str | None = None
    body_content: str | None = None  # Only include on DEBUG/ERROR logs


class HTTPResponse(BaseModel):
    status_code: int
    bytes: int | None = None
    mime_type: str | None = None
    body_content: str | None = None  # Only include on DEBUG/ERROR logs

    @field_validator("status_code")
    def check_status_code(cls, value: int) -> int:
        if not (100 <= value <= 599):
            raise ValueError("status_code must be between 100 and 599")
        return value


class HTTPLog(BaseModel):
    version: str = "1.1"  # Default HTTP version
    request: HTTPRequest
    response: HTTPResponse


class Labels(BaseModel):
    environment: Literal["DEV", "TST", "UAT", "PRD", "LOCAL", "QUA"]


class ErrorLog(BaseModel):
    """
    Error log model for CTT logging

    Fields below need to be filled if you log error outside excpetion block or force exc_info=False:
    - message
    - stack_trace
    - type
    """

    code: int  # codes from ctt, dont know yet
    message: str | None = None
    stack_trace: str | None = None
    type: str | None = None


class UserLog(BaseModel):
    id: str
    name: str


class ClientLog(BaseModel):
    address: str
    ip: IPvAnyAddress | None = None
    user: UserLog | None = None


class AgentLog(BaseModel):
    id: str
    name: str
    type: str
    version: str


class URLLog(BaseModel):
    original: AnyUrl
    full: AnyUrl
    domain: str
    scheme: Literal["http", "https", "ftp"]
    port: Annotated[int, Field(ge=1, le=65535)]
    path: str
    query: str


class DestinationOrSourceLog(BaseModel):
    address: str
    ip: IPvAnyAddress | None = None
    domain: str | None = None
    user: UserLog | None = None


class UserAgentLog(BaseModel):
    name: str
    original: str
    version: str
    device_name: str

    @computed_field
    @property
    def device(self) -> dict:
        return {"name": self.device_name}

    def model_dump(self, **kwargs):
        kwargs.setdefault("exclude", {"device_name"})
        return super().model_dump(**kwargs)


class BaseLog(BaseModel):
    environment: Literal["DEV", "TST", "UAT", "PRD", "LOCAL", "QUA"]
    event: EventModel
    http: HTTPLog | None = None
    error: ErrorLog | None = None
    client: ClientLog | None = None
    agent: AgentLog | None = None
    source: DestinationOrSourceLog | None = None
    destination: DestinationOrSourceLog | None = None
    user_agent: UserAgentLog | None = None
    url: URLLog | None = None

    @computed_field
    @property
    def labels(self) -> dict:
        return {"environment": self.environment}

    def model_dump(self, **kwargs):
        kwargs.setdefault("exclude", {"environment"})
        return super().model_dump(**kwargs)
