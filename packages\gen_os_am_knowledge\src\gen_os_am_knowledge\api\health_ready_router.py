"""Module for the live ready router."""

from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse
from sqlmodel import select

from gen_os_am_knowledge.config.database import get_async_session
from gen_os_am_knowledge.sdk.filesystem_connectors.base import BaseFileSystemConnector


class HealthAPI:
    """Health API class to handle health related operations.

    Args:
        storage_connector (BaseFileSystemConnector): The storage connector to use.

    """

    def __init__(self, storage_connector: BaseFileSystemConnector):
        """Initialize the HealthAPI."""
        self.storage_connector = storage_connector

    def get_router(self) -> APIRouter:
        """Return the FastAPI router for the health API.

        Returns:
            APIRouter: The FastAPI router for the health API

        """
        health_router = APIRouter()

        @health_router.get("/healthz")
        async def healthz():
            """Return the healthz status."""
            return JSONResponse(content={"status": "ok"}, status_code=200)

        @health_router.get("/readyz")
        async def readyz():
            """Return the readyz status."""
            try:
                db_status = await self._db_check()
                storage_status = self._storage_check()
                if db_status and storage_status:
                    return JSONResponse(
                        content={
                            "status": "ok",
                            "components": {"database": "ok", "storage": "ok"},
                        },
                        status_code=200,
                    )
                else:
                    return JSONResponse(
                        content={
                            "status": "not ready",
                            "components": {
                                "database": "ok" if db_status else "not ready",
                                "storage": "ok" if storage_status else "not ready",
                            },
                        },
                        status_code=503,
                    )
            except Exception as error:
                raise HTTPException(status_code=503, detail=str(error)) from error

        return health_router

    async def _db_check(self) -> bool:
        try:
            async for session in get_async_session():
                result = await session.execute(select(1))
                return result.first() is not None
        except Exception:
            return False

    def _storage_check(self) -> bool:
        try:
            return self.storage_connector.ls("") is not None
        except Exception:
            return False
