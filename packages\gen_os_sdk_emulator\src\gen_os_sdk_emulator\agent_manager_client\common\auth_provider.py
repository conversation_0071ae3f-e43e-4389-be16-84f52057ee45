"""Common authentication provider for the agent manager client."""

from abc import ABC, abstractmethod


class AuthProvider(ABC):
    """Abstract base class for authentication providers."""

    @abstractmethod
    async def get_auth_token(self) -> str:
        """Get an authentication token."""
        pass

    @property
    @abstractmethod
    def requires_auth(self) -> bool:
        """Whether this provider requires authentication."""
        pass


class LocalAuthProvider(AuthProvider):
    """Local development authentication provider that doesn't require authentication."""

    async def get_auth_token(self) -> str:
        """No token needed for local development."""
        return ""

    @property
    def requires_auth(self) -> bool:
        """Local development doesn't require authentication."""
        return False


class GoogleAuthProvider(AuthProvider):
    """Google Cloud authentication provider."""

    def __init__(self, audience: str):
        """Initialize the Google Cloud authentication provider.

        Args:
            audience: The audience for the authentication token

        """
        self._audience = audience

    async def get_auth_token(self) -> str:
        """Get a Google Cloud authentication token for the given audience."""
        import google.auth.transport.requests as google_requests
        import google.oauth2.id_token as google_token

        auth_req = google_requests.Request()
        return google_token.fetch_id_token(auth_req, self._audience)

    @property
    def requires_auth(self) -> bool:
        """Google Cloud requires authentication."""
        return True
