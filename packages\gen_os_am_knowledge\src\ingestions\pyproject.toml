[build-system]
requires = ["pdm-backend"]
build-backend = "pdm.backend"

[project]
name = "ctt_am_ingestions"
version = "0.1.11"
description = "CTT Agent Manager Ingestions"
authors = [
    {name = "DareData, S.A.", email = "<EMAIL>"},
]

requires-python = ">=3.12"
readme = "README.md"
license = {text = "MIT"}

dependencies = [
    "pip",
    "openpyxl==3.1.5",
    "pandas==2.2.3",
    "aiohttp==3.11.11",
    "requests==2.32.3",
    "bs4==0.0.2",
    "aioresponses>=0.7.7",
    "pydantic>=2.10.5",
]

# this defines a local folder as a pipy source
# the filepath is temporary until we have a central repo
# other repos should have a similar one
# [[tool.pdm.source]]
# name = "private"
# url = "../shared/lib/"
# type = "find_links"

[tool.pdm]
distribution = true

[dependency-groups]
dev = [
    "black[jupyter]==24.8.0",
    "pre-commit<=4.0.0,>=3.8.0",
    "isort<6.0.0,>=5.13.2",
    "mypy<=1.14,>1.10",
    "pandas-stubs>=2.2.3.241126",
    "types-beautifulsoup4>=4.12.0.20241020"
]
test = [
    "pytest<9,>=8.3.3",
    "pytest-cov<7.0.0,>=6.0.0",
    "pytest-asyncio>=0.25.2",
]

[tool.pdm.scripts]
# website ingestion
website-ingestion = "sh -c 'cd src/website_ingestion && python main.py'"

# run with pdm run precommit
precommit = "sh -c 'pre-commit run --show-diff-on-failure --color=always --files $(git ls-files)'"

# run tests
tests = "sh -c 'pytest --cov --cov-branch && coverage report --omit=\"tests/*\"'"

# clean
clean = "../../scripts/clean.sh"
