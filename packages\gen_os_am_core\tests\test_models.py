"""Tests for the database models."""

import uuid
from datetime import datetime, timezone

import pytest
from sqlalchemy import select
from sqlalchemy.exc import IntegrityError, ProgrammingError

from gen_os_am_core.database.models.agent_configuration import AgentConfiguration
from gen_os_am_core.database.models.base import Base
from gen_os_am_core.database.session import SessionManager


class TestAgentConfigurationModel:
    """Test cases for the AgentConfiguration model."""

    @pytest.fixture
    def sample_config_data(self):
        """Sample configuration data for testing."""
        return {
            "id": uuid.uuid4(),
            "agent_id": "test-agent-123",
            "name": "test_parameter",
            "description": "A test configuration parameter",
            "value": "test_value",
        }

    @pytest.mark.asyncio
    async def test_create_agent_configuration(self, sample_config_data):
        """Test creating a new agent configuration."""
        async with SessionManager.get_session() as db:
            config = AgentConfiguration(
                id=str(sample_config_data["id"]),
                agent_id=sample_config_data["agent_id"],
                name=sample_config_data["name"],
                description=sample_config_data["description"],
                value=sample_config_data["value"],
            )

            db.add(config)
            await db.commit()
            await db.refresh(config)

            assert str(config.id) == str(sample_config_data["id"])
            assert config.agent_id == sample_config_data["agent_id"]
            assert config.name == sample_config_data["name"]
            assert config.description == sample_config_data["description"]
            assert config.value == sample_config_data["value"]
            assert config.created_at is not None
            assert config.updated_at is not None
            assert isinstance(config.created_at, datetime)
            assert isinstance(config.updated_at, datetime)
            # IDs are stored as string representations of UUIDs in the database
            assert isinstance(config.id, str)
            # Ensure the stored string is a valid UUID format
            uuid.UUID(config.id)

    @pytest.mark.asyncio
    async def test_agent_configuration_repr(self, sample_config_data):
        """Test the string representation of AgentConfiguration."""
        config = AgentConfiguration(
            id=str(sample_config_data["id"]),
            agent_id=sample_config_data["agent_id"],
            name=sample_config_data["name"],
            description=sample_config_data["description"],
            value=sample_config_data["value"],
        )

        repr_str = repr(config)
        assert str(sample_config_data["id"]) in repr_str
        assert sample_config_data["agent_id"] in repr_str
        assert sample_config_data["name"] in repr_str
        assert "AgentConfiguration" in repr_str

    @pytest.mark.asyncio
    async def test_agent_configuration_with_null_description(self, sample_config_data):
        """Test creating configuration with null description."""
        async with SessionManager.get_session() as db:
            config = AgentConfiguration(
                id=str(sample_config_data["id"]),
                agent_id=sample_config_data["agent_id"],
                name=sample_config_data["name"],
                description=None,  # Test null description
                value=sample_config_data["value"],
            )

            db.add(config)
            await db.commit()
            await db.refresh(config)

            assert config.description is None
            assert str(config.id) == str(sample_config_data["id"])

    @pytest.mark.asyncio
    async def test_agent_configuration_update(self, sample_config_data):
        """Test updating an agent configuration."""
        async with SessionManager.get_session() as db:
            # Create initial configuration
            config = AgentConfiguration(
                id=str(sample_config_data["id"]),
                agent_id=sample_config_data["agent_id"],
                name=sample_config_data["name"],
                description=sample_config_data["description"],
                value=sample_config_data["value"],
            )

            db.add(config)
            await db.commit()
            await db.refresh(config)

            original_updated_at = config.updated_at

            # Update the configuration
            config.value = "updated_value"
            config.description = "Updated description"

            await db.commit()
            await db.refresh(config)

            assert config.value == "updated_value"
            assert config.description == "Updated description"
            # Note: updated_at should be automatically updated,
            # but this depends on database setup

    @pytest.mark.asyncio
    async def test_agent_configuration_unique_constraint(self, sample_config_data):
        """Test that agent_id + name combination must be unique."""
        async with SessionManager.get_session() as db:
            # Create first configuration
            config1 = AgentConfiguration(
                id=str(sample_config_data["id"]),
                agent_id=sample_config_data["agent_id"],
                name=sample_config_data["name"],
                description=sample_config_data["description"],
                value=sample_config_data["value"],
            )

            db.add(config1)
            await db.commit()

            # Try to create second configuration with same agent_id and name
            config2 = AgentConfiguration(
                id=str(uuid.uuid4()),  # Different ID
                agent_id=sample_config_data["agent_id"],  # Same agent_id
                name=sample_config_data["name"],  # Same name
                description="Different description",
                value="Different value",
            )

            db.add(config2)

            # This should raise an IntegrityError due to unique constraint
            # SQLite sometimes handles this differently, so we catch both scenarios
            try:
                await db.commit()
                # If commit succeeds, check that only one configuration exists
                # (SQLite might have silently ignored the duplicate)
                stmt = select(AgentConfiguration).where(
                    AgentConfiguration.agent_id == sample_config_data["agent_id"],
                    AgentConfiguration.name == sample_config_data["name"],
                )
                result = await db.execute(stmt)
                configs = result.scalars().all()
                assert len(configs) == 1, "Unique constraint should prevent duplicates"
            except IntegrityError:
                # This is the expected behavior for most databases
                pass

    @pytest.mark.asyncio
    async def test_agent_configuration_different_agents_same_name(
        self, sample_config_data
    ):
        """Test that different agents can have configurations with the same name."""
        async with SessionManager.get_session() as db:
            # Create configuration for first agent
            config1 = AgentConfiguration(
                id=str(sample_config_data["id"]),
                agent_id="agent-1",
                name=sample_config_data["name"],
                description=sample_config_data["description"],
                value=sample_config_data["value"],
            )

            # Create configuration for second agent with same name
            config2 = AgentConfiguration(
                id=str(uuid.uuid4()),
                agent_id="agent-2",
                name=sample_config_data["name"],  # Same name, different agent
                description="Different description",
                value="Different value",
            )

            db.add_all([config1, config2])
            await db.commit()

            # Both should be created successfully
            await db.refresh(config1)
            await db.refresh(config2)

            assert config1.agent_id == "agent-1"
            assert config2.agent_id == "agent-2"
            assert config1.name == config2.name

    @pytest.mark.asyncio
    async def test_query_configurations_by_agent(self, sample_config_data):
        """Test querying configurations by agent ID."""
        async with SessionManager.get_session() as db:
            agent1_id = "agent-1"
            agent2_id = "agent-2"

            # Create configurations for both agents
            config1 = AgentConfiguration(
                id=str(uuid.uuid4()),
                agent_id=agent1_id,
                name="param1",
                description="Parameter 1",
                value="value1",
            )

            config2 = AgentConfiguration(
                id=str(uuid.uuid4()),
                agent_id=agent1_id,
                name="param2",
                description="Parameter 2",
                value="value2",
            )

            config3 = AgentConfiguration(
                id=str(uuid.uuid4()),
                agent_id=agent2_id,
                name="param1",
                description="Parameter 1 for agent 2",
                value="value1_agent2",
            )

            db.add_all([config1, config2, config3])
            await db.commit()

            # Query configurations for agent 1
            stmt = select(AgentConfiguration).where(
                AgentConfiguration.agent_id == agent1_id
            )
            result = await db.execute(stmt)
            agent1_configs = result.scalars().all()

            assert len(agent1_configs) == 2
            assert all(config.agent_id == agent1_id for config in agent1_configs)

            # Query configurations for agent 2
            stmt = select(AgentConfiguration).where(
                AgentConfiguration.agent_id == agent2_id
            )
            result = await db.execute(stmt)
            agent2_configs = result.scalars().all()

            assert len(agent2_configs) == 1
            assert agent2_configs[0].agent_id == agent2_id

    @pytest.mark.asyncio
    async def test_delete_agent_configuration(self, sample_config_data):
        """Test deleting an agent configuration."""
        async with SessionManager.get_session() as db:
            # Create configuration
            config = AgentConfiguration(
                id=str(sample_config_data["id"]),
                agent_id=sample_config_data["agent_id"],
                name=sample_config_data["name"],
                description=sample_config_data["description"],
                value=sample_config_data["value"],
            )

            db.add(config)
            await db.commit()

            # Verify it was created
            stmt = select(AgentConfiguration).where(
                AgentConfiguration.id == str(sample_config_data["id"])
            )
            result = await db.execute(stmt)
            found_config = result.scalar_one_or_none()
            assert found_config is not None

            # Delete the configuration
            await db.delete(config)
            await db.commit()

            # Verify it was deleted
            result = await db.execute(stmt)
            found_config = result.scalar_one_or_none()
            assert found_config is None

    @pytest.mark.asyncio
    async def test_agent_configuration_required_fields(self):
        """Test that required fields are enforced."""
        async with SessionManager.get_session() as db:
            # Test missing required fields - this should be caught by
            # SQLAlchemy/Pydantic
            # ProgrammingError may be raised by the DB when required fields are missing
            with pytest.raises(
                (TypeError, ValueError, IntegrityError, ProgrammingError)
            ):
                config = AgentConfiguration(
                    # Missing required fields like id, agent_id, name, value
                    description="Test description"
                )
                db.add(config)
                await db.commit()

    def test_base_model_inheritance(self):
        """Test that AgentConfiguration inherits from Base correctly."""
        assert issubclass(AgentConfiguration, Base)
        assert hasattr(AgentConfiguration, "__tablename__")
        assert AgentConfiguration.__tablename__ == "agent_configuration"

    def test_model_table_structure(self):
        """Test that the model has the expected table structure."""
        # Check that required columns exist
        table = AgentConfiguration.__table__
        column_names = [col.name for col in table.columns]

        required_columns = [
            "id",
            "agent_id",
            "name",
            "description",
            "value",
            "created_at",
            "updated_at",
        ]
        for col in required_columns:
            assert col in column_names

        # Check indexes - model defines composite index on (agent_id, name)
        index_names = {idx.name for idx in table.indexes}
        assert "ix_agent_configuration_agent_id_name" in index_names
