FROM python:3.12.9-slim-bookworm

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV PDM_USE_VENV=true
ENV PYTHONOPTIMIZE=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    libpq-dev \
    postgresql-client-15\
    && rm -rf /var/lib/apt/lists/*

RUN groupadd -r app && useradd -m -r -g app app

WORKDIR /home/<USER>

RUN pip install --no-cache-dir pdm

# Copy only the files needed
COPY --chown=app:app packages packages

USER app

WORKDIR /home/<USER>/packages/gen_os_am_core
RUN pdm install -G:all

WORKDIR /home/<USER>

# Copy and make entrypoint script executable
COPY entrypoint.sh .

# Expose the port the app runs on
EXPOSE 8080

# Use the entrypoint script
ENTRYPOINT ["./entrypoint.sh"]
