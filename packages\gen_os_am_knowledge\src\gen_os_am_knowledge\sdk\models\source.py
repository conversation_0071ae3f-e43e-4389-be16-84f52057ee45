"""Module with source data model."""

import uuid
from typing import Any

from sqlmodel import JSON, Column, Field, Relationship, SQLModel


class Source(SQLModel, table=True):
    """The Source Table.

    Holds information about the sources that can be assigned to documents.

    Args:
        id (uuid.UUID): The Internal Database Id
        name (str): The Source Name.
        type (str): The Source Type as freeform string for now.
        meta (dict[str, Any] | None): The Json Metadata For This Source.
            Anything specific to the source that needs to be stored.
        ingestions (list[Ingestion]):
            The list of Ingestions that use this Source.
            A 1 Source to N Ingestion relationship, meaning a join to another
            table on access. Use this for access.

    """

    __tablename__ = "source"  # type: ignore
    __table_args__ = {"extend_existing": True}

    id: uuid.UUID = Field(
        default_factory=uuid.uuid4,
        primary_key=True,
        nullable=False,
        description="The Internal Database Id. "
        "Autogenerated if not passed, which is the recommended way",
    )
    """The Internal Database Id.
    Autogenerated if not passed, which is the recommended way"""

    name: str = Field(nullable=False, unique=True, description="The Source Name")
    """The Source Name"""

    type: str = Field(nullable=False, description="The Source Type")
    """The Source Type"""

    meta: dict[str, Any] | None = Field(
        sa_column=Column(
            JSON,
            default=None,
            nullable=True,
        ),
        description="Json Metadata For This Source",
    )
    """The Json Metadata For This Source"""

    ingestions: list["Ingestion"] = Relationship(back_populates="source")  # type: ignore since pylance doesn't do forward references
    """The list of Ingestions that use this Source.
    A 1 Source to N Ingestion relationship, meaning a
    join to another table on access. Use this for access."""
