"""Main entry point for the Conversational service."""

import logging

import uvicorn
from fastapi import FastAP<PERSON>

from gen_os_am_conversational.api.router import router, telemetry_router
from gen_os_am_conversational.settings import Settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="Conversational Service",
    description="Agent Conversational Tracking Service",
    version="0.1.0",
)

# Include routers
app.include_router(router)
app.include_router(telemetry_router)


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "service": "conversational"}


if __name__ == "__main__":
    settings = Settings.get_settings()
    uvicorn.run(
        "main:app",
        host=settings.API_LISTEN_HOST,
        port=settings.API_LISTEN_PORT,
        workers=settings.API_WORKERS,
        reload=True,
    )
