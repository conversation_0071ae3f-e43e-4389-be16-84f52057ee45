"""Add Delete to incidents.

Revision ID: 0e7472762e75
Revises: 9e947542e19f
Create Date: 2025-07-14 13:56:48.408537

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "0e7472762e75"
down_revision: str | None = "9e947542e19f"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Add delete to incidents."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("agent_configuration", schema=None) as batch_op:
        batch_op.create_index(
            batch_op.f("ix_agent_configuration_agent_id"), ["agent_id"], unique=False
        )

    with op.batch_alter_table(
        "reported_incidents_conversations", schema=None
    ) as batch_op:
        batch_op.add_column(
            sa.Column(
                "is_deleted", sa.<PERSON>olean(), server_default="false", nullable=False
            )
        )
        batch_op.add_column(
            sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True)
        )
        batch_op.add_column(sa.Column("deleted_by", sa.String(), nullable=True))

    with op.batch_alter_table("reported_incidents_workflows", schema=None) as batch_op:
        batch_op.add_column(
            sa.Column(
                "is_deleted", sa.Boolean(), server_default="false", nullable=False
            )
        )
        batch_op.add_column(
            sa.Column("deleted_at", sa.DateTime(timezone=True), nullable=True)
        )
        batch_op.add_column(sa.Column("deleted_by", sa.String(), nullable=True))

    # ### end Alembic commands ###


def downgrade() -> None:
    """Remove delete from incidents."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("reported_incidents_workflows", schema=None) as batch_op:
        batch_op.drop_column("deleted_by")
        batch_op.drop_column("deleted_at")
        batch_op.drop_column("is_deleted")

    with op.batch_alter_table(
        "reported_incidents_conversations", schema=None
    ) as batch_op:
        batch_op.drop_column("deleted_by")
        batch_op.drop_column("deleted_at")
        batch_op.drop_column("is_deleted")

    with op.batch_alter_table("agent_configuration", schema=None) as batch_op:
        batch_op.drop_index(batch_op.f("ix_agent_configuration_agent_id"))

    # ### end Alembic commands ###
