"""Test the document API."""

import asyncio
import io
import json
import os
import uuid
from datetime import datetime, timedelta, timezone

import pytest
from fastapi.testclient import TestClient

from gen_os_am_knowledge.sdk.models.common import (
    _DEFAULT_UUID,
    DEFAULT_INGESTION,
    DEFAULT_USER,
)

# Skip all tests in this module
# pytestmark = pytest.mark.skip(reason="Skipping all tests in test_06_document_api.py")

pytest_plugins = "pytest_asyncio"


@pytest.fixture(scope="module")
def sample_file():
    """Test the sample file API."""
    with open("test.txt", "w") as f:
        f.write("test")
    with open("test2.txt", "w") as f:
        f.write("test2")
    yield
    os.remove("test.txt")
    os.remove("test2.txt")


@pytest.fixture(scope="module")
def tags(client):
    """Test the tag API."""
    tag_names = ["water", "fire", "earth", "air"]
    tag_ids = []
    for tag_name in tag_names:
        response = client.post(
            "/tag",
            json={"tag_name": tag_name},
        )
        tag_ids.append(response.json()["id"])

        assert response.status_code == 201

    yield tag_names

    for tag_id in tag_ids:
        response = client.delete(f"/tag/{tag_id}")
        assert response.status_code == 200


@pytest.fixture(scope="module")
def folder(client):
    """Test the folder API."""
    folder_name = f"secret_{uuid.uuid4()}"
    response = client.post(
        "/folder",
        json={"folder_name": folder_name, "user_id": str(_DEFAULT_UUID)},
    )
    assert response.status_code == 201
    id_ = response.json()["id"]

    yield id_

    response = client.delete(f"/folder/{id_}/{str(_DEFAULT_UUID)}")
    assert response.status_code == 200
    # cleanup deleted folders
    response = client.delete("/folder/cleanup_deleted_folders")
    assert response.status_code == 200


@pytest.fixture(scope="module")
def user(client):
    """Test the user API."""
    user_name = "master"
    response = client.post(
        "/user",
        json={"user_name": user_name, "user_external_id": "master"},
    )
    yield response.json()["id"]
    response = client.delete(f"/user/{response.json()['id']}")
    assert response.status_code == 200


@pytest.fixture(scope="module")
def source(client):
    """Test the source API."""
    source_name = "test_source"
    response = client.post(
        "/source",
        json={"source_name": source_name, "source_type": "website"},
    )
    yield response.json()["id"]

    response = client.delete(f"/source/{response.json()['id']}")
    assert response.status_code == 200


@pytest.fixture(scope="module")
def ingestion_folder(client):
    """Test the ingestion folder API."""
    folder_name = "ingestion"
    response = client.post(
        "/folder",
        json={"folder_name": folder_name, "user_id": str(_DEFAULT_UUID)},
    )
    assert response.status_code == 201

    yield response.json()["id"]

    response = client.delete(f"/folder/{response.json()['id']}/{str(_DEFAULT_UUID)}")
    assert response.status_code == 200
    response = client.delete("/folder/cleanup_deleted_folders")
    assert response.status_code == 200


@pytest.fixture(scope="module")
def ingestion(client, source, ingestion_folder):
    """Test the ingestion API."""
    description = "test_ingestion"
    response = client.post(
        "/ingestion",
        json={
            "source_id": source,
            "description": description,
            "folder_id": ingestion_folder,
        },
    )
    yield response.json()["id"]

    response = client.delete(f"/ingestion/{response.json()['id']}")


# Helper function to create a document (optional, but reduces repetition)
def _create_document(
    client,
    kind,
    folder_id,
    tags,
    user_id=None,
    ingestion_id=None,
    draft=False,
    filename="test.txt",
    file_content=b"test",
):
    """Create a document via API call."""
    data = {
        "original_path": f"whatever/{filename}",
        "folder_id": folder_id,
        "tags": json.dumps(tags),
        "draft": draft,
    }
    if user_id:
        data["user_id"] = user_id
    if ingestion_id:
        data["ingestion_id"] = ingestion_id

    response = client.post(
        "/document",
        files={"file": (filename, io.BytesIO(file_content))},
        data=data,
    )
    response.raise_for_status()  # Raise an exception for bad status codes
    return response.json()


# Helper function to delete a document
def _delete_document(client, document_id, user_id):
    """Delete a document via API call."""
    response = client.delete(f"/document/{document_id}/{user_id}")
    response.raise_for_status()
    return response.json()


# TESTS


@pytest.mark.asyncio
async def test_create_slave_document(client, tmp_path, folder, tags, ingestion):
    """Test creating a slave document."""
    original_path = "whatever/slave.txt"
    # Create a temporary file for this test
    test_file_path = tmp_path / "slave_test.txt"
    file_content = b"slave"
    test_file_path.write_bytes(file_content)
    filename = test_file_path.name

    response = client.post(
        "/document",
        files={"file": (filename, open(test_file_path, "rb"))},  # Use the temp file
        data={
            "original_path": original_path,
            "ingestion_id": ingestion,
            "folder_id": folder,
            "tags": json.dumps(tags[:3]),
        },
    )

    response_json = response.json()
    assert response.status_code == 201
    assert response_json["original_path"] == original_path
    assert response_json["ingestion_id"] == ingestion
    assert response_json["folder_id"] == folder
    assert response_json["kind"] == "slave"
    assert response_json["size"] == len(file_content)
    assert response_json["type"] == "txt"
    assert response_json["filename"] == filename
    assert response_json["document_status"] == "Syncing VectorDB"
    assert response_json["created_by_id"] == str(DEFAULT_USER.id)

    # Cleanup
    _delete_document(client, response_json["id"], DEFAULT_USER.id)


@pytest.mark.asyncio
async def test_create_master_document(client, tmp_path, folder, tags, user):
    """Test creating a master document."""
    original_path = "whatever/master.txt"
    # Create a temporary file for this test
    test_file_path = tmp_path / "master_test.txt"
    file_content = b"master"
    test_file_path.write_bytes(file_content)
    filename = test_file_path.name

    response = client.post(
        "/document",
        files={"file": (filename, open(test_file_path, "rb"))},  # Use the temp file
        data={
            "original_path": original_path,
            "user_id": user,
            "folder_id": folder,
            "tags": json.dumps(tags[1:]),
        },
    )
    response_json = response.json()
    assert response.status_code == 201
    assert response_json["original_path"] == original_path
    assert response_json["created_by_id"] == user
    assert response_json["folder_id"] == folder
    assert response_json["kind"] == "master"
    assert response_json["size"] == len(file_content)  # Use known content length
    assert response_json["type"] == "txt"
    assert response_json["filename"] == filename  # Use the temp filename
    assert response_json["document_status"] == "Syncing VectorDB"
    assert response_json["ingestion_id"] == str(DEFAULT_INGESTION.id)

    # Cleanup
    _delete_document(client, response_json["id"], user)


@pytest.mark.asyncio
async def test_create_draft_document(client, tmp_path, folder, tags, user):
    """Test creating a draft document."""
    original_path = "whatever/draft.txt"
    # Create a temporary file for this test
    test_file_path = tmp_path / "draft_test.txt"
    file_content = b"draft"
    test_file_path.write_bytes(file_content)
    filename = test_file_path.name

    response = client.post(
        "/document",
        files={"file": (filename, open(test_file_path, "rb"))},  # Use the temp file
        data={
            "original_path": original_path,
            "user_id": user,
            "folder_id": folder,
            "tags": json.dumps(tags[1:]),
            "draft": True,
        },
    )
    response_json = response.json()
    assert response.status_code == 201
    assert response_json["original_path"] == original_path
    assert response_json["created_by_id"] == user
    assert response_json["folder_id"] == folder
    assert response_json["kind"] == "master"
    assert response_json["size"] == len(file_content)  # Use known content length
    assert response_json["type"] == "txt"
    assert response_json["filename"] == filename  # Use the temp filename
    assert response_json["document_status"] == "Syncing VectorDB"
    assert response_json["draft"] is True

    # Cleanup
    _delete_document(client, response_json["id"], user)


@pytest.mark.asyncio
async def test_create_document_with_user_and_ingestion_fails(
    client, tmp_path, folder, tags, user, ingestion
):
    """Test creating a document with both user_id and ingestion_id fails."""
    original_path = "whatever/bad.txt"
    # Create a temporary file for this test
    test_file_path = tmp_path / "bad_test.txt"
    file_content = b"bad"
    test_file_path.write_bytes(file_content)
    filename = test_file_path.name

    response = client.post(
        "/document",
        files={"file": (filename, open(test_file_path, "rb"))},  # Use the temp file
        data={
            "original_path": original_path,
            "user_id": user,
            "ingestion_id": ingestion,
            "folder_id": folder,
            "tags": json.dumps(tags),
        },
    )

    assert response.status_code == 400
    assert (
        response.json()["detail"]
        == "Only one of user_id or ingestion_id can be provided"
    )


@pytest.mark.asyncio
async def test_get_document(client, sample_file, folder, tags, user):
    """Test getting a document by ID."""
    # Setup: Create a document first
    doc_info = _create_document(
        client,
        "master",
        folder,
        tags[1:],
        user_id=user,
        filename="get_me.txt",
        file_content=b"get",
    )
    master_document_id = doc_info["id"]
    original_path = doc_info["original_path"]  # Use the actual path from creation

    # Test get
    response = client.get(f"/document/{master_document_id}")
    assert response.status_code == 200
    response_json = response.json()
    assert response_json["id"] == master_document_id
    assert response_json["original_path"] == original_path
    assert response_json["created_by_id"] == user
    assert response_json["folder_id"] == folder
    assert response_json["kind"] == "master"
    assert response_json["size"] == 3  # size of b"get"
    assert response_json["type"] == "txt"
    assert response_json["filename"] == "get_me.txt"
    assert response_json["document_status"] == "Syncing VectorDB"
    assert response_json["ingestion_id"] == str(DEFAULT_INGESTION.id)

    # Cleanup
    _delete_document(client, master_document_id, user)


@pytest.mark.asyncio
async def test_update_document(client, tmp_path, folder, tags, user):
    """Test updating a document's metadata and file content."""
    # Create a dummy file for updating
    update_file_path = tmp_path / "update.txt"
    update_file_path.write_bytes(b"updated")

    # Setup: Create a document first
    doc_info = _create_document(
        client,
        "master",
        folder,
        tags[1:],
        user_id=user,
        filename="to_update.txt",
        file_content=b"orig",
    )
    master_document_id = doc_info["id"]

    # Test update
    meta = json.dumps({"test": "test"})
    tags_ = json.dumps(tags)  # Update with all tags

    response = client.patch(
        f"/document/{master_document_id}/{user}",
        files={"file": ("update.txt", open(update_file_path, "rb"))},
        data={
            "meta": meta,
            "tags": tags_,
        },
    )

    assert response.status_code == 200
    response_json = response.json()
    assert response_json["success"] is True
    assert response_json["updated_instance"]["meta"] == json.loads(meta)
    assert response_json["error"] is None
    assert response_json["updated_values"]["meta"] == [None, json.loads(meta)]
    # Check tags update carefully - comparing sets is robust to order changes
    assert set(response_json["updated_values"]["tags"][0]) == set(tags[1:])
    assert set(response_json["updated_values"]["tags"][1]) == set(tags)
    # Filename should NOT change when updating file content this way
    assert response_json["updated_instance"]["filename"] == "to_update.txt"
    assert response_json["updated_instance"]["size"] == os.path.getsize(
        update_file_path
    )  # size of b"updated"

    # Cleanup
    _delete_document(client, master_document_id, user)


@pytest.mark.asyncio
async def test_update_document_with_file_and_filename_fails(
    client, tmp_path, folder, tags, user
):
    """Test that updating file content and filename simultaneously fails."""
    # Create a dummy file for updating
    update_file_path = tmp_path / "update_fail.txt"
    update_file_path.write_bytes(b"updated_fail")

    # Setup: Create a document first
    doc_info = _create_document(
        client,
        "master",
        folder,
        tags[1:],
        user_id=user,
        filename="update_fail_orig.txt",
        file_content=b"orig_fail",
    )
    master_document_id = doc_info["id"]

    # Test bad update
    response = client.patch(
        f"/document/{master_document_id}/{user}",
        files={"file": ("update_fail_newname.txt", open(update_file_path, "rb"))},
        data={
            "filename": "update_fail_newname.txt"
        },  # Attempting to change filename too
    )

    assertion_detail = (
        "Only one of file, filename or folder_id can be changed in a single operation"
    )
    assert response.status_code == 400
    assert response.json()["detail"] == assertion_detail

    # Cleanup
    _delete_document(client, master_document_id, user)


@pytest.mark.asyncio
async def test_update_document_status(client, folder, tags, user):
    """Test updating a document's status."""
    # Setup: Create a document first
    doc_info = _create_document(
        client,
        "master",
        folder,
        tags[1:],
        user_id=user,
        filename="status_update.txt",
        file_content=b"status",
    )
    master_document_id = doc_info["id"]

    # Test update status
    response = client.patch(
        f"/document/status/{master_document_id}/{user}",
        data={"status": "Synced"},
    )

    assert response.status_code == 200
    response_json = response.json()
    assert response_json["success"] is True
    assert response_json["updated_instance"]["document_status"] == "Synced"
    assert response_json["updated_values"]["document_status"] == [
        "Syncing VectorDB",
        "Synced",
    ]

    # Cleanup
    _delete_document(client, master_document_id, user)


@pytest.mark.asyncio
async def test_get_updated_documents(client, folder, tags, user):
    """Test retrieving documents updated after a specific timestamp."""
    # Setup: Create and update a document to ensure it appears in results
    doc_info = _create_document(
        client,
        "master",
        folder,
        tags[1:],
        user_id=user,
        filename="updated_doc.txt",
        file_content=b"update_me",
    )
    master_document_id = doc_info["id"]

    # Update status to modify updated_at timestamp (ensure it's recent)
    await asyncio.sleep(0.1)
    update_response = client.patch(
        f"/document/status/{master_document_id}/{user}",
        data={"status": "Synced"},
    )
    update_response.raise_for_status()
    update_time_str = update_response.json()["updated_instance"]["modification_date"]
    # Get timestamp slightly before the update
    update_time = datetime.fromisoformat(update_time_str.replace("Z", "+00:00"))
    query_time = update_time - timedelta(seconds=1)
    query_time_str = query_time.isoformat()

    # Test get updated docs using a timestamp *before* the update
    response = client.get(f"/updated_documents?from={query_time_str}")
    assert response.status_code == 200
    response_json = response.json()
    # Check if the updated document is in the list
    found = any(doc["id"] == master_document_id for doc in response_json)
    assert found, f"Document {master_document_id} not found in updated documents list"

    future_time = update_time + timedelta(days=1)
    future_time_str = future_time.isoformat()
    response = client.get(f"/updated_documents?from={future_time_str}")
    assert response.status_code == 200
    assert len(response.json()) == 0

    # Cleanup
    _delete_document(client, master_document_id, user)


@pytest.mark.asyncio
async def test_search_document(client, folder, tags, user):
    """Test searching documents by various criteria (kind, metadata)."""
    meta = {"search_meta_key": "search_meta_value"}
    meta_json = json.dumps(meta)

    # Setup: Create documents
    doc1_info = _create_document(
        client,
        "master",
        folder,
        tags,
        user_id=user,
        filename="search1.txt",
        file_content=b"s1",
    )
    doc1_id = doc1_info["id"]
    # Update doc1 with metadata
    client.patch(
        f"/document/{doc1_id}/{user}", data={"meta": meta_json}
    ).raise_for_status()

    doc2_info = _create_document(
        client,
        "slave",
        folder,
        tags,
        ingestion_id=str(DEFAULT_INGESTION.id),
        filename="search2.txt",
        file_content=b"s2",
    )  # Need ingestion_id for slave
    doc2_id = doc2_info["id"]

    # Test search by kind=master
    response = client.get("/document/?kind=master")
    assert response.status_code == 200
    results = response.json()
    assert len(results) >= 1
    assert any(doc["id"] == doc1_id for doc in results)
    assert not any(doc["id"] == doc2_id for doc in results)

    # Test search by kind=slave
    response = client.get("/document/?kind=slave")
    assert response.status_code == 200
    results = response.json()
    assert len(results) >= 1
    assert not any(doc["id"] == doc1_id for doc in results)
    assert any(doc["id"] == doc2_id for doc in results)

    # Test search by metadata field
    response = client.get(f"/document/?metadata_field={meta_json}")
    assert response.status_code == 200
    results = response.json()
    assert len(results) == 1
    assert results[0]["id"] == doc1_id

    # Test search by non-existent metadata
    non_existent_meta = json.dumps({"nonexistent": "value"})
    response = client.get(f"/document/?metadata_field={non_existent_meta}")
    assert response.status_code == 200
    assert len(response.json()) == 0

    # Cleanup
    _delete_document(client, doc1_id, user)
    _delete_document(
        client, doc2_id, str(DEFAULT_USER.id)
    )  # Slave doc deleted by default user


@pytest.mark.asyncio
async def test_draft_and_publish_document(client, folder, tags, user):
    """Test marking documents as draft and publishing them."""
    # Setup: Create a master and a draft document
    master_doc_info = _create_document(
        client,
        "master",
        folder,
        tags,
        user_id=user,
        filename="master_pub.txt",
        file_content=b"master",
    )
    master_doc_id = master_doc_info["id"]

    draft_doc_info = _create_document(
        client,
        "master",
        folder,
        tags,
        user_id=user,
        draft=True,
        filename="draft_pub.txt",
        file_content=b"draft",
    )
    draft_doc_id = draft_doc_info["id"]

    # Verify initial draft status
    response = client.get(f"/document/{master_doc_id}")
    assert response.json()["draft"] is False
    response = client.get(f"/document/{draft_doc_id}")
    assert response.json()["draft"] is True

    # Search non-draft (should find master_doc)
    response = client.get("/document/?draft=false")
    assert response.status_code == 200
    assert any(doc["id"] == master_doc_id for doc in response.json())
    assert not any(doc["id"] == draft_doc_id for doc in response.json())

    # Search with draft documents included (should find draft_doc)
    response = client.get("/document/?draft=true")
    assert response.status_code == 200
    assert any(doc["id"] == master_doc_id for doc in response.json())
    assert any(doc["id"] == draft_doc_id for doc in response.json())

    # Mark master_doc as draft
    response = client.patch(
        f"/document/{master_doc_id}/{user}",
        data={"draft": True},
    )
    assert response.status_code == 200
    response = client.get(f"/document/{master_doc_id}")
    assert response.json()["draft"] is True

    # Now both should be drafts
    response = client.get("/document/?draft=true")
    assert response.status_code == 200
    assert any(doc["id"] == master_doc_id for doc in response.json())
    assert any(doc["id"] == draft_doc_id for doc in response.json())

    response = client.get("/document/?draft=false")
    assert response.status_code == 200
    assert not any(doc["id"] == master_doc_id for doc in response.json())
    assert not any(doc["id"] == draft_doc_id for doc in response.json())

    # Publish both documents
    response = client.patch(
        f"/publish/{user}",
        json={"document_ids": [draft_doc_id, master_doc_id]},
    )
    assert response.status_code == 200
    assert len(response.json()) == 2  # API returns list of published docs

    # Verify both are no longer drafts
    response = client.get(f"/document/{draft_doc_id}")
    assert response.json()["draft"] is False
    response = client.get(f"/document/{master_doc_id}")
    assert response.json()["draft"] is False

    # Search non-draft (should find both)
    response = client.get("/document/?draft=false")
    assert response.status_code == 200
    assert any(doc["id"] == master_doc_id for doc in response.json())
    assert any(doc["id"] == draft_doc_id for doc in response.json())

    # Cleanup
    _delete_document(client, master_doc_id, user)
    _delete_document(client, draft_doc_id, user)


@pytest.mark.asyncio
async def test_delete_document_and_search_deleted(
    client, folder, tags, user, ingestion
):
    """Test deleting documents and searching for them."""
    # Setup: Create documents
    master_doc_info = _create_document(
        client,
        "master",
        folder,
        tags,
        user_id=user,
        filename="del_master.txt",
        file_content=b"delm",
    )
    master_doc_id = master_doc_info["id"]
    slave_doc_info = _create_document(
        client,
        "slave",
        folder,
        tags,
        ingestion_id=ingestion,
        filename="del_slave.txt",
        file_content=b"dels",
    )
    slave_doc_id = slave_doc_info["id"]

    # Verify they exist initially (search non-deleted)
    response = client.get("/document/?search_deleted=false")
    initial_docs = response.json()
    assert any(doc["id"] == master_doc_id for doc in initial_docs)
    assert any(doc["id"] == slave_doc_id for doc in initial_docs)
    initial_count = len(initial_docs)

    # Delete documents
    _delete_document(
        client, slave_doc_id, str(DEFAULT_USER.id)
    )  # Slave deleted by default user
    _delete_document(client, master_doc_id, user)

    # Search non-deleted (should not find them)
    response = client.get("/document/?search_deleted=false")
    assert response.status_code == 200
    # Account for potential other docs existing from concurrent tests or previous runs
    assert len(response.json()) <= max(0, initial_count - 2)
    assert not any(doc["id"] == master_doc_id for doc in response.json())
    assert not any(doc["id"] == slave_doc_id for doc in response.json())

    # Search deleted (should find them)
    response = client.get("/document/?search_deleted=true")
    assert response.status_code == 200
    deleted_docs = response.json()
    # Check they are present in the potentially larger list of all docs (incl. deleted)
    assert any(
        doc["id"] == master_doc_id and doc["deleted_date"] is not None
        for doc in deleted_docs
    )
    assert any(
        doc["id"] == slave_doc_id and doc["deleted_date"] is not None
        for doc in deleted_docs
    )

    # Assuming it returns ALL.
    assert len(deleted_docs) >= 2


@pytest.mark.asyncio
async def test_cleanup_deleted_documents(client, folder, tags, user):
    """Test the cleanup endpoint for permanently removing deleted documents."""
    # Setup: Create and delete a document
    doc_info = _create_document(
        client,
        "master",
        folder,
        tags,
        user_id=user,
        filename="cleanup.txt",
        file_content=b"clean",
    )
    doc_id = doc_info["id"]
    _delete_document(client, doc_id, user)

    # Verify it's searchable as deleted before cleanup
    response = client.get("/document/?search_deleted=true")
    assert any(doc["id"] == doc_id for doc in response.json())

    # Perform cleanup
    response = client.delete("/cleanup_deleted_documents/")
    assert response.status_code == 200

    # Verify it's no longer found even when searching deleted
    response = client.get("/document/?search_deleted=true")
    assert response.status_code == 200
    assert not any(doc["id"] == doc_id for doc in response.json())

    # Verify it's not found in regular search either
    response = client.get("/document/")
    assert response.status_code == 200
    assert not any(doc["id"] == doc_id for doc in response.json())
