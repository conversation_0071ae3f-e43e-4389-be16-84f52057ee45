"""Module for logging decorators."""

import contextvars
import functools
import logging
import os
import traceback
from datetime import datetime, timezone
from typing import Literal

import requests
from dotenv import load_dotenv

from gen_os_am_knowledge.utils.logging.log_models import (
    BaseLog,
    ErrorLog,
    HTTPRequest,
    HTTPResponse,
)

load_dotenv()
ENVIRONMENT = os.getenv("ENVIRONMENT", "DEV")

start_time_var = contextvars.ContextVar("start_time")


# ------------------------ #
#          Events          #
# ------------------------ #
def event_log(
    msg: str,
    start: datetime,
    logger: logging.Logger,
    type_: Literal["access", "change"] | None = None,
    category: list[Literal["database", "file"]] | None = None,
):
    """Log an event."""
    end = datetime.now(timezone.utc)
    msg = f"{msg}.\nDuration: {end - start}."
    if category:
        msg += f"\nCategory: `{category}`."
    if type_:
        msg += f"\nType: `{type_}`."
    log = BaseLog(
        environment=ENVIRONMENT,
        start=start,
        end=end,
    )
    logger.info(msg, extra=log.model_dump(mode="json"))


def error_event_log(
    msg: str,
    start: datetime,
    error: Exception,
    logger: logging.Logger,
    category: list[Literal["database", "file"]] | None = None,
    type_: Literal["access", "change"] | None = None,
):
    """Log an error event."""
    log = BaseLog(
        environment=ENVIRONMENT,
        start=start,
        end=datetime.now(timezone.utc),
        error=ErrorLog(
            code=0,
            message=str(error),
            type=type(error).__name__,
            stack_trace=traceback.format_exc(limit=-2),
        ),
    )
    msg = f"{msg}.\nDuration: {datetime.now(timezone.utc) - start}."
    if category:
        msg += f"\nCategory: `{category}`."
    if type_:
        msg += f"\nType: `{type_}`."
    logger.error(msg, extra=log.model_dump(mode="json"))


def log_decorator(
    log_success=True,
    log_error=True,
    type_: Literal["access", "change"] | None = None,
    category: list[Literal["database", "file"]] | None = None,
):
    """Decorate to log agent manager methods."""

    def decorator(func):
        @functools.wraps(func)
        async def wrapper(self, *args, **kwargs):
            start = datetime.now(timezone.utc)
            start_time_var.set(start)
            try:
                result = await func(self, *args, **kwargs)
                if log_success:
                    event_log(
                        msg=f"{func.__name__} executed successfully",
                        start=start,
                        logger=self.logger,
                        type_=type_,
                        category=category,
                    )
                return result
            except Exception as e:
                if log_error:
                    error_event_log(
                        msg=f"Error in {func.__name__}",
                        start=start,
                        error=e,
                        logger=self.logger,
                        category=category,
                        type_=type_,
                    )
                raise e

        return wrapper

    return decorator


# ------------------------ #
#            API           #
# ------------------------ #
def log_http_request(
    method: str,
    url: str,
    request_body: dict | None = None,
    headers: dict | None = None,
    logger: logging.Logger | None = None,
):
    """Log external API requests and responses using the APILoggingMiddleware format."""
    if logger is None:
        return

    start_dt = datetime.now(timezone.utc)
    error_log = None
    response_body = None
    request_size = None
    response_size = None

    try:
        req_body_content = str(request_body) if request_body else None
        request_size = len(req_body_content) if req_body_content else None
    except Exception:
        req_body_content = None

    try:
        response = requests.request(method, url, json=request_body, headers=headers)
        response.raise_for_status()
        response_body = response.text
        response_size = len(response.content)
        status_code = response.status_code
        mime_type = response.headers.get("content-type")

    except requests.RequestException as e:
        status_code = getattr(e.response, "status_code", 500)
        mime_type = getattr(e.response, "headers", {}).get("content-type", None)
        error_log = ErrorLog(
            code=status_code,
            message=str(e),
            type=type(e).__name__,
            stack_trace=traceback.format_exc(),
        )

    base_log = BaseLog(
        environment=ENVIRONMENT,
        start=start_dt,
        end=datetime.now(timezone.utc),
        request=HTTPRequest(
            method=method,
            bytes=request_size,
            mime_type=headers.get("content-type") if headers else None,
            body_content=req_body_content,
        ),
        response=HTTPResponse(
            status_code=status_code,
            bytes=response_size,
            mime_type=mime_type,
            body_content=response_body if status_code >= 400 else None,
        ),
        error=error_log,
    )

    if status_code < 400:
        logger.info(
            msg=f"Successful {method} call to {url}",
            extra=base_log.model_dump(mode="json"),
        )
    else:
        base_log.error = error_log
        logger.error(
            msg=f"Failed {method} call to {url}",
            extra=base_log.model_dump(mode="json"),
        )

    return response if "response" in locals() else None
