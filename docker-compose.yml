services:
  postgres:
    container_name: gen_os_agent_manager_postgres
    image: gen_os_agent_manager_postgres
    build:
      context: ./packages/gen_os_am_core/compose/postgres
      dockerfile: Dockerfile
    env_file:
      - .env
    environment:
      POSTGRES_USER: ${AM_CORE_DATABASE_USER}
      POSTGRES_PASSWORD: ${AM_CORE_DATABASE_PASSWORD}
      POSTGRES_PORT: ${AM_CORE_DATABASE_PORT}
      POSTGRES_DB: ${AM_CORE_DATABASE_NAME}
    volumes:
      - ./packages/gen_os_am_core/compose/postgres/scripts/:/docker-entrypoint-initdb.d/
      - ./packages/gen_os_am_core/compose/postgres/postgresql.conf:/etc/postgresql.conf
      - postgres_data:/var/lib/postgresql/data
    ports:
      - '${DATABASE_PORT}:5432'
    command: -c config_file=/etc/postgresql.conf
    healthcheck:
      test:
        [
          'CMD-SHELL',
          'pg_isready -U ${AM_CORE_DATABASE_USER} -d ${AM_CORE_DATABASE_NAME}',
        ]
      interval: 15s
      timeout: 15s
      retries: 10

  agent_manager:
    build:
      context: .
      dockerfile: Dockerfile
      no_cache: true
    env_file:
      - .env
    environment:
      - PYTHONPATH=/app/src
      - GOOGLE_APPLICATION_CREDENTIALS=/credentials/application_default_credentials.json
    volumes:
      - ./src:/app/src
      - km_storage:/app/storage/km
      - ${GOOGLE_APPLICATION_CREDENTIALS_LOCAL_PATH}:/credentials/application_default_credentials.json:ro
    ports:
      - '8080:8080'
    depends_on:
      postgres:
        condition: service_healthy

volumes:
  postgres_data:
  km_storage:
