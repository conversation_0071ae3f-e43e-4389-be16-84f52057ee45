"""Response models for the API."""

from uuid import UUID

from pydantic import BaseModel


class MessageResponse(BaseModel):
    """Response model for message."""

    message: str


class FileUploadResponse(BaseModel):
    """Response model for file upload."""

    file_id: UUID
    upload_url: str


class FileDownloadResponse(BaseModel):
    """Response model for file download."""

    id: UUID
    url: str
    file_name: str


class EmailExistsResponse(BaseModel):
    """Response model for email exists check."""

    exists: bool


class EmailMessageIdsResponse(BaseModel):
    """Response model for email message IDs."""

    message_ids: list[str]
