"""Unit tests for the storage module."""

import uuid
from datetime import UTC, datetime
from unittest.mock import MagicMock, patch

import pytest
from gen_os_sdk_emulator.infrastructure.storage import (
    DownloadInfo,
    FileMetadata,
    UploadInfo,
)
from gen_os_sdk_emulator.infrastructure.storage.storage import GCPStorageService


@pytest.fixture
def file_id():
    """Return a fixed UUID for testing."""
    return uuid.UUID("12345678-1234-5678-1234-************")


@pytest.fixture
def file_metadata(file_id):
    """Return a file metadata object for testing."""
    return FileMetadata(
        id=file_id,
        file_name="test_file.txt",
        storage_path=f"files/{file_id}.txt",
        content_type="text/plain",
    )


@pytest.fixture
def mock_storage_client():
    """Create a mock GCP storage client."""
    with patch("google.cloud.storage.Client") as mock_client:
        # Set up the blob mock
        mock_blob = MagicMock()
        mock_blob.generate_signed_url.return_value = "https://example.com/signed-url"

        # Set up the bucket mock
        mock_bucket = MagicMock()
        mock_bucket.blob.return_value = mock_blob

        # Set up the client mock
        mock_client.return_value = MagicMock()
        mock_client.return_value.bucket.return_value = mock_bucket

        yield mock_client


@pytest.fixture
def mock_auth():
    """Create a mock GCP auth service with proper credentials."""
    with patch("google.auth.default") as mock_auth_default:
        # Set up the credentials mock with required attributes
        mock_credentials = MagicMock()
        mock_credentials.service_account_email = "<EMAIL>"
        mock_credentials.token = "fake-token"

        # Set up the auth default return value
        mock_auth_default.return_value = (mock_credentials, "test-project")

        yield mock_auth_default


@pytest.fixture
def mock_impersonated_credentials():
    """Mock the impersonated credentials class."""
    with patch("google.auth.impersonated_credentials.Credentials") as mock_credentials:
        # Set up the impersonated credentials mock
        mock_credentials.return_value = MagicMock()
        yield mock_credentials


class TestGCPStorageService:
    """Tests for the GCPStorageService class."""

    def test_get_storage_path(self, file_id):
        """Test that get_storage_path returns the correct path format."""
        # Arrange
        service = GCPStorageService(
            bucket_name="test-bucket",
            project_name="test-project",
            service_account_email="<EMAIL>",
        )

        # Act
        result = service.get_storage_path(file_id, "test_file.txt")

        # Assert
        assert result == f"files/{file_id}.txt"

    @pytest.mark.asyncio
    async def test_generate_upload_url(
        self,
        file_metadata,
        mock_storage_client,
        mock_auth,
        mock_impersonated_credentials,
    ):
        """Test that generate_upload_url calls the GCP client correctly."""
        # Arrange
        service = GCPStorageService(
            bucket_name="test-bucket",
            project_name="test-project",
            service_account_email="<EMAIL>",
        )

        # Act
        with patch(
            "gen_os_sdk_emulator.infrastructure.storage.storage.datetime"
        ) as mock_datetime:
            mock_datetime.now.return_value = datetime(2023, 1, 1, 12, 0)
            mock_datetime.UTC = UTC
            result = await service.generate_upload_url(file_metadata)

        # Assert
        assert isinstance(result, UploadInfo)
        assert result.file_id == file_metadata.id
        assert result.upload_url == "https://example.com/signed-url"

        # Verify interactions with mocks
        mock_storage_client.assert_called_once_with(project="test-project")
        mock_storage_client.return_value.bucket.assert_called_once_with("test-bucket")
        mock_storage_client.return_value.bucket.return_value.blob.assert_called_once_with(
            file_metadata.storage_path
        )

    @pytest.mark.asyncio
    async def test_generate_download_url(
        self,
        file_metadata,
        mock_storage_client,
        mock_auth,
        mock_impersonated_credentials,
    ):
        """Test that generate_download_url calls the GCP client correctly."""
        # Arrange
        service = GCPStorageService(
            bucket_name="test-bucket",
            project_name="test-project",
            service_account_email="<EMAIL>",
        )

        # Act
        with patch(
            "gen_os_sdk_emulator.infrastructure.storage.storage.datetime"
        ) as mock_datetime:
            mock_datetime.now.return_value = datetime(2023, 1, 1, 12, 0)
            mock_datetime.UTC = UTC
            result = await service.generate_download_url(file_metadata)

        # Assert
        assert isinstance(result, DownloadInfo)
        assert result.file_id == file_metadata.id
        assert result.download_url == "https://example.com/signed-url"
        assert result.file_name == file_metadata.file_name

        # Verify interactions with mocks
        mock_storage_client.assert_called_once_with(project="test-project")
        mock_storage_client.return_value.bucket.assert_called_once_with("test-bucket")
        mock_storage_client.return_value.bucket.return_value.blob.assert_called_once_with(
            file_metadata.storage_path
        )
