"""Test the source API."""

import pytest


def test_source(client):  # Changed fixture from setup_api to client
    """Test the source API."""
    # Removed: app = setup_api

    source_name = "test"
    source_type = "website"
    # Removed: with TestClient(app) as c:
    response = client.post(  # Changed c.post to client.post
        "/source", json={"source_name": source_name, "source_type": source_type}
    )

    assert response.status_code == 201
    response_json = response.json()
    assert response_json["name"] == source_name
    assert response_json["type"] == source_type
    source_id = response_json["id"]

    # get
    # Removed: with TestClient(app) as c:
    response = client.get(f"/source/{source_id}")  # Changed c.get to client.get
    assert response.status_code == 200
    response_json = response.json()
    assert response_json["name"] == source_name
    assert response_json["type"] == source_type

    # update
    updated_source_name = "update"
    updated_meta = {"metafield1": "test1", "metafield2": "test2"}

    # Removed: with TestClient(app) as c:
    response = client.patch(  # Changed c.patch to client.patch
        f"/source/{source_id}",
        json={
            "source_name": updated_source_name,
            "meta": updated_meta,
        },
    )
    assert response.status_code == 200
    response_json = response.json()
    assert response_json["success"]
    assert response_json["updated_instance"]["name"] == updated_source_name
    assert response_json["updated_instance"]["meta"] == updated_meta
    assert response_json["error"] is None
    assert response_json["updated_values"] == {
        "name": [source_name, updated_source_name],
        "meta": [None, updated_meta],
    }

    # search
    metadata_field = '{"metafield1": "test1"}'
    # Removed: with TestClient(app) as c:
    response = client.get(  # Changed c.get to client.get
        f"/source?source_name={updated_source_name}"
        f"&source_type={source_type}&metadata_field={metadata_field}"
    )

    assert response.status_code == 200
    response_json = response.json()
    assert len(response_json) == 1
    assert response_json[0]["name"] == updated_source_name
    assert response_json[0]["type"] == source_type
    assert response_json[0]["meta"] == updated_meta

    # delete
    # Removed: with TestClient(app) as c:
    response = client.delete(
        f"/source/{source_id}"
    )  # Changed c.delete to client.delete
    assert response.status_code == 200
    response_json = response.json()
    assert response_json["name"] == updated_source_name
    assert response_json["type"] == source_type
    assert response_json["meta"] == updated_meta
