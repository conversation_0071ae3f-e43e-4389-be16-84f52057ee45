"""Add order_number to step block; and add input to StepRun.

Revision ID: 911aafbcd3a0
Revises: dbd896cf34f6
Create Date: 2025-05-29 09:50:09.255156

"""

from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "911aafbcd3a0"
down_revision: str | None = "dbd896cf34f6"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Add order_number to step_input and step_output; and add input to StepRun."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("step_input", schema=None) as batch_op:
        batch_op.add_column(sa.Column("order_number", sa.Integer(), nullable=False))

    with op.batch_alter_table("step_output", schema=None) as batch_op:
        batch_op.add_column(sa.Column("order_number", sa.Integer(), nullable=False))

    with op.batch_alter_table("step_run", schema=None) as batch_op:
        batch_op.add_column(sa.Column("input", sa.JSON(), nullable=False))

    # ### end Alembic commands ###


def downgrade() -> None:
    """Remove order_number from step_input and step_output; and remove input from StepRun."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("step_run", schema=None) as batch_op:
        batch_op.drop_column("input")

    with op.batch_alter_table("step_output", schema=None) as batch_op:
        batch_op.drop_column("order_number")

    with op.batch_alter_table("step_input", schema=None) as batch_op:
        batch_op.drop_column("order_number")

    # ### end Alembic commands ###
