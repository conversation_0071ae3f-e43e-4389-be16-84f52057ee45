"""Module for the Folder API."""

import logging
import uuid
from typing import Literal

from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from gen_os_am_knowledge.api.utils import error_handling_decorator, validate_boolean
from gen_os_am_knowledge.config.logger_config import get_api_logger
from gen_os_am_knowledge.sdk.filesystem_connectors.base import BaseFileSystemConnector
from gen_os_am_knowledge.sdk.models import Folder
from gen_os_am_knowledge.sdk.models.common import _DEFAULT_UUID
from gen_os_am_knowledge.sdk.operations import FolderService
from gen_os_am_knowledge.sdk.operations.folder_ops import (
    EnrichedFolder,
    RecursiveSearchResult,
)
from gen_os_am_knowledge.sdk.operations.utils import NOT_PROVIDED


class UpdateFolderModel(BaseModel):
    """Update folder model."""

    folder_name: str | type[NOT_PROVIDED] = NOT_PROVIDED
    parent_folder_id: uuid.UUID | None | type[NOT_PROVIDED] = NOT_PROVIDED


class CreateFolderModel(BaseModel):
    """Create folder model."""

    folder_name: str
    user_id: uuid.UUID
    parent_id: uuid.UUID | None = None


class FolderAPI:
    """API for folder operations.

    Args:
        prefix (str, optional): prefix for the API. Defaults to "".

    Methods:
        get_router: returns the router for the API

    """

    def __init__(
        self,
        fs_connector: BaseFileSystemConnector,
        prefix: str = "",
        logger: logging.Logger = get_api_logger(),
    ):
        """Initialize the FolderAPI class."""
        self.prefix = prefix
        self.folder_service = FolderService(
            fs_connector=fs_connector, logger=logger.getChild("sdk")
        )

    def get_router(self) -> APIRouter:
        """Return the FastAPI router for the folder API.

        Returns:
            APIRouter: The FastAPI router for the folder API

        """
        router = APIRouter(prefix=self.prefix + "/folder")

        @router.post("/")
        @error_handling_decorator
        async def create_folder(create_model: CreateFolderModel) -> JSONResponse:
            """Create a folder.

            Args:
                create_model (CreateFolderModel): Folder creation model as json
                    Ex:
                    {
                        "user_id": "user_id",
                        "folder_name": "folder_name",
                        "parent_id": "parent_id"
                    }

            Returns:
                JSONResponse: created folder

            """
            result = await self.folder_service.create_folder(
                **create_model.model_dump()
            )
            return JSONResponse(content=result.model_dump(mode="json"), status_code=201)

        @router.delete("/{folder_id}/{user_id}")
        @error_handling_decorator
        async def delete_folder(
            folder_id: uuid.UUID, user_id: uuid.UUID
        ) -> Folder | None:
            """Delete a folder.

            Args:
                folder_id (uuid.UUID): folder id to delete
                user_id (uuid.UUID): user id of the user that wants to delete the folder

            Returns:
                Folder: deleted folder instance

            """
            result = await self.folder_service.delete_folder(folder_id, user_id)
            if result.success:
                return result.deleted_instance
            else:
                raise HTTPException(status_code=404, detail=result.error)

        @router.get("/{folder_id}")
        @error_handling_decorator
        async def get_folder(
            folder_id: uuid.UUID,
            include_dependencies: bool = False,
            include_published_and_deleted: bool = False,
            include_deleted_folders: bool = False,
        ) -> EnrichedFolder | None:
            """Get a folder.

            Args:
                folder_id (uuid.UUID): folder id to get
                include_dependencies (bool, optional): include dependencies
                    (files and folders in this folder). Defaults to False.
                include_published_and_deleted (bool, optional): whether to include
                    files that are deleted and published. Defaults to False.
                include_deleted_folders (bool, optional): whether to include
                    folders that are deleted. Defaults to False.

            Returns:
                EnrichedFolder | None: The folder instance with all the contents and
                    ingestion that uses that folder as root or None if not found.

            """
            include_dependencies = validate_boolean(include_dependencies)
            include_published_and_deleted = validate_boolean(
                include_published_and_deleted
            )
            include_deleted_folders = validate_boolean(include_deleted_folders)
            result = await self.folder_service.get_folder(
                folder_id=folder_id,
                include_dependencies=include_dependencies,
                include_published_and_deleted=include_published_and_deleted,
                include_deleted_folders=include_deleted_folders,
            )
            if include_dependencies and result is not None:
                if result.documents is not None:
                    documents = []
                    for doc in result.documents:
                        doc.hash = str(doc.hash)
                        documents.append(doc)
                    result.documents = documents
            return result

        @router.get("/")
        @error_handling_decorator
        async def search_folders(
            folder_name: str | None = None,
            parent_id: uuid.UUID | Literal["null"] | type[NOT_PROVIDED] = NOT_PROVIDED,
            offset: int = 0,
            limit: int = 20,
        ) -> list[Folder]:
            """Search folders.

            Args:
                folder_name (str, optional): folder name to search
                parent_id (uuid.UUID, optional): parent folder id to search.
                    Send "null" to search for root folders.
                offset (int, optional): offset for pagination. Defaults to 0.
                limit (int, optional): limit for pagination. Defaults to 20.

            Returns:
                list[Folder]: list of folders found

            """
            if parent_id == "null":
                parent_id = None  # type: ignore
            result = await self.folder_service.search_folders(
                folder_name=folder_name, parent_id=parent_id, offset=offset, limit=limit
            )
            return result

        @router.patch("/{folder_id}/{user_id}")
        @error_handling_decorator
        async def update_folder(
            folder_id: uuid.UUID,
            user_id: uuid.UUID,
            update_model: UpdateFolderModel,
        ):
            """Update a folder.

            Args:
                folder_id (uuid.UUID): folder id to update
                user_id (uuid.UUID): user id of the user that wants to update the folder
                update_model (UpdateFolderModel): Folder update model as json
                    Ex:
                    {
                        "folder_name": "folder_name",
                        "parent_folder_id": "parent_folder_id"
                    }

            Returns:
                Folder: updated folder

            """
            result = await self.folder_service.update_folder(
                folder_id=folder_id, user_id=user_id, **update_model.model_dump()
            )
            return result

        @router.get("/parents/{folder_id}")
        @error_handling_decorator
        async def get_parent_folders(folder_id: uuid.UUID) -> list[Folder]:
            """Get parent folders of a folder.

            Args:
                folder_id (uuid.UUID): folder id to get parents of

            Returns:
                list[Folder]: list of parent folders sorted by depth,
                    where the last one is the folder itself

            """
            path = await self.folder_service.get_folder_path(folder_id=folder_id)
            if path is not None:
                folders = await self.folder_service.create_folders_from_path(
                    path=path, user_id=_DEFAULT_UUID
                )  # it is not used to create folders, just to get the folder instances
                return folders

        @router.delete("/cleanup_deleted_folders")
        @error_handling_decorator
        async def cleanup_deleted_folders():
            """Cleanup deleted folders.

            Returns:
                tuple[list[Folder], list[Folder]]: list of deleted and
                    not deleted folders

            """
            result = await self.folder_service.cleanup_deleted_folders()
            return result

        @router.get("/{folder_id}/tree")
        @error_handling_decorator
        async def get_folder_tree(
            folder_id: uuid.UUID,
            include_published_and_deleted: bool = False,
            include_deleted_folders: bool = False,
        ) -> RecursiveSearchResult | None:
            """Get a folder tree.

            Args:
                folder_id (uuid.UUID): folder id to get
                include_published_and_deleted (bool, optional): whether to include
                    files that are deleted and published. Defaults to False.
                include_deleted_folders (bool, optional): whether to include
                    folders that are deleted. Defaults to False.

            Returns:
                RecursiveSearchResult | None: The folder instance with all the
                    contents and ingestion that uses that folder as root or None
                    if not found.

            """
            include_published_and_deleted = validate_boolean(
                include_published_and_deleted
            )
            include_deleted_folders = validate_boolean(include_deleted_folders)
            result = await self.folder_service.recursive_folder_tree(
                folder_id=folder_id,
                include_published_and_deleted=include_published_and_deleted,
                include_deleted_folders=include_deleted_folders,
            )
            return result

        return router
