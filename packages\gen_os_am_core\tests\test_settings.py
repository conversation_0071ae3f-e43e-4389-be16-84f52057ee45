"""Tests for the settings module in the Agent Manager component."""

import pytest

from gen_os_am_core.settings import Settings


def test_settings_initialization():
    """Test that settings can be initialized."""
    settings = Settings.get_settings()
    assert settings is not None
    assert settings.API_LISTEN_HOST == "localhost"  # Test environment sets this
    assert settings.API_LISTEN_PORT == 8080
