"""Test utils for the web scraper."""

import pytest

try:
    from bs4 import BeautifulSoup
    from ctt_am_ingestions.website_ingestion.web_scraping.utils import (
        add_text,
        extract_list_items,
        extract_table_data,
        get_headline_from_section_header,
        get_highest_header,
        get_main_list,
        get_scope,
        get_tabs_dict,
        normalize_url,
        prepare_text,
        remove_extra_spaces,
    )
except ImportError:
    pytest.skip(
        "Ingestions are not part of the scope of the project", allow_module_level=True
    )


def test_get_scope():
    """Test the get_scope function."""
    assert (
        get_scope("https://www.example.com/help/support/articles/how-to-fix")
        == "Help / Support / Articles"
    )
    assert get_scope("https://www.test.com/only-one-segment/") == ""
    assert get_scope("https://www.sample.com/start/middle/end") == "Start / Middle"
    assert get_scope("https://www.dummy.com/") == ""


def test_normalize_url():
    """Test the normalize_url function."""
    # Test 1: A URL with percent-encoded characters
    url = "HTTP://EXAMPLE.COM/Path%20To%20Resource"
    normalized = normalize_url(url)
    assert (
        normalized == "http://example.com/path to resource"
    ), f"Expected 'http://example.com/path to resource', got {normalized}"

    # Test 2: A URL already in lowercase (no change expected)
    url = "https://example.com/some/path"
    normalized = normalize_url(url)
    assert (
        normalized == "https://example.com/some/path"
    ), f"Expected 'https://example.com/some/path', got {normalized}"

    # Test 3: A URL with mixed case (should be converted to lowercase)
    url = "HTTPS://ExAmPlE.CoM/Path/With/MiXeD/Case"
    normalized = normalize_url(url)
    assert (
        normalized == "https://example.com/path/with/mixed/case"
    ), f"Expected 'https://example.com/path/with/mixed/case', got {normalized}"

    # Test 4: URL with query parameters (percent-encoded)
    url = "https://example.com/search?q=python%20testing"
    normalized = normalize_url(url)
    assert (
        normalized == "https://example.com/search?q=python testing"
    ), f"Expected 'https://example.com/search?q=python testing', got {normalized}"

    # Test 5: URL with an already decoded path and no special characters
    url = "https://example.com/normal/path"
    normalized = normalize_url(url)
    assert (
        normalized == "https://example.com/normal/path"
    ), f"Expected 'https://example.com/normal/path', got {normalized}"

    # Test 6: URL with only a domain (no path, no query)
    url = "HTTPS://EXAMPLE.COM"
    normalized = normalize_url(url)
    assert (
        normalized == "https://example.com"
    ), f"Expected 'https://example.com', got {normalized}"

    # Test 7: URL with empty string (should return empty string)
    url = ""
    normalized = normalize_url(url)
    assert normalized == "", f"Expected '', got {normalized}"


def make_soup(html: str):
    """Make Beautiful Soup help function."""
    return BeautifulSoup(html, "html.parser")


def test_get_highest_header():
    """Test the get_highest_header function."""
    html = """
    <div>
        <h3>Title Level 3</h3>
        <h2>Title Level 2</h2>
        <h1>Title Level 1</h1>
    </div>
    """
    soup = make_soup(html)
    assert get_highest_header(soup) == "h1"

    html = """<div><h5>Title</h5></div>"""
    soup = make_soup(html)
    assert get_highest_header(soup) == "h5"

    html = """<div>No headers here</div>"""
    soup = make_soup(html)
    assert get_highest_header(soup) is None


def test_get_tabs_dict():
    """Test the get_tabs_dict function."""
    html = """
    <ul class="tabs">
        <li><a href="#tab1">Tab 1</a></li>
        <li><a href="#tab2">Tab 2</a></li>
    </ul>
    """
    soup = make_soup(html)
    assert get_tabs_dict(soup) == {"tab1": "Tab 1", "tab2": "Tab 2"}

    html = """<div>No tabs here</div>"""
    soup = make_soup(html)
    assert get_tabs_dict(soup) is None


def test_get_main_list():
    """Test the get_main_list function."""
    html = """
    <div>
        <p>Some text</p>
        <div>
            <ul>
                <li>Item 1</li>
                <li>Item 2</li>
            </ul>
        </div>
    </div>
    """
    soup = BeautifulSoup(html, "html.parser")
    elem = soup.div
    result = get_main_list(elem)
    assert result is not None
    assert result.name == "ul"
    assert len(result.find_all("li")) == 2

    html = """
    <div>
        <p>Some text</p>
        <div>
            <span>No list here</span>
        </div>
    </div>
    """
    soup = BeautifulSoup(html, "html.parser")
    elem = soup.div
    result = get_main_list(elem)
    assert result is None


def test_get_headline_from_section_header():
    """Test the get_headline_from_section_header function."""
    html = """
    <div>
        <div class="section-header">Main Headline</div>
    </div>
    """
    soup = BeautifulSoup(html, "html.parser")
    elem = soup.div
    result = get_headline_from_section_header(elem)
    assert result == "Main Headline"

    html = """
    <div>
        <p>No section header here</p>
    </div>
    """
    soup = BeautifulSoup(html, "html.parser")
    elem = soup.div
    result = get_headline_from_section_header(elem)
    assert result is None


def test_remove_extra_spaces():
    """Test the remove_extra_spaces function."""
    assert (
        remove_extra_spaces("Text    with   extra spaces.") == "Text with extra spaces."
    )
    assert (
        remove_extra_spaces("Text before , punctuation.") == "Text before, punctuation."
    )
    assert (
        remove_extra_spaces("    Leading and trailing spaces    ")
        == "Leading and trailing spaces"
    )


def test_prepare_text():
    """Test the prepare_text function."""
    html = """
    <p>This is a <a href="/link">link</a>. Another <span>span</span> text.</p>
    """
    soup = make_soup(html)
    assert (
        prepare_text(soup)
        == "This is a link[https://www.ctt.pt/link]. Another span text."
    )

    html = """<p>Plain text</p>"""
    soup = make_soup(html)
    assert prepare_text(soup) == "Plain text"


def test_extract_table_data():
    """Test the extract_table_data function."""
    html = """
    <table>
        <tr><th>Header 1</th><th>Header 2</th></tr>
        <tr><td>Data 1</td><td>Data 2</td></tr>
        <tr><td>Data 3</td><td>Data 4</td></tr>
    </table>
    """
    soup = make_soup(html)
    expected = """
| Header 1 | Header 2 |
|---|---|
| Data 1 | Data 2 |
| Data 3 | Data 4 |
    """.strip()
    assert extract_table_data(soup) == expected

    html = """<table><tr><td>Only data</td></tr></table>"""
    soup = make_soup(html)
    expected = """
| Only data |
    """.strip()
    assert extract_table_data(soup) == expected


def test_extract_list_items():
    """Test the extract_list_items function."""
    html = """<ul>
                <li>Item 1</li>
                <li>Item 2</li>
                <li>Item 3</li>
            </ul>"""

    soup = BeautifulSoup(html, "html.parser")
    elem = soup.find("ul")

    expected_output = "- Item 1\n- Item 2\n- Item 3"
    assert extract_list_items(elem) == expected_output

    # Test empty list
    empty_html = "<ul></ul>"
    empty_soup = BeautifulSoup(empty_html, "html.parser")
    empty_elem = empty_soup.find("ul")
    assert extract_list_items(empty_elem) == ""


def test_add_text():
    """Test the add_text function."""
    # Test when target is empty
    assert add_text("", "new data") == "new data"

    # Test when target is non-empty
    assert add_text("existing data", "new data") == "existing data\nnew data"

    # Test when new data is empty
    assert add_text("existing data", "") == "existing data\n"

    # Test when both target and new data are empty
    assert add_text("", "") == ""

    # Test when target contains only whitespace
    assert add_text(" ", "new data") == " \nnew data"

    # Test when target contains multiple lines
    assert add_text("line1\nline2", "new data") == "line1\nline2\nnew data"
