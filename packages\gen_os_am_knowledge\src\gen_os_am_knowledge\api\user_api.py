"""Module for the User API."""

import logging
import uuid

from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from gen_os_am_knowledge.api.utils import error_handling_decorator
from gen_os_am_knowledge.config.logger_config import get_api_logger
from gen_os_am_knowledge.sdk.models import User
from gen_os_am_knowledge.sdk.operations import UserService
from gen_os_am_knowledge.sdk.operations.utils import NOT_PROVIDED


class UpdateUserModel(BaseModel):
    """Update user model."""

    user_name: str | type[NOT_PROVIDED] = NOT_PROVIDED
    user_external_id: str | type[NOT_PROVIDED] = NOT_PROVIDED


class CreateUserModel(BaseModel):
    """Create user model."""

    user_name: str
    user_external_id: str


class UserAPI:
    """User API class to handle user related operations.

    Args:
        prefix (str, optional): Prefix for the user API. Defaults to "".

    Methods:
        get_router: Returns the FastAPI router for the user API

    """

    def __init__(
        self,
        prefix: str = "",
        logger: logging.Logger = get_api_logger(),
    ):
        """Initialize the UserAPI."""
        self.prefix = prefix
        self.user_service = UserService(
            logger=logger.getChild("sdk"),
        )

    def get_router(self) -> APIRouter:
        """Return the FastAPI router for the user API.

        Returns:
            APIRouter: FastAPI router for the user API

        """
        router = APIRouter(prefix=self.prefix + "/user")

        @router.post("/")
        @error_handling_decorator
        async def create_user(update_model: CreateUserModel) -> JSONResponse:
            """Create a new user.

            Args:
                update_model (CreateUserModel): User creation model
                    Ex:
                    {
                        "user_name": "new_user_name",
                        "user_external_id": "new_user_external_id"
                    }

            Returns:
                JSONResponse: JSON response with the created user

            """
            result = await self.user_service.create_user(**update_model.model_dump())
            return JSONResponse(content=result.model_dump(mode="json"), status_code=201)

        @router.delete("/{user_id}")
        @error_handling_decorator
        async def delete_user(user_id: uuid.UUID) -> User | None:
            """Delete a user.

            Args:
                user_id (uuid.UUID): User ID

            Returns:
                User: Deleted user

            """
            result = await self.user_service.delete_user(user_id)
            if result.success:
                return result.deleted_instance
            else:
                raise HTTPException(status_code=404, detail=result.error)

        @router.get("/{user_id}")
        @error_handling_decorator
        async def get_user(user_id: uuid.UUID) -> User | None:
            """Get a user.

            Args:
                user_id (uuid.UUID): User ID

            Returns:
                User: User instance if found else None

            """
            result = await self.user_service.get_user(user_id)
            return result

        @router.get("/")
        @error_handling_decorator
        async def search_users(
            user_name: str | None = None,
            user_external_id: str | None = None,
            offset: int = 0,
            limit: int = 20,
        ) -> list[User]:
            """Search users with query parameters.

            Args:
                user_name (str | None): User name
                user_external_id (str | None): User external ID
                offset (int): Offset for pagination
                limit (int): Limit for pagination

            Returns:
                list[User]: List of users

            """
            result = await self.user_service.search_users(
                user_name=user_name,
                user_external_id=user_external_id,
                offset=offset,
                limit=limit,
            )
            return result

        @router.patch("/{user_id}")
        @error_handling_decorator
        async def update_user(
            user_id: uuid.UUID,
            update_model: UpdateUserModel,
        ):
            """Update a user.

            Args:
                user_id (uuid.UUID): User ID. Path parameter
                update_model (UpdateUserModel): User update model
                    Ex:
                    {
                        "user_name": "new_user_name",
                        "user_external_id": "new_user_external_id"
                    }

            Returns:
                UpdateResult: Update result with the updated user instance

            """
            result = await self.user_service.update_user(
                user_id, **update_model.model_dump()
            )
            return result

        return router
