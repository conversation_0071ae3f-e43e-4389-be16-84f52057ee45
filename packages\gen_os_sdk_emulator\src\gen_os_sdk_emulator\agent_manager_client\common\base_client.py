"""Base client for the agent manager."""

from gen_os_sdk_emulator.agent_manager_client.common.auth_provider import AuthProvider


class BaseClient:
    """Base client for the agent manager and its services."""

    def __init__(self, auth_provider: AuthProvider):
        """Initialize the base client.

        Args:
            auth_provider: The authentication provider to use

        """
        self._auth_provider = auth_provider

    async def _handle_headers(
        self,
        content_type: str = "application/json",
        with_content_type: bool = False,
        trace_id: str | None = None,
        app_caller: str | None = None,
    ) -> dict:
        """Return the headers needed for authenticated requests.

        Args:
            content_type: The content type of the request
            with_content_type: Whether to include the content type in the headers
            trace_id: The trace ID for the request
            app_caller: The app caller for the request

        """
        headers = {}
        if trace_id:
            headers["X-eTrackingID"] = trace_id
        if app_caller:
            headers["X-process"] = app_caller

        if not self._auth_provider.requires_auth:
            return dict(
                headers,
                **({"Content-Type": content_type} if with_content_type else {}),
            )

        token = await self._auth_provider.get_auth_token()
        return dict(
            headers,
            **{"Authorization": f"Bearer {token}", "Content-Type": content_type},
        )
