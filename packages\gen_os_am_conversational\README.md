# Conversational Service

The Conversational Service handles agent conversation tracking and telemetry within the Gen OS ecosystem.

## Run locally

### Docker

You can run the Conversational service using Docker with a single command:

```bash
pdm run conversational-docker
```

This command will:

1. Configure the environment for Docker
2. Build and start all containers (API, Postgres, etc.)

To stop and remove the containers:

```bash
docker compose down
```

### Source

> Make sure you've created a fresh environment and installed the dependencies using `pdm`.

To run the service locally:

```bash
pdm run conversational-source
```

This command will:

1. Start the local Postgres database
2. Run database migrations
3. Configure the environment for local development
4. Start the API server at http://localhost:8081

To stop the database:

```bash
pdm run stop-database
```
---
