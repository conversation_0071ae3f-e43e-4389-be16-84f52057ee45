"""Tests for folder operations functionality."""

import os
import uuid

import pytest
import pytest_asyncio
from pytest import fixture

from gen_os_am_knowledge.sdk.filesystem_connectors.local_fs_connector import (
    LocalFSConnector,
)
from gen_os_am_knowledge.sdk.models.common import _DEFAULT_UUID
from gen_os_am_knowledge.sdk.operations.folder_ops import FolderService
from gen_os_am_knowledge.sdk.operations.user_ops import UserService

pytest_plugins = "pytest_asyncio"


@fixture(scope="module")
def create_folder():
    """Create root folder for testing."""
    storage_path = os.environ["AM_KM_LOCAL_STORAGE_PATH"]
    root_folder = "root-folder/folder_ops"
    os.makedirs(f"{storage_path}/{root_folder}", exist_ok=True)
    yield root_folder
    # shutil.rmtree(storage_path)


@pytest_asyncio.fixture(scope="module")
async def create_user():
    """Create a user for testing."""
    user_service = UserService()
    user_id = (
        await user_service.create_user(
            user_name="test_user", user_external_id="test_user_id"
        )
    ).id
    yield user_id
    await user_service.delete_user(user_id=user_id)


@fixture(scope="module")
def local_fs_connector(create_folder):
    """Create local filesystem connector."""
    yield LocalFSConnector(root_folder=create_folder)


@pytest.mark.asyncio
async def test_create_and_get_folder(local_fs_connector, create_folder, create_user):
    """Test creating, retrieving, updating, and deleting a folder."""
    folder_service = FolderService(
        fs_connector=local_fs_connector,
    )
    # Test variables
    folder_name_0 = "pytest_folder"
    folder_name = "pytest_folder_2"

    # Create a folder
    parent_created_folder = await folder_service.create_folder(
        folder_name=folder_name_0,
        user_id=_DEFAULT_UUID,
    )
    assert parent_created_folder is not None, "Folder creation failed"
    assert (
        parent_created_folder.name == folder_name_0
    ), "Folder name mismatch after creation"
    assert os.path.exists(
        f"{os.environ["AM_KM_LOCAL_STORAGE_PATH"]}/{create_folder}/{folder_name_0}"
    ), "Folder not created"
    assert (
        parent_created_folder.created_by_id == _DEFAULT_UUID
    ), "Created by ID mismatch"
    assert (
        parent_created_folder.last_modified_by_id == _DEFAULT_UUID
    ), "Last modified by ID mismatch"
    assert parent_created_folder.parent_id is None, "Parent ID mismatch after creation"
    assert (
        parent_created_folder.created_date is not None
    ), "Created at timestamp mismatch"
    assert (
        parent_created_folder.modification_date is not None
    ), "Last modified at timestamp mismatch"

    # Create child folder
    parent_id = parent_created_folder.id
    created_folder = await folder_service.create_folder(
        folder_name=folder_name, parent_id=parent_id, user_id=_DEFAULT_UUID
    )
    assert created_folder is not None, "Folder creation failed"
    assert created_folder.name == folder_name, "Folder name mismatch after creation"
    assert created_folder.parent_id == parent_id, "Parent ID mismatch after creation"
    assert os.path.exists(
        f"{os.environ["AM_KM_LOCAL_STORAGE_PATH"]}/{create_folder}/{folder_name_0}/{folder_name}"
    ), "Folder not created"
    assert created_folder.created_by_id == _DEFAULT_UUID, "Created by ID mismatch"
    assert (
        created_folder.last_modified_by_id == _DEFAULT_UUID
    ), "Last modified by ID mismatch"
    assert created_folder.created_date is not None, "Created at timestamp mismatch"
    assert (
        created_folder.modification_date is not None
    ), "Last modified at timestamp mismatch"

    # create same folder again to catch exception
    with pytest.raises(
        ValueError, match=f"Folder with name {folder_name} already exists"
    ):
        await folder_service.create_folder(
            folder_name=folder_name, parent_id=parent_id, user_id=_DEFAULT_UUID
        )

    # Create folder from path

    created_folders = await folder_service.create_folders_from_path(
        path="1/2/3", user_id=_DEFAULT_UUID
    )

    assert created_folders is not None, "Folder creation failed"
    assert len(created_folders) == 3, "Folder creation failed"
    for i in range(len(created_folders) - 1):
        assert created_folders[i].name == str(
            i + 1
        ), "Folder name mismatch after creation"
        assert (
            created_folders[i + 1].parent_id == created_folders[i].id
        ), "Parent ID mismatch after creation"
        assert (
            created_folders[i].created_by_id == _DEFAULT_UUID
        ), "Created by ID mismatch"

    # Get folder path
    folder_path = await folder_service.get_folder_path(created_folders[-1].id)
    assert folder_path == "1/2/3/", "Folder path mismatch"

    # Get folder
    retrieved_folder = await folder_service.get_folder(folder_id=created_folder.id)
    assert retrieved_folder is not None, "Folder retrieval failed"
    assert retrieved_folder.name == folder_name, "Retrieved folder name mismatch"
    assert retrieved_folder.parent_id == parent_id, "Retrieved parent ID mismatch"

    retrieved_folders = await folder_service.search_folders(
        folder_name=folder_name, parent_id=parent_id, limit=1, offset=0
    )

    assert (
        len(retrieved_folders) == 1
    ), "Folder retrieval failed or returned unexpected results"
    assert retrieved_folders == [created_folder], "Retrieved folders mismatch"

    # Update the folder
    updated_folder_name = "pytest_folder_updated"
    updated_parent_id = None
    update_result = await folder_service.update_folder(
        folder_id=created_folder.id,
        folder_name=updated_folder_name,
        parent_folder_id=updated_parent_id,
        user_id=create_user,
    )

    assert update_result.success
    assert update_result.updated_instance.name == updated_folder_name
    assert update_result.updated_instance.parent_id == updated_parent_id
    assert update_result.error is None
    assert update_result.updated_values == {
        "name": (folder_name, updated_folder_name),
        "parent_id": (parent_id, updated_parent_id),
        "last_modified_by_id": (_DEFAULT_UUID, create_user),
    }

    assert os.path.exists(
        f"{os.environ["AM_KM_LOCAL_STORAGE_PATH"]}/{create_folder}/{updated_folder_name}"
    ), "Folder not updated"
    assert not os.path.exists(
        f"{os.environ["AM_KM_LOCAL_STORAGE_PATH"]}/{create_folder}/{folder_name_0}/{folder_name}"
    ), "Folder not updated"
    assert os.path.exists(
        f"{os.environ["AM_KM_LOCAL_STORAGE_PATH"]}/{create_folder}/{folder_name_0}"
    ), "Wrong folder manipulated"

    # Test deleting the folder
    deletion_result = await folder_service.delete_folder(
        folder_id=created_folder.id, user_id=create_user
    )
    assert deletion_result.success, "Folder deletion failed"
    assert deletion_result.deleted_instance.id == update_result.updated_instance.id
    assert (
        deletion_result.deleted_instance.deleted_date is not None
    ), "Deleted date mismatch"

    folder_uuid = uuid.uuid4()
    deletion_result = await folder_service.delete_folder(
        folder_id=folder_uuid, user_id=create_user
    )

    assert not deletion_result.success, "Folder deletion succeeded unexpectedly"
    assert deletion_result.deleted_instance is None, "Deleted items not None"
    assert (
        deletion_result.error == f"Folder with id {folder_uuid} not found"
    ), "Error message mismatch"

    assert os.path.exists(
        f"{os.environ["AM_KM_LOCAL_STORAGE_PATH"]}/{create_folder}/{updated_folder_name}"
    ), "Folder not deleted softly"

    # delete other folders in db
    for folder in created_folders[::-1]:
        await folder_service.delete_folder(folder_id=folder.id, user_id=create_user)

    assert os.path.exists(
        f"{os.environ["AM_KM_LOCAL_STORAGE_PATH"]}/{create_folder}/1"
    ), "Folders not deleted softly"

    await folder_service.delete_folder(
        folder_id=parent_created_folder.id, user_id=create_user
    )
    assert os.path.exists(
        f"{os.environ["AM_KM_LOCAL_STORAGE_PATH"]}/{create_folder}/{folder_name_0}"
    ), "Folder not deleted softly"

    # Cleanup
    result = await folder_service.cleanup_deleted_folders()

    # assert len(result[0]) == 5, "Cleanup failed"
    # assert len(result[1]) == 0, "Cleanup failed"
    assert not os.path.exists(
        f"{os.environ["AM_KM_LOCAL_STORAGE_PATH"]}/{create_folder}/1"
    ), "Folder not cleaned up"

    assert not os.path.exists(
        f"{os.environ["AM_KM_LOCAL_STORAGE_PATH"]}/{create_folder}/{folder_name_0}"
    ), "Folder not cleaned up"

    assert not os.path.exists(
        f"{os.environ["AM_KM_LOCAL_STORAGE_PATH"]}/{create_folder}/{updated_folder_name}"
    ), "Folder not cleaned up"
