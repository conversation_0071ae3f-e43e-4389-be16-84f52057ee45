"""Module for the main API."""

import uvicorn
from fastapi import APIRout<PERSON>, Depends, FastAPI, Path
from fastapi.middleware.cors import CORSMiddleware

from gen_os_am_knowledge.api import (
    DocumentAPI,
    FolderAPI,
    HealthAPI,
    IngestionAPI,
    IngestionRunAPI,
    SourceAPI,
    TagAPI,
    UserAPI,
)
from gen_os_am_knowledge.config.logger_config import get_api_logger
from gen_os_am_knowledge.config.settings import ApiSettings
from gen_os_am_knowledge.sdk.filesystem_connectors import (
    AzureDataLakeStorageFSConnector,
    LocalFSConnector,
)
from gen_os_am_knowledge.utils.ctt.ctt_utils.logging.fastapi_log_middleware import (
    APILoggingMiddleware,
)

settings = ApiSettings.get_settings()

if settings.ENVIRONMENT == "LOCAL":
    storage_connector = LocalFSConnector(root_folder="test")
else:
    storage_connector = AzureDataLakeStorageFSConnector()


def agent_id_dependency(agent_id: str = Path(...)):
    """Extract agent_id from the path.

    This allows us to add agent_id to the router prefix without
    adding it to every endpoint signature.
    """
    pass


api_prefix = "/agents/{agent_id}/km/v1"

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

main_router = APIRouter(dependencies=[Depends(agent_id_dependency)], prefix=api_prefix)

app.add_middleware(APILoggingMiddleware, logger=get_api_logger(), settings=ApiSettings)

tag_api_router = TagAPI(
    logger=get_api_logger(),
).get_router()
document_api_router = DocumentAPI(
    storage_connector=storage_connector,
    logger=get_api_logger(),
).get_router()
source_api_router = SourceAPI(logger=get_api_logger()).get_router()
folder_api_router = FolderAPI(
    fs_connector=storage_connector, logger=get_api_logger()
).get_router()
user_api_router = UserAPI(logger=get_api_logger()).get_router()
ingestion_api_router = IngestionAPI(logger=get_api_logger()).get_router()
health_router = HealthAPI(storage_connector=storage_connector).get_router()
ingestion_run_router = IngestionRunAPI(
    logger=get_api_logger(), storage_connector=storage_connector
).get_router()


main_router.include_router(tag_api_router)
main_router.include_router(document_api_router)
main_router.include_router(source_api_router)
main_router.include_router(folder_api_router)
main_router.include_router(user_api_router)
main_router.include_router(ingestion_api_router)
main_router.include_router(health_router)
main_router.include_router(ingestion_run_router)

app.include_router(main_router)

if __name__ == "__main__":
    config = uvicorn.Config(
        app=app,
        host=settings.API_HOST,
        port=settings.API_PORT,
        reload=settings.API_RELOAD,
        workers=settings.API_WORKERS,
    )
    server = uvicorn.Server(config)
    server.run()
