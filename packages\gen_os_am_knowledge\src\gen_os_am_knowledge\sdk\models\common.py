"""Module that contains common constants that are used to define the models."""

import datetime
import uuid
from typing import Any

from gen_os_am_knowledge.sdk.models import (
    Document,
    Folder,
    Ingestion,
    Source,
    Tag,
    User,
)
from gen_os_am_knowledge.sdk.models.default_uuid import _DEFAULT_UUID

# _DEFAULT_UUID = uuid.UUID(bytes=b"0" * 16, version=4)

DEFAULT_USER: User = User(id=_DEFAULT_UUID, name="default", external_id="default")

DEFAULT_SOURCE: Source = Source(
    id=_DEFAULT_UUID,
    name="default",
    type="internal",
    meta=None,
)

DEFAULT_INGESTION: Ingestion = Ingestion(
    id=_DEFAULT_UUID,
    source_id=_DEFAULT_UUID,
    description="the default ingestion",
    meta=None,
    status=Ingestion.ING_STATUS_DONE,
    folder_id=_DEFAULT_UUID,
)

DEFAULT_FOLDER: Folder = Folder(
    id=_DEFAULT_UUID,
    name="root",
    parent_id=None,
    created_by_id=_DEFAULT_UUID,
    last_modified_by_id=_DEFAULT_UUID,
)


def slave_document(
    filename: str,
    size: int,
    type_: str,
    hash_: int,
    folder_id: uuid.UUID,
    original_path: str | None = None,
    tags: list[Tag] | None = None,
    folder: Folder | None = None,  # type: ignore
    created_date: datetime.datetime | None = None,
    ingestion: Ingestion | None = None,
    ingestion_id: uuid.UUID | None = None,
    document_status: Document._DOC_STATUS_TYPE | None = None,
    meta: dict[str, Any] | None = None,
    modification_date: datetime.datetime | None = None,
    deleted_date: datetime.datetime | None = None,
    id_: uuid.UUID | None = None,
    draft: bool = False,
) -> Document:
    """Create a SlaveDocument: A document without user.

    Args:
        filename (str): Filename On Our Agent Manager File Storage.
        original_path (str): Filepath On The Original Source.
        size (int): The Size of the Document file.
        type_ (str): The type of this file.
        folder_id (uuid.UUID): The Folder Id, has to be passed.
        tags (list[Tag]|None): The list of Tags, a N tags to N docs.
        folder (Folder|None): The folder for this doc,
            Either pass this or the folder_id.
        created_date (datetime.datetime | None ): The Date The Document Was Created On.
        ingestion_id (uuid.UUID | None): The id for the associate ingestion.
            Either pass this or the ingestion.
        ingestion (Ingestion|None): The ingestion associated with this doc.
            Either pass this or the ingestion_id.
        document_status (Document._DOC_STATUS_TYPE | None):
            The Document Ingestion Status.
        meta (dict[str, Any] | None ): Json Metadata For This Document.
        modification_date (datetime.datetime | None): The Data When This
            Document Was Modified.
        deleted_date (datetime.datetime | None): The Deletion Date For This File.
        id_ (uuid.UUID | None): The Internal Database Id. Autogenerated if not passed,
            which is the recommended way.
        hash_ (int | None): The xxhash64 of the document file and its location.
        draft (bool): Whether the document is in draft mode or not.

    Returns:
        Document: A Slave Document with DEFAULT_USER as created_by and last_modified_by

    """
    doc_args = {
        "ingestion": ingestion,
        "ingestion_id": ingestion_id,
        "document_status": document_status,
        # "created_by": DEFAULT_USER,
        "created_by_id": DEFAULT_USER.id,
        # "last_modified_by": DEFAULT_USER,
        "last_modified_by_id": DEFAULT_USER.id,
        "id": id_,
        "filename": filename,
        "folder_id": folder_id,
        "original_path": original_path,
        "created_date": created_date,
        "size": size,
        "type": type_,
        "kind": "slave",
        "meta": meta,
        "modification_date": modification_date,
        "deleted_date": deleted_date,
        "tags": tags,
        "folder": folder,
        "hash": hash_,
        "draft": draft,
    }
    # accept only one or the other but not both (XOR)
    if not ((doc_args["ingestion"] is None) ^ (doc_args["ingestion_id"] is None)):
        raise ValueError("Needs one of ingestion or ingestion_id, and not both")

    # remove None entries
    doc_args = {k: v for k, v in doc_args.items() if v}

    return Document(**doc_args)


def master_document(
    filename: str,
    size: int,
    type_: str,
    hash_: int,
    folder_id: uuid.UUID = DEFAULT_FOLDER.id,
    original_path: str | None = None,
    tags: list[Tag] | None = None,
    folder: Folder | None = None,  # type: ignore
    created_date: datetime.datetime | None = None,
    created_by: User | None = None,
    created_by_id: uuid.UUID | None = None,
    meta: dict[str, Any] | None = None,
    modification_date: datetime.datetime | None = None,
    last_modified_by: User | None = None,
    last_modified_by_id: uuid.UUID | None = None,
    deleted_date: datetime.datetime | None = None,
    id_: uuid.UUID | None = None,
    document_status: Document._DOC_STATUS_TYPE | None = None,
    draft: bool = False,
) -> Document:
    """Create a master document without ingestion for external data sources.

    Args:
        filename (str): Filename On Our Agent Manager File Storage.
        original_path (str): Filepath On The Original Source.
        size (int): The Size of the Document file.
        type_ (str): The type of this file.
        tags (list[Tag]|None): The list of Tags, a N tags to N docs.
        folder_id (uuid.UUID ): The Folder Id. Default is the root folder.
        folder (Folder|None): The folder for this doc,
            Either pass this or the folder_id.
        created_date (datetime.datetime | None ): The Date The Document Was Created On.
        created_by_id (uuid.UUID | None): The Id Of User That Created The Document.
            Either pass this or created_by.
        created_by (User|None): The user that created this doc.
            Either pass this or created_by_id.
        meta (dict[str, Any] | None ): Json Metadata For This Document.
        modification_date (datetime.datetime | None): The Data When
            This Document Was Modified.
        last_modified_by (User|None): The user that last modified this doc.
            Either pass this or last_modified_by_id.
        last_modified_by_id (uuid.UUID | None): The Id Of The User That Modified
            The Document. Either pass this or last_modified_by.
        deleted_date (datetime.datetime | None): The Deletion Date For This File.
        id_ (uuid.UUID | None): The Internal Database Id. Autogenerated if not
            passed, which is the recommended way.
        hash_ (int): The xxhash64 of the document file and its location.
        document_status:
            (Literal["Pending","Storage","Pending VectorDB","Syncing VectorDB",
            "Synced","Error"] | None): The Document Ingestion Status.
        draft (bool): Whether the document is in draft mode or not.

    Returns:
        Document: A Master Document with DEFAULT_INGESTION as ingestion.

    """
    doc_args = {
        # "ingestion": DEFAULT_INGESTION,
        "ingestion_id": DEFAULT_INGESTION.id,
        "ingestion_status": document_status,
        "filename": filename,
        "original_path": original_path,
        "size": size,
        "type": type_,
        "kind": "master",
        "tags": tags,
        "folder_id": folder_id,
        "folder": folder,
        "created_date": created_date,
        "created_by": created_by,
        "created_by_id": created_by_id,
        "meta": meta,
        "modification_date": modification_date,
        "last_modified_by": last_modified_by,
        "last_modified_by_id": last_modified_by_id,
        "deleted_date": deleted_date,
        "id": id_,
        "hash": hash_,
        "draft": draft,
    }
    # accept only one or the other but not both (XOR)
    if not ((doc_args["created_by"] is None) ^ (doc_args["created_by_id"] is None)):
        raise ValueError("Needs one of created_by or created_by_id, and not both")

    if not (
        (doc_args["last_modified_by"] is None)
        ^ (doc_args["last_modified_by_id"] is None)
    ):
        raise ValueError(
            "Needs one of last_modified_by or last_modified_by_id, and not both"
        )

    # remove None entries
    doc_args = {k: v for k, v in doc_args.items() if v}

    return Document(**doc_args)
