"""Functions to extract and process the HTML content from the schematic pages."""

from bs4 import Tag
from ctt_am_ingestions.website_ingestion.web_scraping.results import Page, Section
from ctt_am_ingestions.website_ingestion.web_scraping.utils import (
    HEADER_PATTERN,
    add_text,
    extract_list_items,
    get_headline_from_section_header,
    get_main_list,
    prepare_text,
)


def should_create_new_headline(
    elem: Tag, headline: str | None, section_index: int, element_index: int
) -> bool:
    """Determine whether a new headline should be created."""
    if section_index == 1:
        return False
    if headline:
        return False
    if element_index not in [1, 3]:
        return False
    if elem.find_parent("div", class_="card-container"):
        return False
    return True


def should_ignore_child(child: Tag):
    """Determine whether a child should be ignored."""
    text = child.get_text(strip=True)
    if not text:
        return True
    card = child.find("div", class_="card-container")
    if isinstance(card, Tag):
        card_children = list(card.children)
        second_card_child = card_children[1] if len(card_children) > 0 else None
        if isinstance(second_card_child, Tag) and second_card_child.name == "a":
            return True
    keywords_to_ignore = ["sidebar", "banner", "logo", "video"]
    if any(
        keyword in cls
        for cls in child.get("class", [])
        for keyword in keywords_to_ignore
    ):
        return True
    return False


def update_sections(
    sections: list[Section], headline: str | None, info: str
) -> list[Section]:
    """Add a new section or update the last one with additional information."""
    if headline:
        sections.append(Section(headline=headline, info=info))
    else:
        sections[-1].info = add_text(sections[-1].info, info)
    return sections


def extract_schematic_headline_and_info(
    section: Tag, section_index: int, headline: str | None
) -> tuple[str | None, str]:
    """Extract the headline and the info from section."""
    info_list = []
    for element_index, elem in enumerate(section.descendants):
        text = elem.get_text(strip=True)
        if not isinstance(elem, Tag) or not elem.name or not text:
            continue

        if HEADER_PATTERN.fullmatch(elem.name):
            text = prepare_text(elem)
            if should_create_new_headline(elem, headline, section_index, element_index):
                headline = text
            else:
                info_list.append(text)
        elif elem.name == "p":
            info_list.append(prepare_text(elem))
        elif elem.name in ["ol", "ul"]:
            info_list.append(extract_list_items(elem))

    info = "\n".join(info_list)
    return headline, info


def extract_schematic_element_data(
    summary: str,
    sections: list[Section],
    raw_section: Tag,
    headline: str | None,
    section_index: int,
) -> tuple[str, list[Section]]:
    """Extract the content of a schematic page."""
    headline, info = extract_schematic_headline_and_info(
        raw_section, section_index, headline
    )

    if not headline and len(sections) == 0:
        summary = add_text(summary, info)
    else:
        sections = update_sections(sections, headline, info)
    return summary, sections


def extract_faqs_section_content(
    summary: str, sections: list[Section], raw_section: Tag, section_index: int
) -> tuple[str, list[Section]]:
    """Extract the content of a FAQS section."""
    for description in raw_section.find_all("dd"):
        if not isinstance(description, Tag):
            continue
        anchor = description.find("a")
        headline = anchor.get_text(strip=True) if anchor else None
        summary, sections = extract_schematic_element_data(
            summary, sections, description, headline, section_index
        )
    return summary, sections


def extract_list_section_content(
    summary: str, sections: list[Section], raw_section: Tag
) -> tuple[str, list[Section]]:
    """Extract the content of a list section."""
    main_list = get_main_list(raw_section)
    notes = raw_section.find("p", class_=lambda c: c and "notes" in c)
    headline = get_headline_from_section_header(raw_section)
    info = extract_list_items(main_list) if isinstance(main_list, Tag) else ""
    if isinstance(notes, Tag):
        info = add_text(info, prepare_text(notes))
    if not headline and len(sections) == 0:
        summary = add_text(summary, info)
    else:
        sections = update_sections(sections, headline, info)
    return summary, sections


def extract_card_section_content(
    summary: str, sections: list[Section], raw_section: Tag, section_index: int
) -> tuple[str, list[Section]]:
    """Extract the content of a card section."""
    headline, info = get_headline_from_section_header(raw_section), ""
    if headline:
        sections.append(Section(headline=headline, info=info))
    for card in raw_section.find_all("div", class_="card-container"):
        if not isinstance(card, Tag):
            continue

        text = card.get_text(strip=True)
        card_content = card.find("div", class_="card-content")
        if not text or not isinstance(card_content, Tag):
            continue

        check_list = card_content.find("ul", class_="list-check-large")
        if (
            headline
            or isinstance(check_list, Tag)
            and len(check_list.get_text(strip=True)) > 0
        ):
            summary, sections = extract_schematic_element_data(
                summary, sections, card_content, None, section_index
            )
        else:
            pre_headline_tag = card_content.find("h6")
            pre_headline = (
                prepare_text(pre_headline_tag)
                if isinstance(pre_headline_tag, Tag)
                else ""
            )
            card_title_tag = card_content.find(HEADER_PATTERN, class_="card-title")
            card_title = (
                prepare_text(card_title_tag) if isinstance(card_title_tag, Tag) else ""
            )
            headline = f"[{pre_headline}] {card_title}" if pre_headline else card_title
            summary, sections = extract_schematic_element_data(
                summary, sections, card_content, headline, section_index
            )
            if pre_headline:
                sections[-1].info = sections[-1].info.split("\n", 1)[1]
            sections[-1].info = sections[-1].info.split("\n", 1)[1]
            headline = None

    return summary, sections


def extract_text_section_content(
    summary: str, sections: list[Section], raw_section: Tag, section_index: int
) -> tuple[str, list[Section]]:
    """Extract the content of a text section."""
    for division in raw_section.find_all("div", class_="row"):
        text = division.get_text(strip=True)
        if (
            not isinstance(division, Tag)
            or not division.find_parent(["section", "li"])
            or not text
        ):
            continue
        headline = None
        summary, sections = extract_schematic_element_data(
            summary, sections, division, headline, section_index
        )
    return summary, sections


def extract_schematic_content(page_content: Tag) -> tuple[str, list[Section]]:
    """Extract the content of a schematic page."""
    summary = ""
    sections: list[Section] = []
    for section_index, child in enumerate(page_content.children):
        if not isinstance(child, Tag) or should_ignore_child(child):
            continue

        classes = child.get("class", [])
        if not isinstance(classes, list):
            continue
        if "product-faqs" in classes:
            summary, sections = extract_faqs_section_content(
                summary, sections, child, section_index
            )
        elif any(
            "product-text-section" in cls or "product-benefits-section" in cls
            for cls in classes
        ):
            summary, sections = extract_text_section_content(
                summary, sections, child, section_index
            )
        elif any("product" in cls for cls in classes) or "number-stats" in classes:
            summary, sections = extract_list_section_content(summary, sections, child)
        elif any("card" in cls for cls in classes):
            summary, sections = extract_card_section_content(
                summary, sections, child, section_index
            )

    return summary, sections


def extract_banner_text(banner: Tag) -> str:
    """Extract the text from banner slides."""
    slides = []
    for slide in banner.find_all("div", class_="slide-caption-wrap"):
        if not isinstance(slide, Tag):
            continue
        pre_headline_tag = slide.find("h6")
        pre_headline = (
            prepare_text(pre_headline_tag) if isinstance(pre_headline_tag, Tag) else ""
        )
        caption_tag = slide.find("h1", class_="caption")
        caption = prepare_text(caption_tag) if isinstance(caption_tag, Tag) else ""
        slides.append(f"[{pre_headline}] {caption}" if pre_headline_tag else caption)
    return "\n".join(slides) if slides else ""


def process_schematic_page(page: Page, banner: Tag, page_content: Tag) -> Page | None:
    """Process a schematic page and extract relevant data."""
    banner_text = extract_banner_text(banner)
    summary, sections = extract_schematic_content(page_content)
    if not summary and not sections:
        return None
    page.summary = add_text(banner_text, summary)
    page.sections = sections
    return page
