x-environment: &default-environment
  POSTGRES_USER: ${POSTGRES_USER:-someuser}
  POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-somepass}
  POSTGRES_PORT: ${POSTGRES_PORT:-5432}
  POSTGRES_DB: ${POSTGRES_DB:-km_db}

volumes:
  postgres_data:
    driver: local
    name: ${PROJECT_NAME:-km}_volume

services:
  postgres:
    container_name: ${PROJECT_NAME:-km}_container
    image: km_postgres
    build:
      context: ./postgres
      dockerfile: Dockerfile
      network: host
    environment:
      <<: *default-environment
    volumes:
      - postgres_data:/var/lib/postgresql/data/
      - ./postgres/create_test_databases.sql:/docker-entrypoint-initdb.d/create_test_databases.sql
      - ./postgres/activate_extensions.sql:/docker-entrypoint-initdb.d/activate_extensions.sql
      - ./postgres/postgresql.conf:/etc/postgresql.conf
    ports:
      - ${POSTGRES_PORT:-5432}:5432
    command: -c config_file=/etc/postgresql.conf
