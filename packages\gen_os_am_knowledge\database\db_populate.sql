-- Clear existing data to make the script idempotent
-- Use CASCADE to handle foreign key constraints
TRUNCATE public.document_tags CASCADE;
TRUNCATE public.document CASCADE;
TRUNCATE public.ingestion CASCADE;
TRUNCATE public.source CASCADE;
TRUNCATE public.folder CASCADE;
TRUNCATE public.tag CASCADE;
TRUNCATE public.user CASCADE;

-- Start fresh insertions
BEGIN;

INSERT INTO public.source (id, name, type, meta)
VALUES
  ('11111111-1111-1111-1111-111111111111', 'Website', 'web_scraper', '{"info": "sample"}');

INSERT INTO public."user" (id, name, external_id)
VALUES
  ('33333333-3333-3333-3333-333333333333', 'Alice', 'alice_ext'),
  ('44444444-4444-4444-4444-444444444444', 'Bob', 'bob_ext');

INSERT INTO public.folder (id, name, parent_id)
VALUES
  ('55555555-5555-5555-5555-555555555555', 'Folder', '30303030-3030-4030-b030-303030303030'),
  ('55555555-5555-5555-5555-555555555556', 'Subfolder', '55555555-5555-5555-5555-555555555555'),
  ('55555555-5555-5555-5555-555555555557', 'Ingestion Folder', NULL),
  ('66666666-6666-6666-6666-666666666666', 'Another Folder', '30303030-3030-4030-b030-303030303030');

INSERT INTO public.ingestion (
    id, description, status, source_id, last_run_date, last_success_date, meta, error, folder_id
)
VALUES (
    '22222222-2222-2222-2222-222222222222',
    'Sample Ingestion',
    'Done',
    '11111111-1111-1111-1111-111111111111',
    NOW(),
    NOW(),
    '{
        "base_url": "http://localhost:3000",
        "urls": [
            "http://localhost:3000",
            "http://localhost:3000/5",
            "http://localhost:3000/56",
            "http://localhost:3000/57",
            "http://localhost:3000/58"
        ],
        "schedule": "0 8 * * *"
    }',
    NULL,
    '55555555-5555-5555-5555-555555555557'
);


INSERT INTO public.tag (id, tag)
VALUES
  ('77777777-7777-7777-7777-777777777777', 'Important'),
  ('88888888-8888-8888-8888-888888888888', 'Confidential');

INSERT INTO public.document (
    id, filename, folder_id, original_path, created_date, created_by_id, size, type, kind, meta,
    last_modified_by_id, modification_date, ingestion_id, hash, document_status, deleted_date, draft
)
VALUES
  (
    '99999999-9999-9999-9999-999999999999',
    'example.pdf',
    '30303030-3030-4030-b030-303030303030',
    'http://localhost:3000/5',
    NOW(),
    '30303030-3030-4030-b030-303030303030',
    123456,
    'pdf',
    'slave',
    '{"pages": 10}',
    '30303030-3030-4030-b030-303030303030',
    NOW(),
    '22222222-2222-2222-2222-222222222222',
    1234567890,
    'Synced',
    NULL,
    FALSE
  ),
  (
    'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
    'second_example.docx',
    '55555555-5555-5555-5555-555555555555',
    'http://localhost:3000/56',
    NOW(),
    '30303030-3030-4030-b030-303030303030',
    78910,
    'docx',
    'slave',
    '{"words": 250}',
    '30303030-3030-4030-b030-303030303030',
    NOW(),
    '22222222-2222-2222-2222-222222222222',
    987654321,
    'Syncing Storage',
    NULL,
    FALSE
  ),
  (
    'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb',
    'third_example.txt',
    '55555555-5555-5555-5555-555555555557',
    'http://localhost:3000/57',
    NOW(),
    '30303030-3030-4030-b030-303030303030',
    65432,
    'txt',
    'slave',
    '{"lines": 50}',
    '30303030-3030-4030-b030-303030303030',
    NOW(),
    '22222222-2222-2222-2222-222222222222',
    1122334455,
    'Syncing VectorDB',
    NULL,
    FALSE
  ),
  (
    'cccccccc-cccc-cccc-cccc-cccccccccccc',
    'fourth_example.xlsx',
    '55555555-5555-5555-5555-555555555557',
    'http://localhost:3000/58',
    NOW(),
    '30303030-3030-4030-b030-303030303030',
    98765,
    'xlsx',
    'slave',
    NULL,
    '30303030-3030-4030-b030-303030303030',
    NOW(),
    '22222222-2222-2222-2222-222222222222',
    9988776655,
    'Error',
    NULL,
    TRUE
  );

INSERT INTO public.document_tags (document_id, tag_id)
VALUES
  ('99999999-9999-9999-9999-999999999999', '77777777-7777-7777-7777-777777777777'),
  ('99999999-9999-9999-9999-999999999999', '88888888-8888-8888-8888-888888888888'),
  ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '77777777-7777-7777-7777-777777777777'),
  ('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', '77777777-7777-7777-7777-777777777777'),
  ('cccccccc-cccc-cccc-cccc-cccccccccccc', '88888888-8888-8888-8888-888888888888');

COMMIT;
