"""Common schemas shared across multiple routers."""

from typing import Any, Literal

from pydantic import BaseModel, Field, field_validator


class PaginationFilters(BaseModel):
    """Base pagination and sorting filters used by multiple endpoints."""

    sort_field: str = "created_at"
    sort_order: str = "desc"
    offset: int = Field(0, ge=0)
    limit: int = Field(20, ge=1, le=100)

    @field_validator("sort_order")
    @classmethod
    def _validate_sort_order(cls, v: str) -> str:
        if v.lower() not in {"asc", "desc"}:
            raise ValueError("sort_order must be either 'asc' or 'desc'")
        return v.lower()


class PaginationDetails(BaseModel):
    """Pagination metadata returned in paginated responses."""

    offset: int = Field(ge=0)
    limit: int = Field(ge=1, le=100)
    total_items: int = Field(ge=0) 