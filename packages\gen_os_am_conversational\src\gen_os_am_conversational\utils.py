"""Utility functions for managing agent conversations and conversational tracking."""

import json
import logging
import uuid
from datetime import datetime
from typing import Any, Literal

from sqlalchemy import asc, case, desc, func, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.types import String

from gen_os_am_conversational.api.schemas import PaginationDetails, SpanData
from gen_os_am_conversational.models.models import Conversations, MessageTurn

logger = logging.getLogger(__name__)


def any_value_to_python(value: Any) -> Any:
    """Convert OpenTelemetry AnyValue protobuf to Python value.

    Args:
        value: OpenTelemetry AnyValue protobuf object

    Returns:
        Python value extracted from the protobuf

    """
    kind = value.WhichOneof("value")
    return getattr(value, kind)


def otel_trace_id_to_uuid(trace_id_bytes: bytes) -> uuid.UUID:
    """Convert OpenTelemetry trace ID (16 bytes) to UUID.

    OTel trace IDs are 16 bytes (128-bit) which is the same size as a UUID.
    We can convert them directly by formatting the hex string properly.

    Args:
        trace_id_bytes: 16-byte trace ID from OpenTelemetry

    Returns:
        UUID representation of the trace ID

    """
    hex_string = trace_id_bytes.hex()
    # Insert hyphens to create proper UUID format: xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
    formatted_hex = (
        f"{hex_string[:8]}-{hex_string[8:12]}-{hex_string[12:16]}-"
        f"{hex_string[16:20]}-{hex_string[20:32]}"
    )
    return uuid.UUID(formatted_hex)


def otel_span_id_to_uuid(span_id_bytes: bytes) -> uuid.UUID:
    """Convert OpenTelemetry span ID (8 bytes) to UUID.

    OTel span IDs are 8 bytes (64-bit), so we need to pad them to 16 bytes for UUID.
    We'll pad with zeros at the beginning.

    Args:
        span_id_bytes: 8-byte span ID from OpenTelemetry

    Returns:
        UUID representation of the span ID (padded with zeros)

    """
    hex_string = span_id_bytes.hex()
    # Pad to 32 characters (16 bytes) and format as UUID
    padded_hex = hex_string.zfill(32)
    formatted_hex = (
        f"{padded_hex[:8]}-{padded_hex[8:12]}-{padded_hex[12:16]}-"
        f"{padded_hex[16:20]}-{padded_hex[20:32]}"
    )
    return uuid.UUID(formatted_hex)


async def get_conversation_summaries_paginated(
    db: AsyncSession,
    offset: int,
    limit: int,
    sort_field: str | None = None,
    sort_order: str | None = None,
    agent_id: str | None = None,
    search: str = "",
    search_column: Literal["id", "custom_fields", "agent_id"] | None = None,
) -> tuple[list[dict], PaginationDetails]:
    """Get paginated conversation summaries using efficient JOINs and GROUP BY."""
    # Create a subquery for message aggregates using GROUP BY (more efficient)
    message_stats_subq = (
        select(
            MessageTurn.conversation_id,
            func.count(MessageTurn.id).label("msg_count"),
            func.max(MessageTurn.timestamp).label("last_msg_at"),
        )
        .group_by(MessageTurn.conversation_id)
        .subquery()
    )

    # Main query with LEFT JOIN to include conversations with no messages
    stmt = select(
        Conversations.id,
        Conversations.agent_id,
        Conversations.start_time,
        Conversations.end_time,
        Conversations.custom_fields,
        Conversations.created_at,
        Conversations.updated_at,
        func.coalesce(message_stats_subq.c.msg_count, 0).label("message_count"),
        message_stats_subq.c.last_msg_at.label("last_message_at"),
    ).outerjoin(message_stats_subq, Conversations.id == message_stats_subq.c.conversation_id)

    # Apply agent filter if provided
    if agent_id:
        stmt = stmt.where(Conversations.agent_id == agent_id)

    # SEARCH IMPLEMENTATION - Following CM pattern exactly
    if search:
        if search_column:
            # Targeted search - exactly like CM
            if search_column == "id":
                stmt = stmt.where(Conversations.id.cast(String).ilike(f"%{search}%"))
            elif search_column == "custom_fields":
                stmt = stmt.where(Conversations.custom_fields.cast(String).ilike(f"%{search}%"))
            elif search_column == "agent_id":
                stmt = stmt.where(Conversations.agent_id.cast(String).ilike(f"%{search}%"))
        else:
            # Broad search - exactly like CM
            stmt = stmt.where(
                or_(
                    Conversations.id.cast(String).ilike(f"%{search}%"),
                    Conversations.custom_fields.cast(String).ilike(f"%{search}%"),
                    Conversations.agent_id.ilike(f"%{search}%"),
                )
            )

    # For sorting, map field names to the appropriate column expressions
    sort_column_mapping = {
        "id": Conversations.id,
        "agent_id": Conversations.agent_id,
        "start_time": Conversations.start_time,
        "last_message_at": message_stats_subq.c.last_msg_at,
        "message_count": func.coalesce(message_stats_subq.c.msg_count, 0),
        "created_at": Conversations.created_at,
        "updated_at": Conversations.updated_at,
    }

    sort_column_expr = sort_column_mapping.get(sort_field, message_stats_subq.c.last_msg_at)
    order_modifier = asc if sort_order and sort_order.lower() == "asc" else desc

    # Count query for pagination - NO DUPLICATION like CM
    count_stmt = select(func.count(Conversations.id))
    if agent_id:
        count_stmt = count_stmt.where(Conversations.agent_id == agent_id)

    # Apply same search logic to count - following CM pattern
    if search:
        if search_column:
            if search_column == "id":
                count_stmt = count_stmt.where(Conversations.id.cast(String).ilike(f"%{search}%"))
            elif search_column == "custom_fields":
                count_stmt = count_stmt.where(
                    Conversations.custom_fields.cast(String).ilike(f"%{search}%")
                )
            elif search_column == "agent_id":
                count_stmt = count_stmt.where(
                    Conversations.agent_id.cast(String).ilike(f"%{search}%")
                )
        else:
            count_stmt = count_stmt.where(
                or_(
                    Conversations.id.cast(String).ilike(f"%{search}%"),
                    Conversations.custom_fields.cast(String).ilike(f"%{search}%"),
                    Conversations.agent_id.cast(String).ilike(f"%{search}%"),
                )
            )

    # Apply sorting and pagination
    paginated_stmt = stmt.order_by(order_modifier(sort_column_expr)).offset(offset).limit(limit)

    # Execute queries
    result = await db.execute(paginated_stmt)
    conversations_raw = result.all()

    total_result = await db.execute(count_stmt)
    total_items = total_result.scalar_one()

    # Convert raw results to dictionaries
    conversations_dicts = []
    for row in conversations_raw:
        conversations_dicts.append(
            {
                "id": row.id,
                "agent_id": row.agent_id,
                "start_time": row.start_time,
                "end_time": row.end_time,
                "custom_fields": row.custom_fields,
                "created_at": row.created_at,
                "updated_at": row.updated_at,
                "message_count": row.message_count or 0,
                "last_message_at": row.last_message_at,
            }
        )

    pagination_details = PaginationDetails(
        offset=offset,
        limit=limit,
        total_items=total_items,
    )

    return conversations_dicts, pagination_details


async def get_conversation_metadata(
    db: AsyncSession,
    conversation_id: uuid.UUID,
    agent_id: str,
) -> dict | None:
    """Get conversation metadata using efficient JOIN for message count.

    Args:
        db: Database session
        conversation_id: ID of the conversation to retrieve
        agent_id: ID of the agent (for security filtering)

    Returns:
        Dictionary with conversation metadata if found, None otherwise

    Raises:
        SQLAlchemyError: Database operation failed

    """
    # Create a subquery for message count using GROUP BY
    message_count_subq = (
        select(MessageTurn.conversation_id, func.count(MessageTurn.id).label("msg_count"))
        .where(MessageTurn.conversation_id == conversation_id)
        .group_by(MessageTurn.conversation_id)
        .subquery()
    )

    # Main query with LEFT JOIN to handle conversations with no messages
    stmt = (
        select(
            Conversations.id,
            Conversations.agent_id,
            Conversations.start_time,
            Conversations.end_time,
            Conversations.custom_fields,
            Conversations.created_at,
            Conversations.updated_at,
            func.coalesce(message_count_subq.c.msg_count, 0).label("message_count"),
        )
        .outerjoin(message_count_subq, Conversations.id == message_count_subq.c.conversation_id)
        .where(Conversations.id == conversation_id, Conversations.agent_id == agent_id)
    )

    result = await db.execute(stmt)
    row = result.first()

    if not row:
        return None

    return {
        "id": row.id,
        "agent_id": row.agent_id,
        "start_time": row.start_time,
        "end_time": row.end_time,
        "custom_fields": row.custom_fields,
        "created_at": row.created_at,
        "updated_at": row.updated_at,
        "message_count": row.message_count or 0,
    }


async def list_message_turns_paginated(
    db: AsyncSession,
    conversation_id: uuid.UUID,
    offset: int,
    limit: int,
    agent_id: str,
) -> tuple[list[MessageTurn], int]:
    """Get paginated message turns with proper ordering."""
    # Verify conversation exists and belongs to agent
    conversation_check_stmt = select(Conversations.id).where(
        Conversations.id == conversation_id, Conversations.agent_id == agent_id
    )

    conversation_check = await db.execute(conversation_check_stmt)
    if not conversation_check.scalar_one_or_none():
        logger.warning(f"Conversation {conversation_id} not found for agent {agent_id}")
        return [], 0

    # Get message count
    count_stmt = select(func.count(MessageTurn.id)).where(
        MessageTurn.conversation_id == conversation_id
    )
    count_result = await db.execute(count_stmt)
    total_items = count_result.scalar_one()

    if total_items == 0:
        return [], 0

    # Query for paginated message turns with proper ordering
    messages_stmt = (
        select(MessageTurn)
        .where(MessageTurn.conversation_id == conversation_id)
        .order_by(
            asc(MessageTurn.timestamp),
            case(
                (MessageTurn.role == "user", 0),
                (MessageTurn.role == "agent", 1),
                else_=2,
            ),
        )
        .offset(offset)
        .limit(limit)
    )

    result = await db.execute(messages_stmt)
    message_turns = result.scalars().all()

    return message_turns, total_items


def load_span_data(span_data) -> SpanData:
    """Convert OpenTelemetry span protobuf data to validated SpanData model.

    Args:
        span_data: OpenTelemetry span protobuf object

    Returns:
        SpanData: Validated Pydantic model with cleaned span data

    Raises:
        ValueError: If span data conversion fails
        Exception: If UUID conversion fails

    """
    try:
        # Convert OTel IDs to UUID format properly
        span_id = otel_span_id_to_uuid(span_data.span_id)
        trace_id = otel_trace_id_to_uuid(span_data.trace_id)
        parent_span_id = (
            otel_span_id_to_uuid(span_data.parent_span_id) if span_data.parent_span_id else None
        )

        # Convert attributes and events
        attributes = {attr.key: any_value_to_python(attr.value) for attr in span_data.attributes}

        events = {
            event.name: {attr.key: any_value_to_python(attr.value) for attr in event.attributes}
            for event in span_data.events
        }

        # Convert timestamps
        start_time = datetime.fromtimestamp(span_data.start_time_unix_nano / 1e9)
        end_time = datetime.fromtimestamp(span_data.end_time_unix_nano / 1e9)

        # Create and validate the Pydantic model
        return SpanData(
            span_id=span_id,
            trace_id=trace_id,
            parent_span_id=parent_span_id,
            start_time=start_time,
            end_time=end_time,
            attributes=attributes,
            events=events,
        )
    except Exception as e:
        logger.error(f"Error converting OTel span data to SpanData model: {e}")
        raise ValueError(f"Failed to process span data: {e}") from e


async def process_conversation_span(db: AsyncSession, span_clean: SpanData, span_name: str) -> None:
    """Process conversation spans - no manual field updates needed."""
    # Only process spans that contain conversation data
    if not span_clean.attributes.get("gen_ai.conversation.id"):
        return

    # Extract conversation-related attributes
    try:
        conversation_uuid = uuid.UUID(span_clean.attributes["gen_ai.conversation.id"])
    except (ValueError, TypeError, KeyError) as e:
        logger.error(f"Invalid ID: {span_clean.attributes.get('gen_ai.conversation.id')} - {e}")
        return

    # Extract and validate agent_id
    agent_id = span_clean.attributes.get("gen_ai.agent.id")
    if not agent_id:
        logger.warning(f"Skipping conversation {conversation_uuid} - no agent_id")
        return

    conversation_exists_stmt = select(Conversations).where(Conversations.id == conversation_uuid)
    existing_conversation = await db.scalar(conversation_exists_stmt)

    if existing_conversation:
        if existing_conversation.agent_id != agent_id:
            logger.warning(
                f"Conversation {conversation_uuid} belongs to agent "
                f"{existing_conversation.agent_id}, not {agent_id}."
            )
            return
        conversation = existing_conversation
    else:
        # Create new conversation with only essential fields
        conversation = Conversations(
            id=conversation_uuid,
            agent_id=agent_id,
            start_time=span_clean.start_time,  # Use span start time
            custom_fields={},
            # No message_count or last_message_at - they're computed!
        )
        db.add(conversation)
        await db.flush()

    # Extract and structure raw message data - use what agent provides
    raw_message_json = span_clean.attributes.get("gen_ai.raw_message")
    raw_message_data: dict = {}

    if raw_message_json:
        try:
            raw_message_data = json.loads(raw_message_json)
        except (json.JSONDecodeError, TypeError) as e:
            logger.error(f"Invalid raw_message JSON in span {span_clean.span_id}: {e}")
            raw_message_data = {}

    # Process user message if present
    user_message_content = span_clean.attributes.get("gen_ai.conversation.question")
    if user_message_content:
        user_turn = MessageTurn(
            conversation_id=conversation.id,
            trace_id=span_clean.trace_id,
            timestamp=span_clean.end_time,  # Use span end time
            role="user",
            content_text=str(user_message_content),
            raw_message=raw_message_data,
        )
        db.add(user_turn)

    # Process bot message if present
    bot_message_content = span_clean.attributes.get("gen_ai.conversation.response")
    if bot_message_content:
        bot_turn = MessageTurn(
            conversation_id=conversation.id,
            trace_id=span_clean.trace_id,
            timestamp=span_clean.end_time,  # Use span end time
            role="agent",
            content_text=str(bot_message_content),
            raw_message=raw_message_data,
        )
        db.add(bot_turn)
