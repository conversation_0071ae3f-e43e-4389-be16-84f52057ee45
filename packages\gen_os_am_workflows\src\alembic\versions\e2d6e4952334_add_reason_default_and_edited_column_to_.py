"""Add reason default and edited column to Occurrence; add new default kind to Interaction.

Revision ID: e2d6e4952334
Revises: 37d427c5993c
Create Date: 2025-06-03 07:54:01.157935

"""

from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "e2d6e4952334"
down_revision: str | None = "37d427c5993c"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Upgrade the database."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("occurrence", schema=None) as batch_op:
        batch_op.add_column(sa.Column("edited", sa.<PERSON>an(), nullable=False))

    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade the database."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("occurrence", schema=None) as batch_op:
        batch_op.drop_column("edited")

    # ### end Alembic commands ###
