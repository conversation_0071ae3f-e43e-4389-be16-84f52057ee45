"""Test the tag API."""

import pytest


def test_tag(client):  # Changed fixture from setup_api to client
    """Test the tag API."""
    # Removed: app = setup_api
    # Removed: client = TestClient(app)
    tag_name = "test"
    response = client.post(
        "/tag",
        json={"tag_name": tag_name},
    )

    assert response.status_code == 201
    response_json = response.json()
    assert response_json["tag"] == tag_name
    tag_id = response_json["id"]

    # get
    response = client.get(f"/tag/{tag_id}")
    assert response.status_code == 200
    response_json = response.json()
    assert response_json["tag"] == tag_name

    # update
    updated_tag_name = "update"

    response = client.patch(
        f"/tag/{tag_id}",
        json={"tag_name": updated_tag_name},
    )
    assert response.status_code == 200
    response_json = response.json()
    assert response_json["success"]
    assert response_json["updated_instance"]["tag"] == updated_tag_name
    assert response_json["error"] is None
    assert response_json["updated_values"] == {
        "tag": [tag_name, updated_tag_name],
    }

    # search
    response = client.get(f"/tag?tag_name={updated_tag_name}")

    assert response.status_code == 200
    response_json = response.json()
    assert len(response_json) == 1
    assert response_json[0]["tag"] == updated_tag_name

    # delete
    response = client.delete(f"/tag/{tag_id}")
    assert response.status_code == 200
    response_json = response.json()
    assert response_json["tag"] == updated_tag_name
