# Website ingestion

## Contents

- [Website ingestion](#website-ingestion)
  - [Contents](#contents)
  - [Setup](#setup)
  - [Input and Output File](#input-and-output-file)

## Setup

1. Create folder named `local_storage` in folder `content_management/backend`
2. Agent Manager API and Agent API should be running

3. Install dependencies:
```bash
pdm install
```
1. Run ingestion
```bash
python3 main.py
```

## Input and Output File

The input file is in `web_scraping/data/urls.xlsx` and it's an excel file with 1400 rows and 3 columns (`URLs`, `tag` and `Contacto`) but, at the moment, only the `URLs` column is used at the moment and 9 values in that column are duplicated. Regarding the excel structure, the two first rows and the first column need to be empty.

At the moment, the output file is in `web_scraping/data/output.json` and it's structured as the following example:
```json
{
    "scraped": {
        "pages": [
            {
                "url" :"https://example.com/scraped1",
                "title": "Example Page",
                "scope": "Home / Help / Example Page",
                "summary": "Published on Jan 1, 2025",
                "sections": [
                    {
                        "headline": "Section 1",
                        "info": "Information about section 1",
                    },
                    {
                        "headline": "Section 2",
                        "info": "Information about section 2",
                    }
                ]
            }
        ],
        "count": 1
    },
    "skipped": {
        "urls": [
            "https://example.com/skipped1"
        ],
        "count": 1
    },
    "not_found": {
        "urls": [
            "https://example.com/not_found1"
        ],
        "count": 1
    }
}
```

Note: A page can be skipped for not having any of the basic structures (article, schematic or mixed) or relevant content.
