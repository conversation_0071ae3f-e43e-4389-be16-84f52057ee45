# ruff: noqa: D205, D400, D415, D103
"""Initial Tables.

Revision ID: e0f5970bdd94
Revises:
Create Date: 2025-06-29 23:36:57.478563

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "e0f5970bdd94"
down_revision: str | None = None
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:  # noqa: D103
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "conversations",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("agent_id", sa.String(), nullable=False),
        sa.Column("start_time", sa.DateTime(timezone=True), nullable=False),
        sa.Column("end_time", sa.DateTime(timezone=True), nullable=True),
        sa.Column("custom_fields", sa.JSON(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    with op.batch_alter_table("conversations", schema=None) as batch_op:
        batch_op.create_index(batch_op.f("ix_conversations_agent_id"), ["agent_id"], unique=False)

    op.create_table(
        "span",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("trace_id", sa.UUID(), nullable=False),
        sa.Column("parent_span_id", sa.UUID(), nullable=True),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("start_time", sa.DateTime(), nullable=False),
        sa.Column("end_time", sa.DateTime(), nullable=False),
        sa.Column("attributes", sa.JSON(), nullable=False),
        sa.Column("events", sa.JSON(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    with op.batch_alter_table("span", schema=None) as batch_op:
        batch_op.create_index(batch_op.f("ix_span_trace_id"), ["trace_id"], unique=False)

    op.create_table(
        "message_turns",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("conversation_id", sa.UUID(), nullable=False),
        sa.Column("trace_id", sa.UUID(), nullable=True),
        sa.Column("timestamp", sa.DateTime(timezone=True), nullable=False),
        sa.Column("role", sa.String(), nullable=False),
        sa.Column("content_text", sa.Text(), nullable=True),
        sa.Column("raw_message", sa.JSON(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["conversation_id"],
            ["conversations.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    with op.batch_alter_table("message_turns", schema=None) as batch_op:
        batch_op.create_index(
            batch_op.f("ix_message_turns_conversation_id"), ["conversation_id"], unique=False
        )
        batch_op.create_index(batch_op.f("ix_message_turns_trace_id"), ["trace_id"], unique=False)

    # ### end Alembic commands ###


def downgrade() -> None:  # noqa: D103
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("message_turns", schema=None) as batch_op:
        batch_op.drop_index(batch_op.f("ix_message_turns_trace_id"))
        batch_op.drop_index(batch_op.f("ix_message_turns_conversation_id"))

    op.drop_table("message_turns")
    with op.batch_alter_table("span", schema=None) as batch_op:
        batch_op.drop_index(batch_op.f("ix_span_trace_id"))

    op.drop_table("span")
    with op.batch_alter_table("conversations", schema=None) as batch_op:
        batch_op.drop_index(batch_op.f("ix_conversations_agent_id"))

    op.drop_table("conversations")
    # ### end Alembic commands ###
