"""Changes to models.

Workflow
    (add updated_at and extra_fields columns);
StepInput
    (add previous_step_fed column
    - a column to keep info about if an input depends on a previous step);
WorkflowExecution
    (add updated_at, finished_at and extra_fields columns);

Revision ID: e7a58c11cce3
Revises: 837514154190
Create Date: 2025-05-30 17:05:52.585501

"""

from collections.abc import Sequence

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "e7a58c11cce3"
down_revision: str | None = "837514154190"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Upgrade the database to the new version."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("step_input", schema=None) as batch_op:
        batch_op.add_column(sa.Column("previous_step_fed", sa.JSON(), nullable=False))

    with op.batch_alter_table("workflow", schema=None) as batch_op:
        batch_op.add_column(sa.Column("updated_at", sa.DateTime(), nullable=False))
        batch_op.add_column(sa.Column("extra_fields", sa.JSON(), nullable=False))

    with op.batch_alter_table("workflow_execution", schema=None) as batch_op:
        batch_op.add_column(sa.Column("updated_at", sa.DateTime(), nullable=False))
        batch_op.add_column(sa.Column("finished_at", sa.DateTime(), nullable=True))

    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade the database to the previous version."""
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("workflow_execution", schema=None) as batch_op:
        batch_op.drop_column("finished_at")
        batch_op.drop_column("updated_at")

    with op.batch_alter_table("workflow", schema=None) as batch_op:
        batch_op.drop_column("extra_fields")
        batch_op.drop_column("updated_at")

    with op.batch_alter_table("step_input", schema=None) as batch_op:
        batch_op.drop_column("previous_step_fed")

    # ### end Alembic commands ###
