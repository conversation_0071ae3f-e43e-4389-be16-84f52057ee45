"""Utility functions for ingestions."""

import importlib.resources
from pathlib import Path


def list_ingestions() -> list:
    """List all ingestion folders available in the installed package."""
    ingestion_folders = []
    try:
        package_path = Path(importlib.resources.files("ctt_am_ingestions"))

        for folder in package_path.iterdir():
            if folder.is_dir() and (folder / "main.py").exists():
                ingestion_folders.append(folder.name)
    except Exception as e:
        raise RuntimeError(f"Error accessing ingestions directory: {e}") from e

    return ingestion_folders
