"""Remove outputchange table references.

Revision ID: 40fc64fbd4e3
Revises: 2e8b7fb5bad5
Create Date: 2025-05-29 06:36:51.049002

"""

from collections.abc import Sequence

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "40fc64fbd4e3"
down_revision: str | None = "2e8b7fb5bad5"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """Remove the output_change table."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("output_change")
    # ### end Alembic commands ###


def downgrade() -> None:
    """Add the output_change table."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "output_change",
        sa.Column("id", sa.UUID(), autoincrement=False, nullable=False),
        sa.Column("workflow_run_id", sa.UUID(), autoincrement=False, nullable=False),
        sa.Column(
            "input", postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=False
        ),
        sa.Column(
            "output", postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=False
        ),
        sa.Column(
            "output_edited",
            postgresql.JSON(astext_type=sa.Text()),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column("created_at", postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
        sa.Column("type", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.ForeignKeyConstraint(
            ["workflow_run_id"], ["workflow_run.id"], name="output_change_workflow_run_id_fkey"
        ),
        sa.PrimaryKeyConstraint("id", name="output_change_pkey"),
    )
    # ### end Alembic commands ###
