"""SQLAlchemy models for agent issue management."""

import uuid
from datetime import datetime

from sqlalchemy import JSON, Boolean, DateTime, ForeignKey, String, Text, func
from sqlalchemy.orm import Mapped, mapped_column, relationship

from gen_os_am_core.database.models.base import Base


class Issue(Base):
    """Core issue details."""

    __tablename__ = "issue"

    id: Mapped[uuid.UUID] = mapped_column(primary_key=True, default=uuid.uuid4)
    description: Mapped[str] = mapped_column(Text, nullable=False)
    agent_id: Mapped[str | None] = mapped_column(String)
    issue_type: Mapped[str] = mapped_column(Text, nullable=False)
    state: Mapped[str] = mapped_column(Text, nullable=False)
    deleted: Mapped[bool] = mapped_column(
        Boolean, nullable=False, server_default="false"
    )
    close_desc: Mapped[str | None] = mapped_column("close_desc", Text, nullable=True)
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now(), onupdate=func.now()
    )

    reported_incidents_conversations: Mapped[list["ReportedIncidentConversation"]] = (
        relationship(
            "ReportedIncidentConversation",
            back_populates="issue",
            cascade="all, delete-orphan",
        )
    )
    reported_incidents_workflows: Mapped[list["ReportedIncidentWorkflow"]] = (
        relationship(
            "ReportedIncidentWorkflow",
            back_populates="issue",
            cascade="all, delete-orphan",
        )
    )
    issue_logs: Mapped[list["IssueLog"]] = relationship(
        "IssueLog", back_populates="issue", cascade="all, delete-orphan"
    )

    def __repr__(self) -> str:
        """Return string representation of Issue."""
        return (
            f"<Issue(id='{self.id}', agent_id='{self.agent_id}', state='{self.state}')>"
        )


class ReportedIncidentConversation(Base):
    """Reported incidents associated with conversations."""

    __tablename__ = "reported_incidents_conversations"

    id: Mapped[uuid.UUID] = mapped_column(primary_key=True, default=uuid.uuid4)
    issue_id: Mapped[uuid.UUID] = mapped_column(
        ForeignKey("issue.id", ondelete="CASCADE"), nullable=False, index=True
    )
    # TODO: Set up ForeignKey for conversation_id once Henrique merges the databases
    conversation_id: Mapped[str] = mapped_column(Text, nullable=False)
    answer: Mapped[str | None] = mapped_column(Text)
    description: Mapped[str | None] = mapped_column(Text)
    severity: Mapped[str] = mapped_column(Text, nullable=False)
    created_by: Mapped[str | None] = mapped_column(String)
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now()
    )
    is_deleted: Mapped[bool] = mapped_column(
        Boolean, nullable=False, server_default="false"
    )
    deleted_at: Mapped[datetime | None] = mapped_column(
        DateTime(timezone=True), nullable=True
    )
    deleted_by: Mapped[str | None] = mapped_column(String, nullable=True)

    issue: Mapped["Issue"] = relationship(
        "Issue", back_populates="reported_incidents_conversations"
    )

    def __repr__(self) -> str:
        """Return string representation of ReportedIncidentConversation."""
        return (
            f"<ReportedIncidentConversation(id='{self.id}', "
            f"issue_id='{self.issue_id}')>"
        )


class ReportedIncidentWorkflow(Base):
    """Reported incidents associated with workflow executions."""

    __tablename__ = "reported_incidents_workflows"

    id: Mapped[uuid.UUID] = mapped_column(primary_key=True, default=uuid.uuid4)
    issue_id: Mapped[uuid.UUID] = mapped_column(
        ForeignKey("issue.id", ondelete="CASCADE"), nullable=False, index=True
    )
    # TODO: Set up ForeignKey for workflow_execution_id once Henrique merges the DBs
    workflow_execution_id: Mapped[str] = mapped_column(Text, nullable=False)
    step: Mapped[str | None] = mapped_column(Text)
    description: Mapped[str | None] = mapped_column(Text)
    severity: Mapped[str] = mapped_column(Text, nullable=False)
    created_by: Mapped[str | None] = mapped_column(String)
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now()
    )
    is_deleted: Mapped[bool] = mapped_column(
        Boolean, nullable=False, server_default="false"
    )
    deleted_at: Mapped[datetime | None] = mapped_column(
        DateTime(timezone=True), nullable=True
    )
    deleted_by: Mapped[str | None] = mapped_column(String, nullable=True)

    issue: Mapped["Issue"] = relationship(
        "Issue", back_populates="reported_incidents_workflows"
    )

    def __repr__(self) -> str:
        """Return string representation of ReportedIncidentWorkflow."""
        return f"<ReportedIncidentWorkflow(id='{self.id}', issue_id='{self.issue_id}')>"


class IssueLog(Base):
    """Internal log entries related to specific issues."""

    __tablename__ = "issue_logs"

    id: Mapped[uuid.UUID] = mapped_column(primary_key=True, default=uuid.uuid4)
    issue_id: Mapped[uuid.UUID] = mapped_column(
        ForeignKey("issue.id", ondelete="CASCADE"), nullable=False, index=True
    )
    log_type: Mapped[str] = mapped_column(Text, nullable=False)
    log_metadata: Mapped[dict | list | None] = mapped_column("metadata", JSON)
    created_by: Mapped[str | None] = mapped_column(String)
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now()
    )

    issue: Mapped["Issue"] = relationship("Issue", back_populates="issue_logs")

    def __repr__(self) -> str:
        """Return string representation of IssueLog."""
        return (
            f"<IssueLog(id='{self.id}', "
            f"issue_id='{self.issue_id}', "
            f"log_type='{self.log_type}')>"
        )
