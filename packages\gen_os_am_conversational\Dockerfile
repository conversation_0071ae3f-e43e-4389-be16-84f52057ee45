FROM python:3.12-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1

# Set work directory
WORKDIR /app

# TODO: Check if there's a better way of handling gcc dependency
# Install PostgreSQL development packages and gcc
RUN apt-get update && apt-get install -y libpq-dev gcc && rm -rf /var/lib/apt/lists/*

# Copy project files
COPY . /app/

# Install dependencies
RUN pip install .

WORKDIR /app/src

# Expose the port the app runs on
EXPOSE 8081

# Make sure the entrypoint script has execute permissions
RUN chmod +x /app/entrypoint.sh

# Run the conversational service
CMD ["/app/entrypoint.sh"]
