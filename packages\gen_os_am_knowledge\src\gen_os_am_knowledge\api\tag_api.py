"""Module for the Tag API."""

import logging
import uuid

from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from gen_os_am_knowledge.api.utils import error_handling_decorator
from gen_os_am_knowledge.config.logger_config import get_api_logger
from gen_os_am_knowledge.sdk.models import Tag
from gen_os_am_knowledge.sdk.operations import TagService
from gen_os_am_knowledge.sdk.operations.utils import NOT_PROVIDED


class TagUpdateModel(BaseModel):
    """Tag update model."""

    tag_name: str | type[NOT_PROVIDED] = NOT_PROVIDED


class CreateTagModel(BaseModel):
    """Create tag model."""

    tag_name: str


class TagAPI:
    """Tag API class to handle tag related operations.

    Args:
        prefix (str, optional): Prefix for the tag API. Defaults to "".

    Methods:
        get_router: Returns the FastAPI router for the tag API

    """

    def __init__(
        self,
        prefix: str = "",
        logger: logging.Logger = get_api_logger(),
    ):
        """Initialize the TagAPI class."""
        self.prefix = prefix
        self.tag_service = TagService(
            logger=logger.getChild("sdk"),
        )

    def get_router(self) -> APIRouter:
        """Return the FastAPI router for the tag API.

        Returns:
            APIRouter: The FastAPI router for the tag API

        """
        router = APIRouter(prefix=self.prefix + "/tag")

        @router.post("/")
        @error_handling_decorator
        async def create_tag(create_model: CreateTagModel) -> JSONResponse:
            """Create a new tag.

            Args:
                create_model (CreateTagModel): Tag creation model as json
                    Ex:
                    {
                        "tag_name": "new_tag_name"
                    }

            Returns:
                JSONResponse: JSON response with the created tag

            """
            result = await self.tag_service.create_tag(**create_model.model_dump())
            return JSONResponse(content=result.model_dump(mode="json"), status_code=201)

        @router.delete("/{tag_id}")
        @error_handling_decorator
        async def delete_tag(tag_id: uuid.UUID) -> Tag | None:
            """Delete a tag.

            Args:
                tag_id (uuid.UUID): Tag ID to delete

            Returns:
                Tag: Deleted tag

            """
            result = await self.tag_service.delete_tag(tag_id)
            if result.success:
                return result.deleted_instance
            else:
                raise HTTPException(status_code=404, detail=result.error)

        @router.get("/{tag_id}")
        @error_handling_decorator
        async def get_tag(tag_id: uuid.UUID) -> Tag | None:
            """Get a tag.

            Args:
                tag_id (uuid.UUID): Tag ID to get

            Returns:
                Tag: Tag instance if found else None

            """
            result = await self.tag_service.get_tag(tag_id)
            return result

        @router.get("/")
        @error_handling_decorator
        async def search_tags(
            tag_name: str | None = None, offset: int = 0, limit: int = 20
        ) -> list[Tag]:
            """Search tags.

            Args:
                tag_name (str, optional): Tag name to search. Defaults to None.
                offset (int, optional): Offset for pagination. Defaults to 0.
                limit (int, optional): Limit for pagination. Defaults to 20.

            Returns:
                list[Tag]: List of tags

            """
            result = await self.tag_service.search_tags(
                tag_name=tag_name, offset=offset, limit=limit
            )
            return result

        @router.patch("/{tag_id}")
        @error_handling_decorator
        async def update_tag(tag_id: uuid.UUID, update_model: TagUpdateModel):
            """Update a tag.

            Args:
                tag_id (uuid.UUID): Tag ID to update
                update_model (TagUpdateModel): Tag update model as json
                    Ex:
                    {
                        "tag_name": "new_tag_name"
                    }

            Returns:
                UpdateResult: Update result with the updated tag instance

            """
            result = await self.tag_service.update_tag(
                tag_id, **update_model.model_dump()
            )
            return result

        return router
