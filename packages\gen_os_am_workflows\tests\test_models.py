"""Tests for the database models."""

import uuid
from datetime import datetime

import pytest
from sqlalchemy import select

from gen_os_am_workflows.database.models import (
    File,
    Interaction,
    InteractionKindApproved,
    InteractionKindResumed,
    Occurrence,
    OccurrenceStatusOpen,
    OccurrenceStatusSolved,
    Step,
    StepBlockTypeSelection,
    StepBlockTypeTextStructured,
    StepInput,
    StepOutput,
    StepRun,
    StepRunStatusCompleted,
    StepRunStatusRunning,
    StepTypeAction,
    StepTypeGenAi,
    StepTypeTool,
    Workflow,
    WorkflowExecution,
    WorkflowExecutionHistory,
    WorkflowExecutionStatusCompleted,
    WorkflowExecutionStatusRunning,
)


class TestWorkflowModel:
    """Test Workflow model."""

    def test_create_workflow(self, get_session):
        """Test creating a workflow."""
        workflow = Workflow(
            name=f"test_workflow_{uuid.uuid4().hex[:8]}",
            description="A test workflow",
            extra_fields=[{"field_name": "priority", "field_type": "string"}],
        )

        assert workflow.name.startswith("test_workflow_")
        assert workflow.description == "A test workflow"
        assert len(workflow.extra_fields) == 1
        assert workflow.extra_fields[0]["field_name"] == "priority"

    def test_workflow_relationships(self, get_session):
        """Test workflow relationships."""
        workflow = Workflow(
            name=f"test_workflow_relationships_{uuid.uuid4().hex[:8]}",
            description="A test workflow",
            extra_fields=[],
        )

        # Test that relationships are initialized as empty lists
        assert workflow.steps == []
        assert workflow.workflow_executions == []


class TestStepModel:
    """Test Step model."""

    def test_create_step(self, get_session):
        """Test creating a step."""
        step = Step(
            name=f"test_step_{uuid.uuid4().hex[:8]}",
            description="A test step",
            step_type=StepTypeGenAi,
            order_number=1,
        )

        assert step.name.startswith("test_step_")
        assert step.description == "A test step"
        assert step.step_type == StepTypeGenAi
        assert step.order_number == 1

    def test_step_relationships(self, get_session):
        """Test step relationships."""
        step = Step(
            name=f"test_step_relationships_{uuid.uuid4().hex[:8]}",
            description="A test step",
            step_type=StepTypeTool,
            order_number=1,
        )

        # Test that relationships are initialized as empty lists
        assert step.step_runs == []
        assert step.inputs == []
        assert step.outputs == []

    def test_step_type_validation(self, get_session):
        """Test step type validation."""
        # Valid step types
        valid_types = [StepTypeGenAi, StepTypeTool, StepTypeAction]

        for step_type in valid_types:
            step = Step(
                name=f"test_step_{step_type}_{uuid.uuid4().hex[:8]}",
                description="A test step",
                step_type=step_type,
                order_number=1,
            )
            assert step.step_type == step_type


class TestStepInputModel:
    """Test StepInput model."""

    def test_create_step_input(self, get_session):
        """Test creating a step input."""
        step_input = StepInput(
            step_block_name="input_text",
            step_block_type=StepBlockTypeTextStructured,
            type_extra={"max_length": 1000},
            order_number=1,
            status="editable",
        )

        assert step_input.step_block_name == "input_text"
        assert step_input.step_block_type == StepBlockTypeTextStructured
        assert step_input.type_extra["max_length"] == 1000
        assert step_input.order_number == 1
        assert step_input.status == "editable"

    def test_step_input_validation(self, get_session):
        """Test step input validation."""
        # Test that type_extra is required for selection type
        with pytest.raises(ValueError):
            StepInput(
                step_block_name="selection_input",
                step_block_type=StepBlockTypeSelection,
                type_extra={},  # Empty for selection should raise error
                order_number=1,
                status="editable",
            )

        # Test that type_extra is required for text_structured type
        with pytest.raises(ValueError):
            StepInput(
                step_block_name="text_input",
                step_block_type=StepBlockTypeTextStructured,
                type_extra={},  # Empty for text_structured should raise error
                order_number=1,
                status="editable",
            )


class TestStepOutputModel:
    """Test StepOutput model."""

    def test_create_step_output(self, get_session):
        """Test creating a step output."""
        step_output = StepOutput(
            step_block_name="output_text",
            step_block_type=StepBlockTypeTextStructured,
            type_extra={"format": "markdown"},
            order_number=1,
            status="editable",
        )

        assert step_output.step_block_name == "output_text"
        assert step_output.step_block_type == StepBlockTypeTextStructured
        assert step_output.type_extra["format"] == "markdown"
        assert step_output.order_number == 1
        assert step_output.status == "editable"


class TestWorkflowExecutionModel:
    """Test WorkflowExecution model."""

    def test_create_workflow_execution(self, get_session):
        """Test creating a workflow execution."""
        execution = WorkflowExecution(
            extra_fields=[{"field_name": "priority", "field_value": "high"}]
        )

        # Check that the model was created successfully
        assert len(execution.extra_fields) == 1
        assert execution.extra_fields[0]["field_name"] == "priority"
        # Note: status and timestamps may not be set until the object is persisted
        # We'll test the basic structure instead

    def test_workflow_execution_relationships(self, get_session):
        """Test workflow execution relationships."""
        execution = WorkflowExecution(extra_fields=[])

        # Test that relationships are initialized as empty lists
        assert execution.step_runs == []
        assert execution.history == []

    def test_workflow_execution_status_transitions(self, get_session):
        """Test workflow execution status transitions."""
        execution = WorkflowExecution(extra_fields=[])

        # Test status change
        execution.status = WorkflowExecutionStatusCompleted
        assert execution.status == WorkflowExecutionStatusCompleted

    def test_workflow_execution_completion(self, get_session):
        """Test workflow execution completion."""
        execution = WorkflowExecution(extra_fields=[])

        # Test completion
        execution.status = WorkflowExecutionStatusCompleted
        execution.finished_at = datetime.now()

        assert execution.status == WorkflowExecutionStatusCompleted
        assert execution.finished_at is not None


class TestStepRunModel:
    """Test StepRun model."""

    def test_create_step_run(self, get_session):
        """Test creating a step run."""
        step_run = StepRun(
            inputs=[{"input_name": "text", "value": "test input"}],
            outputs=[{"output_name": "result", "value": "test output"}],
        )

        # Check that the model was created successfully
        assert len(step_run.inputs) == 1
        assert len(step_run.outputs) == 1
        assert step_run.inputs[0]["input_name"] == "text"
        assert step_run.outputs[0]["output_name"] == "result"
        # Note: edited, status and timestamps may not be set until the object is persisted

    def test_step_run_relationships(self, get_session):
        """Test step run relationships."""
        step_run = StepRun(inputs=[], outputs=[])

        # Test that relationships are initialized as empty lists
        assert step_run.occurrences == []

    def test_step_run_completion(self, get_session):
        """Test step run completion."""
        step_run = StepRun(inputs=[], outputs=[])

        # Test completion
        step_run.status = StepRunStatusCompleted
        step_run.completed_at = datetime.now()

        assert step_run.status == StepRunStatusCompleted
        assert step_run.completed_at is not None


class TestOccurrenceModel:
    """Test Occurrence model."""

    def test_create_occurrence(self, get_session):
        """Test creating an occurrence."""
        occurrence = Occurrence(
            reason="pending_approval",
            message="Need approval for this step",
            status=OccurrenceStatusOpen,
        )

        assert occurrence.reason == "pending_approval"
        assert occurrence.message == "Need approval for this step"
        assert occurrence.status == OccurrenceStatusOpen
        # Note: created_at may not be set until the object is persisted

    def test_occurrence_relationships(self, get_session):
        """Test occurrence relationships."""
        occurrence = Occurrence(
            reason="pending_approval", message="Test occurrence", status=OccurrenceStatusOpen
        )

        # Test that relationships are initialized as empty lists
        assert occurrence.interactions == []

    def test_occurrence_status_transitions(self, get_session):
        """Test occurrence status transitions."""
        occurrence = Occurrence(
            reason="pending_approval", message="Test occurrence", status=OccurrenceStatusOpen
        )

        # Test status change
        occurrence.status = OccurrenceStatusSolved
        assert occurrence.status == OccurrenceStatusSolved


class TestInteractionModel:
    """Test Interaction model."""

    def test_create_interaction(self, get_session):
        """Test creating an interaction."""
        interaction = Interaction(
            edited_data=[{"field": "input_text", "old_value": "old", "new_value": "new"}],
            kind=InteractionKindApproved,
            resolution="Approved by user",
        )

        assert len(interaction.edited_data) == 1
        assert interaction.edited_data[0]["field"] == "input_text"
        assert interaction.kind == InteractionKindApproved
        assert interaction.resolution == "Approved by user"
        # Note: interacted_at may not be set until the object is persisted

    def test_interaction_kinds(self, get_session):
        """Test interaction kinds."""
        valid_kinds = [
            InteractionKindApproved,
            InteractionKindResumed,
            "edited",
            "cancelled",
            "solve_manually",
        ]

        for kind in valid_kinds:
            interaction = Interaction(edited_data=[], kind=kind)
            assert interaction.kind == kind


class TestFileModel:
    """Test File model."""

    def test_create_file(self, get_session):
        """Test creating a file."""
        file_obj = File(storage_path="/test/path/file.txt", file_name="file.txt")

        assert file_obj.storage_path == "/test/path/file.txt"
        assert file_obj.file_name == "file.txt"
        # Note: timestamps may not be set until the object is persisted

    def test_file_timestamps(self, get_session):
        """Test file timestamps."""
        file_obj = File(storage_path="/test/path/file.txt", file_name="file.txt")

        # Test that the model was created successfully
        assert file_obj.storage_path == "/test/path/file.txt"
        assert file_obj.file_name == "file.txt"


class TestWorkflowExecutionHistoryModel:
    """Test WorkflowExecutionHistory model."""

    def test_create_history_entry(self, get_session):
        """Test creating a history entry."""
        history = WorkflowExecutionHistory(
            event="execution_started", termination_message="Workflow started successfully"
        )

        assert history.event == "execution_started"
        assert history.termination_message == "Workflow started successfully"
        assert history.error_message is None
        assert history.step_name is None
        # Note: created_at may not be set until the object is persisted

    def test_history_with_error(self, get_session):
        """Test history entry with error."""
        history = WorkflowExecutionHistory(
            event="error", error_message="Something went wrong", step_name="test_step"
        )

        assert history.event == "error"
        assert history.error_message == "Something went wrong"
        assert history.step_name == "test_step"
        assert history.termination_message is None
