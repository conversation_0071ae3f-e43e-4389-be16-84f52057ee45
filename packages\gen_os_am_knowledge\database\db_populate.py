"""Populate the database with the SQL script."""

import logging
import os
import sys

import psycopg
from dotenv import load_dotenv

logger = logging.getLogger(__name__)
handler = logging.StreamHandler(sys.stderr)
logger.addHandler(handler)

if not os.getenv("POSTGRES_DB"):
    # If the environment variables are not set, try to load them from the .env file
    # in the parent directory
    env_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), ".env")
    # If the .env file exists in the parent directory, load it
    if os.path.exists(env_path):
        load_dotenv(env_path)


DB_NAME = os.getenv("POSTGRES_DB", "dbname")
DB_USER = os.getenv("POSTGRES_USER", "somename")
DB_PASSWORD = os.getenv("POSTGRES_PASSWORD", "somepass")
DB_HOST = os.getenv("POSTGRES_HOST", "localhost")
DB_PORT = os.getenv("POSTGRES_PORT", "5432")

conn_str = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

SQL_FILE = "db_populate.sql"


def run_sql_script():
    """Run the SQL script."""
    try:
        with psycopg.connect(conn_str) as conn:
            with conn.cursor() as cur:
                with open(SQL_FILE) as f:
                    sql_script = f.read()

                cur.execute(sql_script)
                conn.commit()
                print("✅ SQL script executed successfully!")  # noqa: T201

    except Exception as e:
        print(f"❌ Error executing SQL script: {e}")  # noqa: T201


if __name__ == "__main__":
    run_sql_script()
