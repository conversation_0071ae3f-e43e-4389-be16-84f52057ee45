workflow:
  name: 'invoices'
  description: 'a workflow that reads invoices'
  steps:
    - name: 'read invoices'
      description: 'the invoice pdf gets uploaded through a triggering of the workflow for field extraction'
      step_type: 'gen_ai'
      order_number: 1
      inputs:
        - step_block_name: 'invoice pdf'
          status: 'non_editable'
          step_block_type: 'file'
          order_number: 1
      outputs:
        - step_block_name: 'invoice fields'
          status: 'non_editable'
          step_block_type: 'text_structured'
          order_number: 1
          type_extra:
            - field_name: 'Expiration Date'
              field_type: 'string'
            - field_name: 'PO'
              field_type: 'string'
            - field_name: 'Period'
              field_type: 'string'
            - field_name: 'Start Date'
              field_type: 'string'
            - field_name: 'End Date'
              field_type: 'string'
            - field_name: 'Product Description'
              field_type: 'string'
            - field_name: 'Quantity'
              field_type: 'string'
            - field_name: 'Price per line'
              field_type: 'string'
            - field_name: 'Amount Euro'
              field_type: 'string'
            - field_name: 'Payment Terms'
              field_type: 'string'
            - field_name: 'VAT Rate'
              field_type: 'string'
            - field_name: 'Retention Rate'
              field_type: 'string'
            - field_name: 'Building/Cinema/Store/Site'
              field_type: 'string'
            - field_name: 'Type of document'
              field_type: 'string'
            - field_name: 'Invoice Number'
              field_type: 'string'
            - field_name: 'Supplier name'
              field_type: 'string'
            - field_name: 'Supplier Address'
              field_type: 'string'
            - field_name: 'Supplier VAT ID'
              field_type: 'string'
            - field_name: 'Customer name'
              field_type: 'string'
            - field_name: 'Customer VAT ID'
              field_type: 'string'
            - field_name: 'Date of Invoice'
              field_type: 'string'
            - field_name: 'IBAN'
              field_type: 'string'
            - field_name: 'Exemption Reason Description'
              field_type: 'string'
            - field_name: 'Retention Framework Description'
              field_type: 'string'

    - name: 'PO identification'
      description: 'automated step to find the PO number in the invoice; can find one or several POs'
      step_type: 'gen_ai'
      order_number: 2
      inputs:
        - step_block_name: 'invoice fields'
          status: 'non_editable'
          step_block_type: 'text_structured'
          order_number: 1
          previous_step_fed:
            step_name: 'read invoices'
            step_block_name: 'invoice fields'
            type_extra:
              - field_name: 'Expiration Date'
                field_type: 'string'
              - field_name: 'PO'
                field_type: 'string'
              - field_name: 'Period'
                field_type: 'string'
              - field_name: 'Start Date'
                field_type: 'string'
              - field_name: 'End Date'
                field_type: 'string'
              - field_name: 'Product Description'
                field_type: 'string'
              - field_name: 'Quantity'
                field_type: 'string'
              - field_name: 'Price per line'
                field_type: 'string'
              - field_name: 'Amount Euro'
                field_type: 'string'
              - field_name: 'Payment Terms'
                field_type: 'string'
              - field_name: 'VAT Rate'
                field_type: 'string'
              - field_name: 'Retention Rate'
                field_type: 'string'
              - field_name: 'Building/Cinema/Store/Site'
                field_type: 'string'
              - field_name: 'Type of document'
                field_type: 'string'
              - field_name: 'Invoice Number'
                field_type: 'string'
              - field_name: 'Supplier name'
                field_type: 'string'
              - field_name: 'Supplier Address'
                field_type: 'string'
              - field_name: 'Supplier VAT ID'
                field_type: 'string'
              - field_name: 'Customer name'
                field_type: 'string'
              - field_name: 'Customer VAT ID'
                field_type: 'string'
              - field_name: 'Date of Invoice'
                field_type: 'string'
              - field_name: 'IBAN'
                field_type: 'string'
              - field_name: 'Exemption Reason Description'
                field_type: 'string'
              - field_name: 'Retention Framework Description'
                field_type: 'string'
      outputs:
        - step_block_name: 'POs'
          status: 'non_editable'
          step_block_type: 'dataset'
          order_number: 1

    - name: 'PO matching'
      description: 'automated step to match the POs with the invoice'
      step_type: 'gen_ai'
      order_number: 3
      inputs:
        - step_block_name: 'POs'
          status: 'non_editable'
          step_block_type: 'dataset'
          order_number: 1
          previous_step_fed:
            step_name: 'PO identification'
            step_block_name: 'POs'
        - step_block_name: 'invoice fields'
          status: 'non_editable'
          step_block_type: 'text_structured'
          order_number: 2
          previous_step_fed:
            step_name: 'read invoices'
            step_block_name: 'invoice fields'
          type_extra:
            - field_name: 'Expiration Date'
              field_type: 'string'
            - field_name: 'PO'
              field_type: 'string'
            - field_name: 'Period'
              field_type: 'string'
            - field_name: 'Start Date'
              field_type: 'string'
            - field_name: 'End Date'
              field_type: 'string'
            - field_name: 'Product Description'
              field_type: 'string'
            - field_name: 'Quantity'
              field_type: 'string'
            - field_name: 'Price per line'
              field_type: 'string'
            - field_name: 'Amount Euro'
              field_type: 'string'
            - field_name: 'Payment Terms'
              field_type: 'string'
            - field_name: 'VAT Rate'
              field_type: 'string'
            - field_name: 'Retention Rate'
              field_type: 'string'
            - field_name: 'Building/Cinema/Store/Site'
              field_type: 'string'
            - field_name: 'Type of document'
              field_type: 'string'
            - field_name: 'Invoice Number'
              field_type: 'string'
            - field_name: 'Supplier name'
              field_type: 'string'
            - field_name: 'Supplier Address'
              field_type: 'string'
            - field_name: 'Supplier VAT ID'
              field_type: 'string'
            - field_name: 'Customer name'
              field_type: 'string'
            - field_name: 'Customer VAT ID'
              field_type: 'string'
            - field_name: 'Date of Invoice'
              field_type: 'string'
            - field_name: 'IBAN'
              field_type: 'string'
            - field_name: 'Exemption Reason Description'
              field_type: 'string'
            - field_name: 'Retention Framework Description'
              field_type: 'string'
      outputs:
        - step_block_name: 'POs matched'
          status: 'non_editable'
          step_block_type: 'text_structured'
          order_number: 1
          type_extra:
            - field_name: 'POs matched'
              field_type: 'list[string]'

    - name: 'Write to SAP'
      description: 'write the POs matched to SAP'
      step_type: 'gen_ai' # TODO(Henrique): This should be an action step
      order_number: 4
      inputs:
        - step_block_name: 'POs matched'
          status: 'non_editable'
          step_block_type: 'text_structured'
          order_number: 1
          previous_step_fed:
            step_name: 'PO matching'
            step_block_name: 'POs matched'
          type_extra:
            - field_name: 'POs matched'
              field_type: 'list[string]'
      outputs:
        # TODO(Henrique): Remove the output once this is an action step
        - step_block_name: 'Success'
          status: 'non_editable'
          step_block_type: 'text_structured'
          order_number: 1
          type_extra:
            - field_name: 'Success'
              field_type: 'string'
