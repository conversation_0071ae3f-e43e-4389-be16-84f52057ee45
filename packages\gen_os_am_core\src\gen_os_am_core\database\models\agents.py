"""SQLAlchemy models for agents."""

import uuid
from datetime import datetime

from sqlalchemy import <PERSON><PERSON><PERSON>, String, Text
from sqlalchemy.orm import Mapped, mapped_column

from gen_os_am_core.database.models.base import Base


class Agent(Base):
    """SQLAlchemy model for agents."""

    __tablename__ = "agents"

    id: Mapped[str] = mapped_column(String, primary_key=True, nullable=False)
    name: Mapped[str] = mapped_column(String, nullable=False)
    url: Mapped[str] = mapped_column(String, nullable=False)
    description: Mapped[str | None] = mapped_column(Text, nullable=True)
    created_at: Mapped[datetime] = mapped_column(default=datetime.now)
    updated_at: Mapped[datetime] = mapped_column(
        default=datetime.now, onupdate=datetime.now
    )

    def __repr__(self) -> str:
        """Return string representation of Agent."""
        return f"<Agent(id='{self.id}', name='{self.name}, url='{self.url}')>"
