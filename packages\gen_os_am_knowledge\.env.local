# General
# -----------------------------------------------------------------------------
PROJECT_NAME=ctt_am
DEBUG=False
ENVIRONMENT="DEV"
LOG_LEVEL="INFO"

# Database
# ------------------------------------------------------------------------------
## PostgreSQL
POSTGRES_DB=ctt_info_manager
POSTGRES_USER=someuser
POSTGRES_PASSWORD=somepass

POSTGRES_HOST=postgres-infoctt-manager
POSTGRES_PORT=5432

## Optional
POSTGRES_PARAMSPEC='{"connect_timeout": 50}' # valid JSON object with connection parameters
POSTGRESQL_APPLICATION_NAME=app

### The application name to use on the connections
POSTGRESQL_APPLICATION_NAME=name
### Set additional connection parameters
POSTGRES_PARAMSPEC='{"connect_timeout": 50}'

## SQLAlchemy
### Set the pool size, use None to disable pooling on sqlalchemy
SQLALCHEMY_POOL_SIZE=20

# Storage Connectors (only one needed)
# -----------------------------------------------------------------------------
## Azure (needed when using azure connector)
AZURE_STORAGE_ACCOUNT_CONNECTION_STRING="your-connection-string"

## Local (needed when using Local Storage connector)
LOCAL_STORAGE_PATH="path-to-local-storage"

# API
# -----------------------------------------------------------------------------
API_PORT=8088
API_HOST="0.0.0.0"
## Optional
ALLOWED_ORIGINS='["*"]'
API_WORKERS=4
API_RELOAD=false


# Agent Manager ingestion
AM_DRAFT_MODE=false

# Agent URL
AGENT_API_URL="http://agent-infoctt:8084/agent-infoctt"
