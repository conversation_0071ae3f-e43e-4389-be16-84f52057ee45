"""Workflow orchestration router for the Agent Manager API."""

from typing import Any, Literal

from fastapi import APIRouter, Depends, HTTPException, Path, Request, Response
from gen_os_sdk_emulator.core.database.crud import get_basemodel_from_table
from sqlalchemy import String, delete, func, or_, select

from gen_os_am_core.database.models.workflows import WorkflowDataset
from gen_os_am_core.database.session import SessionManager
from gen_os_am_core.settings import Settings
from gen_os_am_core.workflows.orchestrator import WorkflowOrchestrator
from gen_os_am_core.workflows.schemas import (
    InteractionNotificationRequest,
    WorkflowDatasetCreate,
    WorkflowDatasetUpdate,
    WorkflowTriggerRequest,
    WorkflowTriggerResponse,
)
from gen_os_am_core.workflows.state_store import create_state_store


class WorkflowRouter:
    """Workflow orchestration router for the Agent Manager API."""

    # Class-level orchestrator instance
    _orchestrator_instance: WorkflowOrchestrator | None = None

    def __init__(self):
        """Initialize the workflow router."""
        self.router = APIRouter(
            prefix="/agents/{agent_id}/core/v1",
            dependencies=[Depends(self._agent_id_dependency)],
            tags=["Workflows"],
        )
        self._setup_routes()

    @classmethod
    def _get_orchestrator(cls) -> WorkflowOrchestrator:
        """Get singleton workflow orchestrator instance with appropriate state store."""
        if cls._orchestrator_instance is None:
            settings = Settings.get_settings()
            state_store = create_state_store(settings.WORKFLOW_STATE_STORE)
            cls._orchestrator_instance = WorkflowOrchestrator(state_store=state_store)
        return cls._orchestrator_instance

    @staticmethod
    def _agent_id_dependency(agent_id: str = Path(...)):
        """Extract agent_id from the path.

        This allows us to add agent_id to the router prefix without
        adding it to every endpoint signature.
        """
        return agent_id

    def _setup_routes(self):
        """Set up the workflow routes."""

        @self.router.post(
            "/trigger/{workflow_name}", response_model=WorkflowTriggerResponse
        )
        async def trigger_workflow(
            request: Request,
            workflow_name: str,
            wf_request: WorkflowTriggerRequest,
            orchestrator: WorkflowOrchestrator = Depends(self._get_orchestrator),
        ) -> dict[str, Any]:
            """Trigger a workflow execution.

            Args:
                request: Request object
                workflow_name: Name of the workflow to execute
                wf_request: Workflow trigger request containing input_data
                    - which is the data to be used to run the first step of the workflow
                orchestrator: Injected workflow orchestrator instance

            Returns:
                dict containing execution ID

            Raises:
                HTTPException: If workflow not found or execution fails

            """
            agent_id = request.path_params["agent_id"]
            try:
                execution_id = await orchestrator.trigger_workflow(
                    workflow_name=workflow_name,
                    input_data=wf_request.input_data,
                    test_execution=wf_request.test_execution,
                    agent_id=agent_id,
                )

                return WorkflowTriggerResponse(execution_id=str(execution_id))

            except ValueError as e:
                raise HTTPException(status_code=404, detail=str(e)) from e
            except HTTPException as e:
                raise HTTPException(status_code=e.status_code, detail=e.detail) from e
            except Exception as e:
                raise HTTPException(
                    status_code=500, detail=f"Failed to trigger workflow: {str(e)}"
                ) from e

        @self.router.get(
            "/datasets",
            response_model=list[
                get_basemodel_from_table(WorkflowDataset, relationship_depth=0)
            ],
        )
        async def get_datasets(
            request: Request,
            response: Response,
            offset: int = 0,
            limit: int = 10,
            search: str = "",
            search_column: Literal["name", "description", "handle"] | None = None,
        ):
            """Get all datasets with search and pagination.

            Args:
                request: Request object
                response: Response object for setting headers
                offset: Number of items to skip
                limit: Maximum number of items to return
                search: Search string to filter results
                search_column: Specific column to search

            """
            agent_id = request.path_params["agent_id"]
            try:
                async with SessionManager.get_session() as db:
                    # Build base query
                    base_query = select(WorkflowDataset).where(
                        WorkflowDataset.agent_id == agent_id
                    )

                    # Add search filters if search string is provided
                    if search:
                        if search_column:
                            if search_column == "handle":
                                base_query = base_query.where(
                                    WorkflowDataset.handle.cast(String).ilike(
                                        f"%{search}%"
                                    )
                                )
                            elif search_column == "name":
                                base_query = base_query.where(
                                    WorkflowDataset.name.ilike(f"%{search}%")
                                )
                            elif search_column == "description":
                                base_query = base_query.where(
                                    WorkflowDataset.description.ilike(f"%{search}%")
                                )
                        else:
                            # Search across all searchable columns
                            base_query = base_query.where(
                                or_(
                                    WorkflowDataset.handle.cast(String).ilike(
                                        f"%{search}%"
                                    ),
                                    WorkflowDataset.name.ilike(f"%{search}%"),
                                    WorkflowDataset.description.ilike(f"%{search}%"),
                                )
                            )

                    # Get total count for pagination header
                    count_query = select(func.count()).select_from(
                        base_query.subquery()
                    )
                    total_count = await db.scalar(count_query)
                    response.headers["x-total-count"] = str(total_count)

                    # Add pagination
                    datasets = await db.execute(base_query.offset(offset).limit(limit))

                    return datasets.scalars().all()
            except Exception as e:
                raise HTTPException(
                    status_code=404, detail=f"Failed to get datasets: {str(e)}"
                ) from e

        @self.router.get(
            "/dataset/{handle}",
            response_model=get_basemodel_from_table(
                WorkflowDataset, relationship_depth=0
            ),
        )
        async def get_dataset(request: Request, handle: int):
            """Get a dataset by handle."""
            agent_id = request.path_params["agent_id"]
            try:
                async with SessionManager.get_session() as db:
                    dataset = await db.execute(
                        select(WorkflowDataset).where(
                            WorkflowDataset.handle == handle,
                            WorkflowDataset.agent_id == agent_id,
                        )
                    )
                    result = dataset.scalars().first()
                    if not result:
                        raise HTTPException(
                            status_code=404,
                            detail=f"Dataset with handle {handle} not found "
                            f"for agent {agent_id}",
                        )
                    return result
            except Exception as e:
                raise HTTPException(
                    status_code=404, detail=f"Failed to get dataset: {str(e)}"
                ) from e

        @self.router.post(
            "/dataset",
            response_model=get_basemodel_from_table(
                WorkflowDataset, relationship_depth=0
            ),
        )
        async def create_dataset(
            request: Request,
            dataset: WorkflowDatasetCreate,
        ):
            """Create a new dataset."""
            agent_id = request.path_params["agent_id"]
            try:
                async with SessionManager.get_session() as db:
                    db_dataset = WorkflowDataset(
                        **dataset.model_dump(exclude_none=True), agent_id=agent_id
                    )
                    db.add(db_dataset)
                    await db.commit()
                    await db.refresh(db_dataset)
                    return db_dataset
            except Exception as e:
                raise HTTPException(
                    status_code=500, detail=f"Failed to create dataset: {str(e)}"
                ) from e

        @self.router.patch(
            "/dataset/{handle}",
            response_model=get_basemodel_from_table(
                WorkflowDataset, relationship_depth=0
            ),
        )
        async def update_dataset(
            request: Request,
            handle: int,
            dataset: WorkflowDatasetUpdate,
        ):
            """Update a dataset."""
            agent_id = request.path_params["agent_id"]
            try:
                async with SessionManager.get_session() as db:
                    db_dataset = await db.execute(
                        select(WorkflowDataset).where(
                            WorkflowDataset.handle == handle,
                            WorkflowDataset.agent_id == agent_id,
                        )
                    )
                    db_dataset = db_dataset.scalars().first()
                    if not db_dataset:
                        raise HTTPException(
                            status_code=404,
                            detail=f"Dataset with handle {handle} not found "
                            f"for agent {agent_id}",
                        )
                    for key, value in dataset.model_dump(exclude_none=True).items():
                        setattr(db_dataset, key, value)
                    await db.commit()
                    await db.refresh(db_dataset)
                    return db_dataset
            except Exception as e:
                raise HTTPException(
                    status_code=500, detail=f"Failed to update dataset: {str(e)}"
                ) from e

        @self.router.delete(
            "/dataset/{handle}",
            response_model=get_basemodel_from_table(
                WorkflowDataset, relationship_depth=0
            ),
        )
        async def delete_dataset(
            request: Request,
            handle: int,
        ):
            """Delete a dataset."""
            agent_id = request.path_params["agent_id"]
            try:
                async with SessionManager.get_session() as db:
                    db_dataset = await db.execute(
                        select(WorkflowDataset).where(
                            WorkflowDataset.handle == handle,
                            WorkflowDataset.agent_id == agent_id,
                        )
                    )
                    db_dataset = db_dataset.scalars().first()
                    if not db_dataset:
                        raise HTTPException(
                            status_code=404,
                            detail=f"Dataset with handle {handle} not found "
                            f"for agent {agent_id}",
                        )
                    await db.delete(db_dataset)
                    await db.commit()
                    return db_dataset
            except Exception as e:
                raise HTTPException(
                    status_code=500, detail=f"Failed to delete dataset: {str(e)}"
                ) from e

        @self.router.delete("/datasets")
        async def delete_datasets(request: Request):
            """Delete all datasets."""
            agent_id = request.path_params["agent_id"]
            try:
                async with SessionManager.get_session() as db:
                    await db.execute(
                        delete(WorkflowDataset).where(
                            WorkflowDataset.agent_id == agent_id
                        )
                    )
                    await db.commit()
                    return {"message": f"All datasets deleted for agent {agent_id}"}
            except Exception as e:
                raise HTTPException(
                    status_code=500, detail=f"Failed to delete datasets: {str(e)}"
                ) from e

        @self.router.post("/notifications/interaction")
        async def handle_interaction_notification(
            request: Request,
            interaction_notification: InteractionNotificationRequest,
            orchestrator: WorkflowOrchestrator = Depends(self._get_orchestrator),
        ) -> dict[str, Any]:
            """Handle notification that an interaction has been created/updated.

            This endpoint is called by gen_os_am_workflows when interactions are created
            through the InteractionStateChangeCascade process.

            Args:
                request: Request object
                interaction_notification: Interaction notification data
                orchestrator: Injected workflow orchestrator instance

            Returns:
                Success response

            """
            agent_id = request.path_params["agent_id"]
            try:
                await orchestrator.handle_interaction_notification(
                    **interaction_notification.model_dump(),
                    agent_id=agent_id,
                )

                return {
                    "message": "Interaction notification processed",
                    "execution_id": str(interaction_notification.execution_id),
                    "interaction_kind": interaction_notification.interaction_kind,
                    "agent_id": agent_id,
                }

            except Exception as e:
                raise HTTPException(
                    status_code=500,
                    detail=f"Failed to process interaction notification: {str(e)}",
                ) from e

    def get_router(self) -> APIRouter:
        """Get the configured router."""
        return self.router
