"""Module for the API utils."""

from functools import wraps

from fastapi import HTTPException


def error_handling_decorator(function):
    """Decorate to handle errors.

    Args:
        function (callable): The function to decorate.

    Returns:
        callable: The decorated function.

    """

    @wraps(function)
    async def wrapper(*args, **kwargs):
        try:
            return await function(*args, **kwargs)
        except Exception as e:
            raise HTTPException(status_code=400, detail=str(e)) from e

    return wrapper


def validate_boolean(value: str | bool) -> bool:
    """Validate a boolean value.

    Args:
        value (str | bool): The value to validate.

    Returns:
        bool: The validated boolean value.

    """
    if isinstance(value, bool):
        return value
    if value.lower() in ["true", "1", "yes"]:
        return True
    elif value.lower() in ["false", "0", "no"]:
        return False
    else:
        raise ValueError("Invalid boolean value")
