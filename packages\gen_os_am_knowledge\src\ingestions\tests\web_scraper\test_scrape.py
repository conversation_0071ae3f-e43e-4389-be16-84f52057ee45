"""Test the web scraper."""

import asyncio
import json
import os

import pytest

try:
    from aiohttp import ClientSession
    from aioresponses import aioresponses
    from ctt_am_ingestions.website_ingestion.web_scraping.results import Results
    from ctt_am_ingestions.website_ingestion.web_scraping.scrape import scrape_url
except ImportError:
    pytest.skip(
        "Ingestions are not part of the scope of the project", allow_module_level=True
    )


@pytest.fixture(scope="module")
def output_sample() -> dict:
    """Fixture to load the expected output sample."""
    with open(
        os.path.join(
            os.path.join(os.path.dirname(__file__), "fixtures"), "output_sample.json"
        ),
        encoding="utf-8",
    ) as f:
        return json.load(f)


def load_mock_html(file_name):
    """Load a mock HTML file for testing."""
    with open(
        os.path.join(os.path.join(os.path.dirname(__file__), "pages"), file_name),
        encoding="utf-8",
    ) as f:
        return f.read()


@pytest.mark.asyncio
async def test_scrape_sample_urls_async(output_sample):
    """Test the scraping logic using mocked HTML responses with asyncio and aiohttp."""
    results = Results()
    visited_urls = set()

    # Define mock responses for specific URLs
    base_url = "https://www.ctt.pt/page"
    mock_pages = {f"{base_url}{i}": f"page{i}.html" for i in range(1, 21)}

    with aioresponses() as mocked:
        for url, page in mock_pages.items():
            mocked.get(url, status=200, body=load_mock_html(page))

        async with ClientSession() as session:
            tasks = [
                scrape_url(url, visited_urls, results, session) for url in mock_pages
            ]
            await asyncio.gather(*tasks)

    # Validate the results match the expected JSON
    assert (
        results.model_dump() == output_sample
    ), "Scraped JSON does not match the expected output."
