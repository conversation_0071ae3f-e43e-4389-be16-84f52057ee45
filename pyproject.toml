[project]

name="daredata-gen-os-agent-manager"
version = "0.1.0"
description=""
authors=[
  { name="DareD<PERSON>", email="<EMAIL>" },
]

requires-python=">=3.12,<3.13"
readme="README.md"
license={ text="Proprietary" }

dependencies = [
    "mkdocs>=1.6.1",
    "build>=1.2.2.post1"
]

[build-system]
requires=["pdm-backend"]
build-backend="pdm.backend"

[dependency-groups]
dev = [
    "pre-commit>=4.2.0"
]
test = [
]
doc = [
  "mkdocs>=1.6.1",
  "mkdocstrings>=0.29.0",
  "mkdocstrings-python>=1.16.8",
  "mkdocs-material>=9.6.9",
  "mkdocs-include-markdown-plugin>=7.1.5",
  "mkdocs-macros-plugin>=1.3.7",
]

notebooks = [
]

[tool.ruff]
lint.select = ["E", "F"]
lint.ignore = ["I001", "E402", "F811", "E722", "E712"]

[tool.black]
line-length = 88
include = '\.pyi?$'
exclude = '''
/(
    \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
)/
'''

[tool.pdm.scripts]
precommit = "sh -c 'pre-commit run --show-diff-on-failure --color=always --files $(git ls-files)'"

[tool.pdm]
distribution = true
