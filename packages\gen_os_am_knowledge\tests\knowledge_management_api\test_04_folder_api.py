"""Test the folder API."""

import uuid

import pytest

from gen_os_am_knowledge.sdk.models.default_uuid import _DEFAULT_UUID


def test_folder(client):
    """Test the folder API."""
    # Use a unique folder name to avoid conflicts
    folder_name = f"test_{uuid.uuid4().hex[:8]}"

    response = client.post(
        "/folder",
        json={
            "folder_name": folder_name,
            "user_id": str(_DEFAULT_UUID),
            "parent_id": None,
        },
    )

    assert response.status_code == 201
    response_json = response.json()
    assert response_json["name"] == folder_name
    folder_id = response_json["id"]

    # Use another unique name for the subfolder
    subfolder_name = f"test_{uuid.uuid4().hex[:8]}"
    response = client.post(
        "/folder",
        json={
            "folder_name": subfolder_name,
            "user_id": str(_DEFAULT_UUID),
            "parent_id": folder_id,
        },
    )

    assert response.status_code == 201
    response_json = response.json()
    assert response_json["name"] == subfolder_name
    subfolder_id = response_json["id"]  # Store subfolder_id separately

    # get
    response = client.get(f"/folder/{subfolder_id}")
    assert response.status_code == 200
    response_json = response.json()
    assert response_json["name"] == subfolder_name

    # update
    updated_folder_name = f"updated_{uuid.uuid4().hex[:8]}"

    response = client.patch(
        f"/folder/{subfolder_id}/{_DEFAULT_UUID}",
        json={
            "folder_name": updated_folder_name,
            "parent_folder_id": None,
        },
    )
    assert response.status_code == 200
    response_json = response.json()
    assert response_json["success"]

    # Check that updated_values contains the correct before/after values
    assert "name" in response_json["updated_values"]
    assert len(response_json["updated_values"]["name"]) == 2
    assert response_json["updated_values"]["name"][1] == updated_folder_name
    assert response_json["error"] is None

    # Verify the update worked by getting the folder again
    response = client.get(f"/folder/{subfolder_id}")
    assert response.status_code == 200
    response_json = response.json()
    assert response_json["name"] == updated_folder_name

    # search
    response = client.get(f"/folder?folder_name={updated_folder_name}&parent_id=null")

    assert response.status_code == 200
    response_json = response.json()
    assert len(response_json) == 1
    assert response_json[0]["name"] == updated_folder_name

    # delete subfolder
    response = client.delete(f"/folder/{subfolder_id}/{_DEFAULT_UUID}")
    assert response.status_code == 200
    response_json = response.json()
    assert response_json["name"] == updated_folder_name

    # delete folder
    response = client.delete(f"/folder/{folder_id}/{_DEFAULT_UUID}")
    assert response.status_code == 200
    response_json = response.json()
    assert response_json["name"] == folder_name

    # cleanup deleted folders
    response = client.delete("/folder/cleanup_deleted_folders")
    assert response.status_code == 200
