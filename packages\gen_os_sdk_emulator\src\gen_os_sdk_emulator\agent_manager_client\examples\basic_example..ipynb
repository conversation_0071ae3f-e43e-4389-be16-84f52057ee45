{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from gen_os_sdk_emulator.agent_manager_client import (\n", "    AgentManagerClient,\n", "    LocalAuthProvider,\n", ")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["auth_provider = LocalAuthProvider()\n", "client = AgentManagerClient(auth_provider, \"http://0.0.0.0:8080\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'status': 'running'}"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["result = await client.get_agent_manager_status()\n", "result"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'id': '25075b9b-7c18-4677-ac16-37a32fd4fbd5', 'tag': 'the-test-tag'}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["result = await client.gen_os_am_knowledge.create_tag(\"the-test-tag\")\n", "result"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'id': '69310113-44e3-4f0b-bfb5-5fce8b794272',\n", " 'name': 'the-best-workflow',\n", " 'description': 'the-best-description',\n", " 'created_at': '2025-05-19T15:17:52.200222'}"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["result = await client.gen_os_am_workflows._os_am_workflows._os_am_workflows._os_am_workflows._os_am_workflows._os_am_workflows.create_workflow(  # noqa: E501\n", "    name=\"the-best-workflow\", description=\"the-best-description\"\n", ")\n", "result"]}], "metadata": {"kernelspec": {"display_name": "gen-os-sdk-emulator-KAo4_DvN-gen-os-sdk-emulator", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}