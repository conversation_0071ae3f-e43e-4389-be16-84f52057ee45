"""Tests for local filesystem connector."""

import os
import shutil

import pytest
from pytest import fixture

from gen_os_am_knowledge.sdk.filesystem_connectors import LocalFSConnector


@fixture(scope="module")
def create_folder():
    """Create root folder for testing."""
    storage_path = os.environ["AM_KM_LOCAL_STORAGE_PATH"]
    root_folder = "root-folder/local_connector"
    os.makedirs(f"{storage_path}/{root_folder}", exist_ok=True)
    yield root_folder
    shutil.rmtree(storage_path)


@fixture(scope="module")
def create_test_file():
    """Create test file for testing."""
    with open("test.txt", "w") as f:
        f.write("test")
    yield "test.txt"
    os.remove("test.txt")


def test_connector(create_folder, create_test_file):
    """Test local filesystem connector functionality."""
    connector = LocalFSConnector(root_folder=create_folder)
    assert (
        connector.prefix == f"{os.environ["AM_KM_LOCAL_STORAGE_PATH"]}/{create_folder}/"
    )
    assert connector.ls("/") == []

    connector.create_folder("test_folder")
    assert connector.ls("/") == ["test_folder/"]

    connector.create_folder("test_folder/1/2/3/4/5/")

    assert connector.ls("test_folder/") == ["1/"]
    assert connector.ls("test_folder/1/2/3/4/") == ["5/"]

    with open(create_test_file, "rb") as f:
        connector.upload_file("test_folder/1/2/3/4/5/", "test.txt", f, overwrite=True)

    assert connector.ls("test_folder/1/2/3/4/5/") == ["test.txt"]

    with open(create_test_file, "rb") as f:
        connector.upload_file("test_folder/1/2/3", "test.txt", f, overwrite=True)

    assert set(connector.ls("test_folder/1/2/3/")) == {"test.txt", "4/"}

    file_obj = connector.download_file("test_folder/1/2/3/", "test.txt")
    assert file_obj.read() == b"test"

    connector.move_file("test_folder/1/2/3/test.txt", "test_folder/test2.txt")
    assert set(connector.ls("test_folder/")) == {"test2.txt", "1/"}
    assert connector.ls("test_folder/1/2/3/") == ["4/"]

    connector.copy_file(
        "test_folder/1/2/3/4/5/test.txt", "test_folder/1/2/3/4/test3.txt"
    )
    assert connector.ls("test_folder/1/2/3/4/5/") == ["test.txt"]
    assert set(connector.ls("test_folder/1/2/3/4/")) == {"test3.txt", "5/"}

    with pytest.raises(
        Exception, match="File test_folder/1/2/3/4/5/not_existent.txt does not exist"
    ):
        connector.delete_file("test_folder/1/2/3/4/5/", "not_existent.txt")

    connector.delete_file("test_folder/1/2/3/4/", "test3.txt")
    assert connector.ls("test_folder/1/2/3/4/") == ["5/"]

    connector.copy_folder("test_folder/1/", "test_folder/1_copy")
    assert set(connector.ls("test_folder/")) == {"test2.txt", "1_copy/", "1/"}
    assert connector.ls("test_folder/1_copy/") == ["1/"]
    assert connector.ls("test_folder/1_copy/1/2/3/4/5/") == ["test.txt"]
    assert connector.ls("test_folder/1/2/3/4/5/") == ["test.txt"]

    connector.move_folder("test_folder/1/", "new/")
    assert set(connector.ls("test_folder/")) == {"test2.txt", "1_copy/"}
    assert connector.ls("new/") == ["1/"]
    assert connector.ls("new/1/2/3/4/5/") == ["test.txt"]
    assert set(connector.ls("/")) == {"test_folder/", "new/"}

    connector.delete_folder("new/")
    assert connector.ls("/") == ["test_folder/"]

    connector.delete_folder("test_folder/")

    assert connector.ls("/") == []
