"""CRUD operations for the database."""

from typing import Any

from pydantic import BaseModel, ConfigDict, create_model
from sqlalchemy.orm import DeclarativeBase
from typing_inspect import get_args


def get_basemodel_from_table(
    table: type[DeclarativeBase],
    relationship_depth: int = 1,
    ignore_fields: set[str] | None = None,
) -> type[BaseModel]:
    """Dynamically create a Pydantic BaseModel from a SQLAlchemy 2.0 table.

    This table can be used to get a response model for fastAPI endpoints.

    Includes support for relationships.

    Args:
        table: A SQLAlchemy 2.0 table (a class inheriting from `DeclarativeBase`).
        relationship_depth: the depth to which relationships are processed
        ignore_fields: a list of tuples of (ClassName, FieldName) to ignore
    Returns:
        A Pydantic BaseModel representing the table's schema and relationships.

    """
    # Set fields to ignore
    ignore_fields = ignore_fields or set()

    # Get the table's columns and relationships
    mapper = table.__mapper__

    columns = mapper.columns
    relationships = mapper.relationships

    # Map SQLAlchemy column types to Pydantic field types
    field_definitions: dict[str, Any] = {}
    for column in columns:
        # Extract the Python type from the annotation
        python_type = get_args(table.__annotations__[column.key])[0]

        # Handle None | fields (nullable columns)
        if column.nullable:
            python_type = None | python_type
        if f"{table.__tablename__}.{column.key}" not in ignore_fields:
            # Add the field to the Pydantic model definition
            field_definitions[column.key] = (
                python_type,
                ... if not column.nullable else None,
            )

    if relationship_depth > 0:
        # Map SQLAlchemy relationships to Pydantic models
        for rel_name, rel in relationships.items():
            if f"{table.__tablename__}.{rel_name}" not in ignore_fields:
                rel_type = get_basemodel_from_table(
                    rel.entity.class_,
                    relationship_depth=relationship_depth - 1,
                    ignore_fields=ignore_fields,
                )
                # Determine the type of relationship (one-to-many, many-to-one, etc.)
                if rel.uselist:
                    field_definitions[rel_name] = (list[rel_type] | None, [])
                else:
                    field_definitions[rel_name] = (rel_type | None, None)

    # Create the Pydantic model dynamically
    model_name = f"{table.__name__}Model"
    basemodel = create_model(
        model_name,
        __config__=ConfigDict(from_attributes=True),  # Enable ORM mode
        **field_definitions,
    )
    return basemodel
