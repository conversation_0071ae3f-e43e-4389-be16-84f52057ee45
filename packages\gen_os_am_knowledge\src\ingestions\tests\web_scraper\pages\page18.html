<!DOCTYPE html>
<!--[if lt IE 7]>		<html class="no-js lt-ie9 lt-ie8 lt-ie7"> <![endif]-->
<!--[if IE 7]>			<html class="no-js lt-ie9 lt-ie8"> <![endif]-->
<!--[if IE 8]>			<html class="no-js lt-ie9"> <![endif]-->
<!--[if gt IE 8]>	   <html class="no-js"> <!--[endif]-->
  <head>






<script>
	// Define dataLayer and the gtag function.
	window.dataLayer = window.dataLayer || [];
	function gtag(){dataLayer.push(arguments);}

	// Default ad_storage to 'denied'.
	gtag('consent', 'default', {
		ad_storage: "denied",
		analytics_storage: "denied",
		functionality_storage: "denied",
		personalization_storage: "denied",
		security_storage: "denied",
		ad_user_data: "denied",
		ad_personalization: "denied",
		'wait_for_update': 500
	});
</script>


<!--
Start of global snippet: Please do not remove
Place this snippet between the <head> and </head> tags on every page of your site.
-->
<!-- Google tag (gtag.js) -->
<script async src=""https://www.googletagmanager.com/gtag/js?id=***********""></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', '***********');
</script>
<!-- End of global snippet: Please do not remove -->


                <!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f
);
})(window,document,'script','dataLayer','GTM-KLM9C44');</script>
<!-- End Google Tag Manager -->
<!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f
);
})(window,document,'script','dataLayer','GTM-M5D8RTM');</script>
<!-- End Google Tag Manager -->

                <script type="text/javascript" src="//cdn.evgnet.com/beacon/z55685555553mww3u3d3n3n3m081551792/prod/scripts/evergage.min.js"></script>








    <!-- OneTrust Cookies Consent Notice with Google Consent Mode v2 start for ctt.pt -->

    <script type="text/javascript" src="https://cdn-ukwest.onetrust.com/consent/616501d5-a314-4307-8fd3-619733862897/OtAutoBlock.js" ></script>

    <script src="https://cdn-ukwest.onetrust.com/scripttemplates/otSDKStub.js" data-document-language="true" type="text/javascript" charset="UTF-8" data-domain-script="616501d5-a314-4307-8fd3-619733862897" data-language="pt"></script>

    <script type="text/javascript">

    function OptanonWrapper() {

        applyCookiePreferences();

    }

    </script>

<!-- OneTrust Cookies Consent Notice with Google Consent Mode v2 end for ctt.pt -->


    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
	<meta name="google-site-verification" content="Sza3VcE6z1e36W0o9PEw9KUouxK1-NlmCfjK2v7gW5U" />

        <meta name="facebook-domain-verification" content="jhgfidkhj00870ycdqvycl1ze4ohl8" />


<title>Template Learts</title>

<meta name="description" content="500 anos de dedicação, empenho e profissionalismo, para aproximar os portugueses e o mundo." />

<meta property="og:description" content="500 anos de dedicação, empenho e profissionalismo, para aproximar os portugueses e o mundo." />
<meta property="og:site_name" content="CTT" />
<meta property="og:type" content="website" />
<meta property="og:title" content="Template Learts" />

    <meta property="og:url" content="https://www.ctt.pt:443/ajuda/empresas/criar-lojas-online/templates/template-learts?" />
<meta property="og:image" content="https://www.ctt.pt/application/themes/images/og-image.png" />


  <!--[if (IE)&(lt IE 9) ]>
  <script type="text/javascript">
  window.location = "unsupported.html";
  </script>
  <![endif]-->

  <!--[if lt IE 9]>
	<script src="/application/themes/js/html5shiv.js"></script>
  <!--[endif]-->


<link rel="icon" href="https://www.ctt.pt/favicon.ico">
<link rel="apple-touch-icon" href="https://www.ctt.pt/application/themes/images/icons/apple-touch-icon.png" />
<link rel="apple-touch-icon" sizes="57x57" href="https://www.ctt.pt/application/themes/images/icons/apple-touch-icon-57x57.png" />
<link rel="apple-touch-icon" sizes="60x60" href="https://www.ctt.pt/application/themes/images/icons/apple-touch-icon-60x60.png" />
<link rel="apple-touch-icon" sizes="72x72" href="https://www.ctt.pt/application/themes/images/icons/apple-touch-icon-72x72.png" />
<link rel="apple-touch-icon" sizes="76x76" href="https://www.ctt.pt/application/themes/images/icons/apple-touch-icon-76x76.png" />
<link rel="apple-touch-icon" sizes="114x114" href="https://www.ctt.pt/application/themes/images/icons/apple-touch-icon-114x114.png" />
<link rel="apple-touch-icon" sizes="120x120" href="https://www.ctt.pt/application/themes/images/icons/apple-touch-icon-120x120.png" />
<link rel="apple-touch-icon" sizes="144x144" href="https://www.ctt.pt/application/themes/images/icons/apple-touch-icon-144x144.png" />
<link rel="apple-touch-icon" sizes="152x152" href="https://www.ctt.pt/application/themes/images/icons/apple-touch-icon-152x152.png" />

<link rel="stylesheet" href="https://www.ctt.pt/application/themes/css/app.min.css?t=174920072020" type="text/css" />
<link rel="stylesheet" href="https://www.ctt.pt/application/themes/css/style.css?t=174920072020" type="text/css" />
<style>

    /* Start of code to put "..." when the subtitle is large */
    /*h3.card-title {
        max-width: 5.625rem;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .card-content p{
        max-width: 5.625rem;
        overflow: hidden;
        text-overflow: ellipsis;
    }*/
    /* End of code to put "..." when the subtitle is large */

    /* Temporary fix until a final version is delivered by Major. */
    .card-slider-wraper .tns-outer .tns-nav{
        transform: translateY(2rem);
    }

    /* Hide "Veja também" from mobile until Major delivers a fix*/
    @media only screen and (max-width: 61.25em){
        .sidebar:before {
            content : '';
        }
    }
    .row + section.banner-side-by-side {
        padding-top: 0px;
    }
    /* Style needed to apply "available" and "out-of-stock" info to product listing */
    .product-available  {
    	font-size: 15px!important;
    	color:#0ABEB4!important;
    }

    .product-available i, .product-out-of-stock i {
    	font-size: 11px!important;
    	padding-right: 5px!important;
    }

    .product-out-of-stock {
    	font-size: 15px!important;
    	color:#DF0024!important;
    }
    .store-card .card-content h4.product-name{
        min-height: 80px!important;
    }
    /* Rule added to override banner link lists behavior and instead show a normal list*/
	ul li.no-link::after{
		content: '';
	}
	ul li.no-link a{
	cursor : default;
	}

	ul:hover li.no-link a{
		transition: none;
		opacity: 1;
	}

	/* Additional CSS because of 3 possible store links */
	ul.store-links:hover li:after {
	    opacity: 0.3;
	}

	ul.store-links:hover a {
	    opacity: 0.3;
	}

    div.store-links-all .store-links {
        display:block!important;
    }

    div.store-links-all li {
        padding-right:90px!important;
    }

	.no-content::after {
		content: none!important;
	}

    @media only screen and (min-width: 48.063em) {
        section.store-links-all {
            margin-bottom: 300px;
        }

        /* Start of code to put "..." when the subtitle is large */
		/*h3.card-title {
            max-width: 8.125rem;
        }

        .card-content p{
            max-width: 8.125rem;
        }*/
        /* End of code to put "..." when the subtitle is large */
    }

    @media only screen and (min-width: 61.313em) {
        section.store-links-all {
            margin-bottom: 0px;
        }

        div.store-links-all {
            padding: 72px 20px!important;
        }

        div.store-links-all .store-links {
            display:flex!important;
        }

        div.store-links-all li {
            padding-right:32px!important;
        }

        /* Start of code to put "..." when the subtitle is large */
		/*h3.card-title {
            max-width: 10.625rem;
        }

        .card-content p{
            max-width: 10.625rem;
        }*/
        /* End of code to put "..." when the subtitle is large */
    }

	.product-featured-products .cardPrice.oldPrice {
		color: #929DA8!important;
		text-decoration: line-through!important;
	}

	.product-featured-products .cardPrice {
		font-size: 3rem!important;
		line-height: 1.2!important;
		margin-bottom: 2.5rem!important;
	}

	.product-featured-products .cardDatePrice {
		font-size: 24px!important;
		color: #3b3b3b!important;
	}

	.product-featured-products .cardPriceText {
		color: #3b3b3b!important;
		font-size: 0.875rem!important;
		margin-bottom: 5px!important;
		display: block!important;
	}

	.product-featured-products .button {
		margin: 2.5rem 0 0 0;
	}

	.cardContainerBackgroundImage {
	   background-size: 96% 96%;
       background-repeat: no-repeat;
	}

	.bannerContainerBackgroundImage {
		background-repeat: no-repeat;
		background-position: right 40% bottom;
		background-size: 260px auto;
	}

	@media only screen and (max-width: 980px) {
		.bannerContainerBackgroundImage {
		    background-image: none!important;
			background-repeat: no-repeat;
			background-position: right 40% bottom;
			background-size: 260px auto;
		}
	}
</style>

<script type="text/javascript" src="https://www.ctt.pt/application/themes/js/jquery-1.11.1.min.js" ></script>
<script type="text/javascript" src="https://www.ctt.pt/application/themes/js/jquery-base64.js" ></script>

            <script src='//grmtech.net/r/pt8a0e1141fd37fa5b98d5bb769ba1a7cc.js' async defer></script>
  </head>

    <body>

                                <!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-KLM9C44"
height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->
<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-M5D8RTM"
height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->












        <div class="off-canvas-wrap">

            <div class="inner-wrap">

              <div class="off-canvas-overlay"></div>






<!-- in menu header -->
<style>
    .secondary-nav .nav-utility #s-nav-user > a::before {
       content: "";
    }

    #s-nav-user-icon-anonymous::before {
		content: "\F11C"!important;
	}

    /* Start of code to put history icon in LOJV */
    .contextnav .contextnav-search .contextnav-search-lojv {
        padding-left: 30px;
    }

	.contextnav .contextnav-cart .contextnav-shopping-cart-lojv{
	    padding-left: 26px;
	}

	.contextnav .contextnav-search .contextnav-history-icon{
        padding:16px;
        width:50px;
        margin:0
	}
	/* End of code to put history icon in LOJV */

	.res-circle {
		width: 22px;
		border-radius: 50%;
		background: #ffffff;
		line-height: 0;
		position: relative;
		border: 2px solid #d40022;
	}

	.res-circle::after {
		content: "";
		display: block;
		padding-bottom: 100%;
	}

	.circle-txt {
		position: absolute;
		bottom: 8px;
		width: 18px;
		text-align: center;

		color: #d40022;
		font-family: arial, sans-serif;
		font-weight: bold;
		font-size: 8px;
	}

	.res-circle-container {
		background-color: #ffffff;
		width:26px;
		padding:4px;
		margin-top: -14px;
		margin-left: -9px;
	}

	.ctt-icon-tools {
		display:none;
	}

	li#tools {
		margin-right: 46px;
		margin-top: 1px;
	}

	li#s-nav-user-logged {
		top: 29px;
		left: -44px;
		position: relative;
	}

	a#s-nav-user-icon-anonymous {
		color:#3b3b3b;
		top:7px;
		left:-44px;
	}

	a#s-nav-user-icon-user {
		display:none;
		letter-spacing: normal;
	}

	a#s-nav-search-link {
		color:#3b3b3b;
		top:7px;
		left:-43px;
	}

	div#s-nav-user-drop {
	    margin-right:-8px;
	}

    .secondary-nav .nav-utility .nav-drop {
        top: 110px;
    }

	@media only screen and (min-width: 61.313em) {
		li#s-nav-user-logged {
			top: 50px;
			left: 6px;
			position: relative;
		}

		a#s-nav-search-link {
			color:#3b3b3b;
			top:27px;
			left:-43px;
		}

		.ctt-icon-tools {
			display:block;
			width:14px;
			height:14px;
			color:#3b3b3b;
		}

		a#s-nav-user-icon-anonymous {
			color:#3b3b3b;
			top: 27px;
			left:4px;
		}

		li.blog_item {
		    margin-left: 0px!important;
		    margin-right: 0px!important;
		    float:right!important;
		}

		/* Start of code to put history icon in LOJV */
        .contextnav .contextnav-links.contextnav-links-lojv {
            width: 80%;
        }

        .contextnav .contextnav-utility.contextnav-utility-lojv {
            width: 20%;
        }
        /* End of code to put history icon in LOJV */
	}


	@media only screen and (max-width: 48.063em) {
		a#s-nav-search-link {
			color:#3b3b3b;
			top:18px;
			left:-43px;
		}

		a#s-nav-user-icon-anonymous {
			color:#3b3b3b;
			top:18px;
			left:-44px;
		}

		a#tools-link {
			display:none;
		}

		li#s-nav-user-logged {
			top: 40px;
			left: -44px;
			position: relative;
		}

		.res-circle-container {
			background-color: transparent;
		}

		.ctt-icon-tools {
			display:block;
			width:14px;
			height:14px;
			color:#3b3b3b;
		}
	}

	@font-face {
        font-family: "Ctt Extended Icons";
        src: url(data:application/font-woff;charset=utf-8;base64,d09GRgABAAAAAASkAAsAAAAABFgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABPUy8yAAABCAAAAGAAAABgDxIFKmNtYXAAAAFoAAAAVAAAAFQXVtKHZ2FzcAAAAbwAAAAIAAAACAAAABBnbHlmAAABxAAAAJwAAACc40NbeGhlYWQAAAJgAAAANgAAADYe62/saGhlYQAAApgAAAAkAAAAJAdUA8ZobXR4AAACvAAAABQAAAAUCgAAf2xvY2EAAALQAAAADAAAAAwAKABibWF4cAAAAtwAAAAgAAAAIAAJABluYW1lAAAC/AAAAYYAAAGGmUoJ+3Bvc3QAAASEAAAAIAAAACAAAwAAAAMDAAGQAAUAAAKZAswAAACPApkCzAAAAesAMwEJAAAAAAAAAAAAAAAAAAAAARAAAAAAAAAAAAAAAAAAAAAAQAAA6QADwP/AAEADwABAAAAAAQAAAAAAAAAAAAAAIAAAAAAAAwAAAAMAAAAcAAEAAwAAABwAAwABAAAAHAAEADgAAAAKAAgAAgACAAEAIOkA//3//wAAAAAAIOkA//3//wAB/+MXBAADAAEAAAAAAAAAAAAAAAEAAf//AA8AAQAAAAAAAAAAAAIAADc5AQAAAAABAAAAAAAAAAAAAgAANzkBAAAAAAEAAAAAAAAAAAACAAA3OQEAAAAAAwB/AE8DkgNRABAAEwAWAAABBQ4BFwUTFjY3EzAmJy4BMRUBJSULAQNZ/S8cBhkBILEPOQrwAgkIJv5N/uoC3/quA1HrCTgPmP7RGAYbAsEmCAgCJ/57mc39UAEpAAAAAQAAAAAAAJdiusdfDzz1AAsEAAAAAADeHhXPAAAAAN4eFc8AAAAAA5IDUQAAAAgAAgAAAAAAAAABAAADwP/AAAAEAAAAAAADkgABAAAAAAAAAAAAAAAAAAAABQQAAAAAAAAAAAAAAAIAAAAEAAB/AAAAAAAKABQAHgBOAAEAAAAFABcAAwAAAAAAAgAAAAAAAAAAAAAAAAAAAAAAAAAOAK4AAQAAAAAAAQAHAAAAAQAAAAAAAgAHAGAAAQAAAAAAAwAHADYAAQAAAAAABAAHAHUAAQAAAAAABQALABUAAQAAAAAABgAHAEsAAQAAAAAACgAaAIoAAwABBAkAAQAOAAcAAwABBAkAAgAOAGcAAwABBAkAAwAOAD0AAwABBAkABAAOAHwAAwABBAkABQAWACAAAwABBAkABgAOAFIAAwABBAkACgA0AKRpY29tb29uAGkAYwBvAG0AbwBvAG5WZXJzaW9uIDEuMABWAGUAcgBzAGkAbwBuACAAMQAuADBpY29tb29uAGkAYwBvAG0AbwBvAG5pY29tb29uAGkAYwBvAG0AbwBvAG5SZWd1bGFyAFIAZQBnAHUAbABhAHJpY29tb29uAGkAYwBvAG0AbwBvAG5Gb250IGdlbmVyYXRlZCBieSBJY29Nb29uLgBGAG8AbgB0ACAAZwBlAG4AZQByAGEAdABlAGQAIABiAHkAIABJAGMAbwBNAG8AbwBuAC4AAAADAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA)
            format("woff");
        font-weight: normal;
        font-style: normal;
    }

    [class^="ctt-extended-icon-"],
    [class*=" ctt-extended-icon-"] {
        speak: none;
        font-family: "Ctt Extended Icons";
        font-style: normal;
        font-weight: normal;
        font-variant: normal;
        text-transform: none;
        text-rendering: auto;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    .ctt-extended-icon-tools-simulator:before {
        content: "\e900";
    }

    ul.blog-nav-segments-wrap {
        width: 100%!important;
    }
</style>



<script type="text/javascript" src="https://www.ctt.pt/application/themes/js/js-cookie-min.js"></script>
<script type="text/javascript" src="https://www.ctt.pt/application/themes/js/language_url.js?t=113027082024"></script>
<script type="text/javascript" src="https://www.ctt.pt/application/themes/js/jquery-base64.js"></script>


<script>
	var baseApplicationUrl = 'https://appserver2.ctt.pt';
	var baseSiteUrl = 'https://www.ctt.pt';

    $(document).ready(function(){
       new LanguageURL("[data-language-url]");
    });
</script>




<input type="hidden" id="locales" value="" />













<header class="page-header ajuda" role="banner">
    <!-- INSIDE OF MENU-HEADER.VTL -->
	<div class="row">
		<div class="navbar">
			<div class="brand-bar">
				<h1 class="brand">
				    				        <a href="https://www.ctt.pt/particulares/index"><img src="https://www.ctt.pt/application/themes/images/logo-ctt.svg" alt="CTT"></a>

				</h1>
				<a id="mobile-nav-toggle" href="#main-nav-mobile" class="nav-toggle" data-main-nav-toggle="">
					<i class="ctt-icon-menu"></i><span class="show-for-sr">Menu</span>
				</a>
				<a href="#" class="mobile-back-button" data-primary-nav-back>
					<i class="ctt-icon-arrow-left"></i><span class="show-for-sr">Voltar</span>
				</a>
			</div>
			<div class="nav-mobile" id="main-nav-mobile" data-main-nav-content>
				<nav class="primary-nav nav-drop-wrap" role="navigation">
					<ul>

					        																	    											<li data-drilldown-nav id="tools" class="has-drop drop-grid-1">
							<!-- QUERY à estrutura Ferramentas -->
							<a id="tools-link" href>
							    <svg aria-hidden="true" focusable="false" data-prefix="fal" data-icon="tools" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" class="ctt-icon-tools"><path fill="currentColor" d="M502.6 389.5L378.2 265c-15.6-15.6-36.1-23.4-56.6-23.4-15.4 0-30.8 4.4-44.1 13.3L192 169.4V96L64 0 0 64l96 128h73.4l85.5 85.5c-20.6 31.1-17.2 73.3 10.2 100.7l124.5 124.5c6.2 6.2 14.4 9.4 22.6 9.4 8.2 0 16.4-3.1 22.6-9.4l67.9-67.9c12.4-12.6 12.4-32.8-.1-45.3zM160 158.1v1.9h-48L42.3 67 67 42.3l93 69.7v46.1zM412.1 480L287.7 355.5c-9.1-9.1-14.1-21.1-14.1-33.9 0-12.8 5-24.9 14.1-33.9 9.1-9.1 21.1-14.1 33.9-14.1 12.8 0 24.9 5 33.9 14.1L480 412.1 412.1 480zM64 432c0 8.8 7.2 16 16 16s16-7.2 16-16-7.2-16-16-16-16 7.2-16 16zM276.8 66.9C299.5 44.2 329.4 32 360.6 32c6.9 0 13.8.6 20.7 1.8L312 103.2l13.8 83 83.1 13.8 69.3-69.3c6.7 38.2-5.3 76.8-33.1 104.5-8.9 8.9-19.1 16-30 21.5l23.6 23.6c10.4-6.2 20.2-13.6 29-22.5 37.8-37.8 52.7-91.4 39.7-143.3-2.3-9.5-9.7-17-19.1-19.6-9.5-2.6-19.7 0-26.7 7l-63.9 63.9-44.2-7.4-7.4-44.2L410 50.3c6.9-6.9 9.6-17.1 7-26.5-2.6-9.5-10.2-16.8-19.7-19.2C345.6-8.3 292 6.5 254.1 44.3c-12.9 12.9-22.9 27.9-30.1 44v67.8l22.1 22.1c-9.6-40.4 1.6-82.2 30.7-111.3zM107 467.1c-16.6 16.6-45.6 16.6-62.2 0-17.1-17.1-17.1-45.1 0-62.2l146.1-146.1-22.6-22.6L22.2 382.3c-29.6 29.6-29.6 77.8 0 107.5C36.5 504.1 55.6 512 75.9 512c20.3 0 39.4-7.9 53.7-22.3L231.4 388c-6.7-9.2-11.8-19.3-15.5-29.8L107 467.1z" class=""></path></svg>
							</a>
							<div class="nav-drop">
							    <div class="mega-menu">
							        <div class="mega-menu-section">
							            <ul gtm-menu="2">
							                                                            <li>
                                                <a href="https://appserver2.ctt.pt/feapl_2/app/open/objectSearch/objectSearch.jspx?request_locale=pt">
                                                    <i class="ctt-icon-tools-track"></i>
                                                    Seguir objeto
                                                </a>
                                            </li>
                                                                                        <li>
                                                <a href="https://appserver2.ctt.pt/feapl_2/app/open/postalCodeSearch/postalCodeSearch.jspx?request_locale=pt">
                                                    <i class="ctt-icon-tools-zipcode"></i>
                                                    Encontrar códigos postais
                                                </a>
                                            </li>
                                                                                        <li>
                                                <a href="https://appserver2.ctt.pt/feecom/app/open/shipping/request.jspx">
                                                    <i class="ctt-icon-tools-sendonline"></i>
                                                    Enviar sem sair de casa
                                                </a>
                                            </li>
                                                                                        <li>
                                                <a href="https://appserver.ctt.pt/OnlineShipment/SimularEnviar">
                                                    <i class="ctt-extended-icon-tools-simulator"></i>
                                                    Simular e Enviar
                                                </a>
                                            </li>
                                                                                        <li>
                                                <a href="https://p1.appserver.ctt.pt/ProcessoAduaneiro/">
                                                    <i class="ctt-icon-tools-customs"></i>
                                                    Desalfandegar uma encomenda
                                                </a>
                                            </li>
                                                                                        <li>
                                                <a href="https://appserver2.ctt.pt/feapl_2/app/open/stationSearch/stationSearch.jspx?request_locale=pt">
                                                    <i class="ctt-icon-tools-locations"></i>
                                                    Encontrar Lojas e Pontos CTT
                                                </a>
                                            </li>
                                                                                        <li>
                                                <a href="/forms/portagens-em-divida">
                                                    <i class="ctt-icon-tools-tolls"></i>
                                                    Consultar portagens a pagamento
                                                </a>
                                            </li>
                                            							            </ul>
							        </div>
							    </div>
							</div>
						</li>

						<!-- Lang switcher for mobile -->
						<li data-drilldown-nav class="has-drop drop-grid-3 hide-for-large-up">
							<a href="#">Idioma</a>
							<div class="nav-drop mobile-drilldown">
								<div class="mega-menu">
									<div class="mega-menu-section">
										<h6>Idioma</h6>
										<ul gtm-menu="1">
									                            							                    							<li><a href="/ajuda/empresas/criar-lojas-online/templates/template-learts?language_id=1">Inglês</a></li>
                    							<li class="active"><a href="/ajuda/empresas/criar-lojas-online/templates/template-learts?com.dotmarketing.htmlpage.language=3">Português</a></li>
                    							                    							                    																</ul>
									</div>
								</div>
							</div>
						</li>
						<!-- Ajuda for mobile -->
											    					    					    						<li class="hide-for-large-up" gtm-menu="1">
							<a href="https://www.ctt.pt/ajuda/index">Ajuda</a>
						</li>
											</ul>
				</nav>
			</div>
		</div>

		<nav class="secondary-nav nav-drop-wrap">
		    		    		    		    		    			<ul class="nav-segments-wrap blog-nav-segments-wrap">
				<li class="has-drop" data-drilldown-nav>
				    				    <a href><span>Ajuda</span></a>
				    					<div class="nav-drop mobile-drilldown">
						<h5>Sector</h5>
						<a id="segments-nav-toggle" href="#" class="nav-toggle" data-nav-drop-close>
							<i class="ctt-icon-menu-close"></i><span class="show-for-sr">Fechar</span>
						</a>
						<ul class="nav-segments" gtm-menu="1">
						    							    							    						        						        						        							<li><a href="https://www.ctt.pt/particulares/index">Particulares</a></li>
    														    							    						        						        						        							<li><a href="https://www.ctt.pt/empresas/index">Empresas</a></li>
    														    							    						        						        						        							<li><a href="https://www.ctt.pt/grupo-ctt/index">Grupo CTT</a></li>

														                                        <li class="blog_item">
                                      <a href="https://blog.ctt.pt/blog-ctt">BLOG</a>
                                    </li>
                                													</ul>
					</div>
				</li>
			</ul>

			<ul class="nav-utility" gtm-menu="1">
			    <input type="hidden" id="hidden_lang" value="1555597541833">

			    			    			    			    			    				<li id="s-nav-user" class="has-drop" data-drilldown-nav data-dropdown-desktop>
					<a id="s-nav-user-icon-anonymous" href="https://appserver2.ctt.pt/femgu/login.jspx?lang=def&redirect=https://www.ctt.pt/particulares/index"></a>

					<div id="s-nav-user-drop" class="nav-drop mobile-drilldown">
						<!-- Area de Login -->
						<div id="s-nav-loginArea">
						    <a id="segments-nav-toggle" href="#" class="nav-toggle" data-nav-drop-close="">
    							<i class="ctt-icon-menu-close"></i><span class="show-for-sr">Fechar</span>
    						</a>
							<!-- Zona de Login -->
							<h5 class="mega-menu-label">Aceda à sua conta CTT</h5>
							<button id="s-nav-user-login" type="button">Entrar</button>
														<!-- Zona de Registo -->
							<h5>Ainda não tem conta?
							    <a href="https://appserver2.ctt.pt/femgu/app/open/enroll/showUserEnrollAction.jspx?lang=def&redirect=" id="nav-user-register">Faça o seu registo</a>
							</h5>
							<!-- Zona de Contrato -->
															<h5>Saiba mais sobre a
									<a href="https://www.ctt.pt/particulares/area-cliente">Área de Cliente CTT</a>
								</h5>
													</div>
					</div>
				</li>
				<li id="s-nav-user-logged">
					<a id="s-nav-user-icon-user" href="https://appserver.ctt.pt/CustomerArea/ClientArea?IsFromClientAreaAndIsMobile=true">
						<div class="res-circle-container">
							<div class="res-circle">
								<div id="s-nav-userNameInitials" class="circle-txt"></div>
							</div>
						</div>
					</a>
				</li>


                			    			    			    				<li id="s-nav-search" class="has-drop" data-dropdown>
					<a id="s-nav-search-link" href="#"></a>
					<div class="nav-drop drop-search">
						<form id="header-search-form" action="https://www.ctt.pt/home/<USER>" method="get" class="open">
							<i class="ctt-icon-search"></i>
							<input type="search" id="header-search-input" name="q" class="storenav-search-input" placeholder='Insira os termos de pesquisa' autocomplete="off">
							<input type="submit" value='Pesquisa' hidden>
							<a href="#" data-nav-drop-close><i class="ctt-icon-menu-close"></i></a>
						</form>
					</div>
				</li>

			</ul>
		</nav>
	</div>

    		    <script>       window.sprChatSettings = window.sprChatSettings || {};       window.sprChatSettings = {"appId":"61b375c0ea7b18032869936b_app_1083543"};   </script>     <script>   (function(){var t=window,e=t.sprChat,a=e&&!!e.loaded,n=document,r=function(){r.m(arguments)};r.q=[],r.m=function(t){r.q.push(t)},t.sprChat=a?e:r;var o=function(){var e=n.createElement("script");e.type="text/javascript",e.async=!0,e.src="https://prod-live-chat.sprinklr.com/api/livechat/handshake/widget/"+t.sprChatSettings.appId,e.onerror=function(){t.sprChat.loaded=!1},e.onload=function(){t.sprChat.loaded=!0};var a=n.getElementsByTagName("script")[0];a.parentNode.insertBefore(e,a)};"function"==typeof e?a?e("update",t.sprChatSettings):o():"loading"!==n.readyState?o():n.addEventListener("DOMContentLoaded",o)})()   </script>
</header>



                                                                <header class="search-area light-background horizontal-section">

                    <div class="row inner-row">



<h2 class="show-for-sr">Ajuda e Suporte</h2>

<p class="h1">Ajuda e Suporte</p>



<p class="claim"><p><span>Encontre ajuda para as suas d&uacute;vidas. Pesquise ou navegue por categoria.</span></p></p>



<form class="block-form clearfix" action="/ajuda/empresas/pesquisa">

  <label for="ajuda" class="show-for-sr">Pesquisar</label>

  <input id="ajuda" class="bigger-input left" type="search" name="q" placeholder="Insira os termos de pesquisa">

  <button type="submit" class="bigger-button">Pesquisar</button>

</form>                    </div>

                </header>

                <!-- Content -->

                <div id="page-content">

                    <div class="row">

                        <div class="main-content has-sidebar">



<ul class="breadcrumbs">
                                                 <li style="display:inline" class="unavailable"><a href="javascript:void(0)">Ajuda</a></li>

                                                                                                                                                                                                                                                        <li style="display:inline"><a href="/ajuda/empresas">Empresas</a></li>

                                                                                                                                                                                                                                <li style="display:inline"><a href="/ajuda/empresas/criar-lojas-online">Criar lojas online</a></li>

                                                                                                                                                                                                <li style="display:inline" class="unavailable"><a href="javascript:void(0)">Templates</a></li>

            </ul>

                                                            <article class="article-post">

                                <header class="article-header">

                                                                                                                                                <h1>Template Learts</h1>

<div class="claim">

    <p><span data-contrast="auto" xml:lang="PT-PT" lang="PT-PT" class="TextRun SCXW134852780 BCX8"><span class="NormalTextRun SCXW134852780 BCX8">O </span><span class="NormalTextRun SCXW134852780 BCX8">template</span><span class="NormalTextRun SCXW134852780 BCX8"> </span><a href="https://learts.lojasonlinectt.pt/" target="_blank" rel="noopener"><span class="NormalTextRun SCXW134852780 BCX8">Learts</span></a><span class="NormalTextRun SCXW134852780 BCX8"> &eacute; a escolha ideal para criar uma apar&ecirc;ncia &uacute;nica para a sua loja online e destacar-se da concorr&ecirc;ncia. Este </span><span class="NormalTextRun SCXW134852780 BCX8">template</span><span class="NormalTextRun SCXW134852780 BCX8"> tem funcionalidades como os filtros de produtos, que melhoram a experi&ecirc;ncia de compra.</span></span><span class="EOP SCXW134852780 BCX8" data-ccp-props="{}">&nbsp;</span></p>

</div>

<!-- Optional image -->

<!-- End Optional Image -->

                                </header>



                                <div class="article-content">

                                                                                                                                                <div></div>

<h3>Configura&ccedil;&otilde;es de apar&ecirc;ncia</h3>
<p><strong>Definir a homepage da loja</strong></p>
<p>Pode escolher a p&aacute;gina principal da sua loja no <strong>menu Configura&ccedil;&otilde;es / Website</strong>, na sec&ccedil;&atilde;o <strong>Homepage</strong>.</p>

<h2></h2>



<ul class="small-block-grid-1 medium-block-grid-1 gallery">



<li>

  <article>

  <div class="entry-media text-center">

        <img src="/contentAsset/raw-data/46a25baf941ddd98eb4b6c9891dd367b/image/d336c26a-3403-4ed1-93b1-06cade3690f9" alt=""/>

      </div>

  <div class="entry-content">

          </div>

</article>

</a>

</li>



</ul><div></div>

<p>A homepage da loja online pode ser a p&aacute;gina <strong>Home</strong>, uma p&aacute;gina nativa (ex. <strong>Todas as marcas</strong>) ou de conte&uacute;do personalizado, que tenha criado no <strong>menu P&aacute;ginas</strong>, uma categoria ou uma p&aacute;gina de produto.</p>
<p>A galeria de imagens, as categorias em destaque e os blocos de destaque s&atilde;o apresentados se tiver configurado a op&ccedil;&atilde;o <strong>Home</strong> como p&aacute;gina principal da loja online.</p>
<p><strong>Esquema de cores, Log&oacute;tipo, Favicon, Background e Dark mode</strong></p>
<p>No <strong>menu</strong> <strong>Apar&ecirc;ncia / Personaliza&ccedil;&atilde;o</strong>, na sec&ccedil;&atilde;o <strong>Customiza&ccedil;&atilde;o</strong>, pode definir diferentes aspetos relacionados com a apar&ecirc;ncia da loja online:</p>
<ul>
<li><strong>Cor base:</strong> esta cor &eacute; utilizada em diferentes zonas da loja, como o cabe&ccedil;alho onde surge o an&uacute;ncio de loja e os perfis de redes sociais, nas etiquetas de promo&ccedil;&atilde;o e novidades, percentagens de desconto e bot&otilde;es de a&ccedil;&atilde;o</li>
<li><strong>Cor secund&aacute;ria:</strong> esta cor &eacute; utilizada em diferentes textos de elementos da loja como o an&uacute;ncio de loja, bot&otilde;es de a&ccedil;&atilde;o, etiquetas, entre outros. Recomendamos que a cor secund&aacute;ria contraste com a cor base escolhida</li>
<li><strong>Background:</strong> defina o preenchimento do fundo da loja, sendo poss&iacute;vel optar por adicionar uma imagem, uma cor ou nenhum background</li>
<li><strong>Logotipo:</strong> adicione o log&oacute;tipo da sua marca e ajuste a sua altura</li>
<li><strong>Favicon:</strong> personalize o &iacute;cone que surge no separador da loja online no browser. Por defeito, todas as lojas surgem com o &iacute;cone &ldquo;ctt&rdquo; como favicon</li>
<li><strong>Dark mode:</strong> ative este modo se pretende uma apar&ecirc;ncia com tons mais escuros, garantindo o contraste necess&aacute;rio para uma boa legibilidade dos conte&uacute;dos</li>
</ul>

<h2></h2>



<ul class="small-block-grid-1 medium-block-grid-1 gallery">



<li>

  <article>

  <div class="entry-media text-center">

        <img src="/contentAsset/raw-data/ed7309fd206f6045f3bb744c18380a44/image/e52d4b8a-6ef0-4777-8374-4a06edc8da5a" alt=""/>

      </div>

  <div class="entry-content">

          </div>

</article>

</a>

</li>



</ul><div></div>

<p><strong>Tipografia</strong></p>
<p>No <strong>menu Apar&ecirc;ncia / Personaliza&ccedil;&atilde;o</strong>, na sec&ccedil;&atilde;o <strong>Tipografia</strong>, pode alterar os tipos de letra utilizados neste template. Pode escolher a tipografia dos <strong>t&iacute;tulos</strong> (ex. t&iacute;tulos de p&aacute;ginas e de produtos), <strong>corpo</strong> (ex. descri&ccedil;&atilde;o de produtos) e <strong>textos</strong> <strong>decorativos</strong> (ex. texto de boas-vindas).</p>

<h2></h2>



<ul class="small-block-grid-1 medium-block-grid-1 gallery">



<li>

  <article>

  <div class="entry-media text-center">

        <img src="/contentAsset/raw-data/93eeb6a45e0779bab50f316e8f2be024/image/119e6efe-9020-42c3-a0e6-3d5281118e6c" alt=""/>

      </div>

  <div class="entry-content">

          </div>

</article>

</a>

</li>



</ul><div></div>

<p><strong>Configurar o An&uacute;ncio de loja</strong></p>
<p>O an&uacute;ncio de loja surge no cabe&ccedil;alho e serve para destacar informa&ccedil;&otilde;es sobre a loja, como a oferta de portes de envio ou uma campanha promocional em vigor.</p>
<p>Pode configurar o an&uacute;ncio de loja no <strong>menu Configura&ccedil;&otilde;es / Website</strong>, na sec&ccedil;&atilde;o <strong>Informa&ccedil;&otilde;es e avisos</strong>, no campo <strong>An&uacute;ncio de loja</strong> (m&aacute;ximo 255 caracteres).</p>

<h2></h2>



<ul class="small-block-grid-1 medium-block-grid-1 gallery">



<li>

  <article>

  <div class="entry-media text-center">

        <img src="/contentAsset/raw-data/a283bcdcb48222990f11bb80682f8d18/image/b00278fc-0fd9-47fd-af2a-2ac4bca8be64" alt=""/>

      </div>

  <div class="entry-content">

          </div>

</article>

</a>

</li>



</ul><div></div>

<p><strong>Galeria de imagens</strong></p>
<p>No <strong>menu Apar&ecirc;ncia / Personaliza&ccedil;&atilde;o</strong>, na sec&ccedil;&atilde;o <strong>Galeria de imagens</strong>, pode configurar o slideshow da loja online. Pode escolher entre dois tipos de galeria:</p>
<ul>
<li><strong>Produtos: </strong>O slideshow ser&aacute; composto por produtos com o atributo <strong>Produto &eacute; destaque</strong>, definido na ficha de produto na sec&ccedil;&atilde;o <strong>Estado do Produto</strong>, no <strong>menu Produtos</strong>. <a href="/ajuda/empresas/criar-lojas-online/produtos/produtos-em-destaque">Saiba mais</a></li>
<li><strong>Imagens: </strong>Pode adicionar at&eacute; 6 imagens personalizadas, a partir do ambiente local ou do <strong>menu Media</strong>. Para cada imagem pode atribuir um <strong>t&iacute;tulo</strong>, <strong>descri&ccedil;&atilde;o</strong>, <strong>bot&atilde;o</strong> e <strong>link</strong>. <a href="/ajuda/empresas/criar-lojas-online/paginas-personalizadas/galeria-de-imagens">Saiba mais</a></li>
</ul>

<h2></h2>



<ul class="small-block-grid-1 medium-block-grid-1 gallery">



<li>

  <article>

  <div class="entry-media text-center">

        <img src="/contentAsset/raw-data/50c429618373aa258ba2fe533f868902/image/04167520-956b-4e1d-ac8f-fdc657ce3c21" alt=""/>

      </div>

  <div class="entry-content">

          </div>

</article>

</a>

</li>



</ul><div></div>

<p><strong>Categorias em destaque</strong></p>
<p>Se escolher como homepage a p&aacute;gina <strong>Home</strong>, v&atilde;o surgir em destaque at&eacute; 4 categorias principais.</p>

<h2></h2>



<ul class="small-block-grid-1 medium-block-grid-1 gallery">



<li>

  <article>

  <div class="entry-media text-center">

        <img src="/contentAsset/raw-data/5d983872c8f2dfc27e74fdcd9f589d99/image/645fb2b2-5871-4d42-8c56-ab8c934fc035" alt=""/>

      </div>

  <div class="entry-content">

          </div>

</article>

</a>

</li>



</ul><div></div>

<p>As categorias s&atilde;o apresentadas de acordo com o crit&eacute;rio de ordena&ccedil;&atilde;o selecionado no <strong>menu Configura&ccedil;&otilde;es / Website</strong>, na sec&ccedil;&atilde;o <strong>Op&ccedil;&otilde;es</strong> e na op&ccedil;&atilde;o <strong>Ordena&ccedil;&atilde;o de categorias</strong>.</p>
<p><strong>Texto de boas-vindas</strong></p>
<p>O t&iacute;tulo de boas-vindas &eacute; preenchido com o nome da loja definido no <strong>menu Configura&ccedil;&otilde;es / Geral</strong>, na sec&ccedil;&atilde;o <strong>Informa&ccedil;&otilde;es de loj</strong>a, no campo <strong>Nome da loja</strong>.</p>
<p>Abaixo do texto de boas-vindas surge a descri&ccedil;&atilde;o da loja definida no mesmo menu, no campo <strong>Descri&ccedil;&atilde;o</strong>.</p>
<p>O <strong>t&iacute;tulo de boas-vindas</strong> s&oacute; &eacute; apresentado na homepage se a <strong>Descri&ccedil;&atilde;o</strong> estiver configurada no <strong>menu Configura&ccedil;&otilde;es / Geral / Informa&ccedil;&otilde;es de loja</strong>.</p>

<h2></h2>



<ul class="small-block-grid-1 medium-block-grid-1 gallery">



<li>

  <article>

  <div class="entry-media text-center">

        <img src="/contentAsset/raw-data/8f7ebced26a7c182379f671ed773b5be/image/2516e7ec-6134-4e07-bab2-05d432b4e4ee" alt=""/>

      </div>

  <div class="entry-content">

          </div>

</article>

</a>

</li>



</ul><div></div>

<p><strong>Produtos em Destaque, Novidades, Promo&ccedil;&otilde;es e Mais vendidos</strong></p>
<p>Quando a p&aacute;gina <strong>Home</strong> &eacute; a p&aacute;gina principal da loja online, os produtos s&atilde;o destacados numa sec&ccedil;&atilde;o em fun&ccedil;&atilde;o dos seus atributos ou hist&oacute;rico de vendas.</p>
<p><strong>Produtos em destaque</strong></p>
<p>Abaixo das categorias em destaque, surge aleatoriamente e de forma rotativa um produto com o atributo <strong>Produto &eacute; destaque</strong> configurado.</p>
<p>Os restantes produtos com este atributo surgem na sec&ccedil;&atilde;o abaixo designada por <strong>Destaques</strong>.</p>

<h2></h2>



<ul class="small-block-grid-1 medium-block-grid-1 gallery">



<li>

  <article>

  <div class="entry-media text-center">

        <img src="/contentAsset/raw-data/9a929d5f24092d4929939eb7530e1cba/image/1191bb4a-5fc6-46ff-8c61-c0fb5c61d889" alt=""/>

      </div>

  <div class="entry-content">

          </div>

</article>

</a>

</li>



</ul><div></div>

<p><strong>Novidades</strong></p>
<p>Os produtos com o atributo <strong>Produto &eacute; novidade</strong> s&atilde;o apresentados na sec&ccedil;&atilde;o designada por <strong>Novidades</strong>. Os produtos s&atilde;o ordenados de acordo com a data de cria&ccedil;&atilde;o (do mais recente para o mais antigo).</p>
<p><strong>Promo&ccedil;&otilde;es</strong></p>
<p>Os produtos com o atributo <strong>Produto</strong> <strong>&eacute; promo&ccedil;&atilde;o</strong>, definido na ficha do produto ou por via da configura&ccedil;&atilde;o de uma <a href="/ajuda/empresas/criar-lojas-online/gestao-de-encomendas/campanhas">campanha promocional</a> quando a op&ccedil;&atilde;o <strong>Adicionar produtos &agrave; p&aacute;gina promo&ccedil;&otilde;es</strong> for selecionada, surgem na sec&ccedil;&atilde;o <strong>Promo&ccedil;&otilde;es</strong>. Estes artigos s&atilde;o tamb&eacute;m apresentados na p&aacute;gina nativa das <a href="https://learts.lojasonlinectt.pt/sales" target="_blank" rel="noopener">promo&ccedil;&otilde;es</a>.</p>
<p><strong>Mais vendidos</strong></p>
<p>Na sec&ccedil;&atilde;o <strong>Mais vendidos</strong> s&atilde;o apresentados os produtos com mais vendas. Se n&atilde;o existirem vendas, &eacute; apresentada uma listagem de produtos da loja.</p>
<p>A quantidade inicial de produtos apresentados por defeito nestas sec&ccedil;&otilde;es &eacute; definida no campo <strong>Nr de produtos p&aacute;gina Home</strong>, no <strong>menu Configura&ccedil;&otilde;es / Website</strong>. Pode consultar mais artigos em cada listagem ao clicar no bot&atilde;o <strong>+ Carregar mais</strong>.</p>

<h2></h2>



<ul class="small-block-grid-1 medium-block-grid-1 gallery">



<li>

  <article>

  <div class="entry-media text-center">

        <img src="/contentAsset/raw-data/0d04d2b4025eabd464e6faf72873012b/image/df3bf0ef-be79-42cd-9a6c-8bc9bc8f666c" alt=""/>

      </div>

  <div class="entry-content">

          </div>

</article>

</a>

</li>



</ul><div></div>

<p><strong>O nosso Blog</strong></p>
<p>Nesta sec&ccedil;&atilde;o &eacute; apresentado um carrossel com artigos publicados no Blog da loja online, ordenados pela data de publica&ccedil;&atilde;o. Pode criar blogposts na &aacute;rea de gest&atilde;o da loja, no <a href="/ajuda/empresas/criar-lojas-online/paginas-personalizadas/gerir-o-blog"><strong>menu Blog</strong></a>.</p>
<p><strong>As nossas marcas</strong></p>
<p>Nesta sec&ccedil;&atilde;o &eacute; apresentado um carrossel com os log&oacute;tipos das marcas da loja. Pode criar as marcas da loja no <strong>menu Produtos / Marcas</strong>.</p>
<p><strong>Blocos de destaque</strong></p>
<p>Os blocos de destaque s&atilde;o sec&ccedil;&otilde;es com informa&ccedil;&atilde;o relevante que pode colocar em destaque na homepage da loja online.</p>
<p>Pode configurar os blocos de destaque no <strong>menu Apar&ecirc;ncia / Personaliza&ccedil;&atilde;o</strong> na sec&ccedil;&atilde;o <strong>Blocos de Destaque</strong>. Pode adicionar at&eacute; 3 blocos de destaque com um <strong>t&iacute;tulo</strong>, uma <strong>descri&ccedil;&atilde;o</strong> e um <strong>&iacute;cone</strong>.</p>

<h2></h2>



<ul class="small-block-grid-1 medium-block-grid-1 gallery">



<li>

  <article>

  <div class="entry-media text-center">

        <img src="/contentAsset/raw-data/a28dc8c86dcc3f7ceccbc46112875cb0/image/074638eb-9222-40a8-8573-be884eab384b" alt=""/>

      </div>

  <div class="entry-content">

          </div>

</article>

</a>

</li>



</ul><div></div>

<p><strong>Newsletter</strong></p>
<p>Pode adicionar um bloco para a inscri&ccedil;&atilde;o na newsletter da loja online na zona do rodap&eacute;. Basta ativar a <strong>app Newsletter</strong>, dispon&iacute;vel em <strong>Apps / Marketing</strong>.</p>
<p><strong>Social</strong></p>
<p>Pode configurar os links dos perfis nas redes sociais <strong>Youtube</strong>, <strong>Facebook</strong>, <strong>TikTok</strong>, <strong>Twitter</strong>, <strong>Linkedin</strong>, <strong>Pinterest</strong> e <strong>Instagram</strong> no <strong>menu Configura&ccedil;&otilde;es / Geral</strong>, na sec&ccedil;&atilde;o <strong>Social</strong>.</p>
<p>Os links v&atilde;o estar hiperligados em &iacute;cones que surgem no cabe&ccedil;alho e no rodap&eacute; de todas as p&aacute;ginas da tua loja online.</p>
<p><strong>Contactos</strong></p>
<p>Os contactos definidos no <strong>menu Configura&ccedil;&otilde;es / Geral</strong>, na sec&ccedil;&atilde;o <strong>Contactos de loja</strong>, s&atilde;o apresentados na p&aacute;gina de contactos da loja online.</p>
<p><strong>Texto de rodap&eacute;</strong></p>
<p>Pode definir o texto apresentado no rodap&eacute; no <strong>menu Configura&ccedil;&otilde;es / Website</strong>, na sec&ccedil;&atilde;o Informa&ccedil;&otilde;es e avisos, no campo Texto de rodap&eacute;.</p>
<p><strong>Menus de navega&ccedil;&atilde;o</strong></p>
<p>No <strong>menu Apar&ecirc;ncia / Personaliza&ccedil;&atilde;o</strong>, na sec&ccedil;&atilde;o <strong>Navega&ccedil;&atilde;o</strong>, pode configurar os <a href="/ajuda/empresas/criar-lojas-online/configuracao/menus-navegacao">menus de navega&ccedil;&atilde;o</a> da loja online.</p>

<h2></h2>



<ul class="small-block-grid-1 medium-block-grid-1 gallery">



<li>

  <article>

  <div class="entry-media text-center">

        <img src="/contentAsset/raw-data/e3b852aa209f10e75da192b928f17b39/image/166d4543-ff9f-44cd-bdb8-ee3bf3ba9354" alt=""/>

      </div>

  <div class="entry-content">

          </div>

</article>

</a>

</li>



</ul><div></div>

<p>Os menus de navega&ccedil;&atilde;o principal ficam dispon&iacute;veis na barra de cabe&ccedil;alho, ao clicar no menu hamb&uacute;rguer. Os menus de navega&ccedil;&atilde;o secund&aacute;ria ficam dispon&iacute;veis no rodap&eacute; da loja.</p>
<p><strong>Personaliza&ccedil;&atilde;o avan&ccedil;ada por c&oacute;digo</strong></p>
<p>Pode personalizar o template com a coloca&ccedil;&atilde;o de c&oacute;digo personalizado nas sec&ccedil;&otilde;es de HTML, CSS e Javascript no <strong>menu Apar&ecirc;ncia / Avan&ccedil;ado</strong>.</p>
<p>Se pretender editar ficheiros de c&oacute;digo do template, pode clonar o mesmo ao clicar no &iacute;cone dispon&iacute;vel no detalhe de cada template, no <strong>menu Apar&ecirc;ncia / Templates</strong>. Ao clonar o template, o mesmo deixar&aacute; de receber atualiza&ccedil;&otilde;es autom&aacute;ticas.</p>

<h2></h2>



<ul class="small-block-grid-1 medium-block-grid-1 gallery">



<li>

  <article>

  <div class="entry-media text-center">

        <img src="/contentAsset/raw-data/82511d40c6d7acb73c551a797d507cb1/image/e58ad843-7b1e-479d-8afd-590966044627" alt=""/>

      </div>

  <div class="entry-content">

          </div>

</article>

</a>

</li>



</ul><div></div>

<p>Consulte a documenta&ccedil;&atilde;o para programadores com informa&ccedil;&otilde;es adicionais sobre os templates.</p>
<h3>Funcionalidades</h3>
<p><strong>Vista r&aacute;pida de produtos</strong></p>
<p>No <strong>menu Apar&ecirc;ncia / Personaliza&ccedil;&atilde;o</strong>, na sec&ccedil;&atilde;o <strong>Navega&ccedil;&atilde;o</strong>, pode optar por mostrar o menu Todos os produtos.</p>

<h2></h2>



<ul class="small-block-grid-1 medium-block-grid-1 gallery">



<li>

  <article>

  <div class="entry-media text-center">

        <img src="/contentAsset/raw-data/8e62f6b68880632630cff64b03d0657d/image/d0712e05-3227-45cf-a5dd-8614fcf2802c" alt=""/>

      </div>

  <div class="entry-content">

          </div>

</article>

</a>

</li>



</ul><div></div>

<p>Na vista r&aacute;pida &eacute; poss&iacute;vel consultar informa&ccedil;&otilde;es como s&atilde;o exemplo o t&iacute;tulo do produto, as categorias, a marca, o pre&ccedil;o, informa&ccedil;&atilde;o sobre campanhas, a descri&ccedil;&atilde;o, o stock dispon&iacute;vel, as tags atribu&iacute;das e campos com <a href="/ajuda/empresas/criar-lojas-online/produtos/informacao-adicional">informa&ccedil;&atilde;o adicional</a>. Tamb&eacute;m &eacute; poss&iacute;vel fazer o upload de ficheiros, adicionar o produto &agrave; wishlist e ao carrinho de compras.</p>
<p><br /><strong>Op&ccedil;&otilde;es de visualiza&ccedil;&atilde;o nas listagens</strong></p>
<p>No template Learts um visitante pode personalizar a visualiza&ccedil;&atilde;o dos produtos nas listagens, escolhendo o <strong>n&uacute;mero de colunas</strong> (apenas dispon&iacute;vel em desktop) e os <strong>crit&eacute;rios de ordena&ccedil;&atilde;o</strong>.</p>
<ul>
<li><strong>N&uacute;mero de colunas:</strong> o visitante pode optar por visualizar a listagem de produtos organizados em <strong>5</strong>, <strong>4</strong> ou <strong>3</strong> colunas;</li>
<li><strong>Crit&eacute;rio de ordena&ccedil;&atilde;o:</strong> o visitante pode ordenar os produtos por <strong>relev&acirc;ncia</strong>, <strong>t&iacute;tulo</strong>, <strong>mais recentes</strong>, <strong>mais vendidos</strong>, <strong>mais baratos</strong>, <strong>mais caros</strong>, <strong>mais stock</strong>, <strong>menos stock</strong>.</li>
</ul>

<h2></h2>



<ul class="small-block-grid-1 medium-block-grid-1 gallery">



<li>

  <article>

  <div class="entry-media text-center">

        <img src="/contentAsset/raw-data/9d6ab00bcfeb2ebe56bba3fee15771b6/image/16e7bc97-e6cf-42b7-90a1-0e7a4eb7296c" alt=""/>

      </div>

  <div class="entry-content">

          </div>

</article>

</a>

</li>



</ul><div></div>

<p>No <strong>menu Configura&ccedil;&otilde;es / Website</strong>, na sec&ccedil;&atilde;o <strong>Op&ccedil;&otilde;es</strong>, pode definir a ordena&ccedil;&atilde;o que os produtos, categorias e marcas assumem por defeito nas listagens dispon&iacute;veis.</p>
<p>A ordena&ccedil;&atilde;o dos produtos ir&aacute; afetar:</p>
<ul>
<li>Menu <a href="https://learts.lojasonlinectt.pt/catalog" target="_blank" rel="noopener">Todos os produtos</a></li>
<li>P&aacute;ginas de categorias</li>
<li>P&aacute;gina de tags</li>
<li>P&aacute;gina com as novidades</li>
<li>P&aacute;gina com as promo&ccedil;&otilde;es</li>
</ul>
<p>A ordena&ccedil;&atilde;o das categorias ir&aacute; afetar:</p>
<ul>
<li>Menu <a href="https://learts.lojasonlinectt.pt/categories" target="_blank" rel="noopener">Todas as categorias</a></li>
<li>Sec&ccedil;&atilde;o com categorias principais na homepage da loja online</li>
<li>P&aacute;ginas de categorias com subcategorias e sub-subcategorias.</li>
</ul>
<p>A ordena&ccedil;&atilde;o por <strong>Posi&ccedil;&atilde;o</strong> permite definir a posi&ccedil;&atilde;o que cada produto, categoria ou marca assume nas listagens. A atribui&ccedil;&atilde;o da posi&ccedil;&atilde;o deve ser feita atrav&eacute;s do campo <strong>Posi&ccedil;&atilde;o</strong>, vis&iacute;vel na ficha de cada produto, categoria ou marca, no <strong>menu Produtos</strong>.</p>
<p><strong>Filtros de produtos</strong></p>
<p>Os filtros de produtos est&atilde;o dispon&iacute;veis em todas as p&aacute;ginas da loja com listas de produtos. Existem v&aacute;rios tipos de filtros baseados nas caracter&iacute;sticas dos produtos que podem ser aplicados nas listagens:</p>
<ul>
<li>Pre&ccedil;os</li>
<li>Stock</li>
<li>Promo&ccedil;&atilde;o</li>
<li>Categorias</li>
<li>Marcas</li>
<li>Tags</li>
<li>Informa&ccedil;&atilde;o adicional</li>
</ul>

<h2></h2>



<ul class="small-block-grid-1 medium-block-grid-1 gallery">



<li>

  <article>

  <div class="entry-media text-center">

        <img src="/contentAsset/raw-data/74558c682435575d6bccfb3092522e5f/image/fd691898-b153-47cd-967e-4be9096a47d5" alt=""/>

      </div>

  <div class="entry-content">

          </div>

</article>

</a>

</li>



</ul><div></div>

<p>Na listagem de filtros apenas s&atilde;o apresentados filtros que podem ser aplicados &agrave; lista de produtos que est&aacute; a ser consultada. Para aplicar filtros nas listagens que est&aacute; a consultar, deve clicar em <strong>Filtros</strong>.</p>
<p>Saiba mais sobre a configura&ccedil;&atilde;o e utiliza&ccedil;&atilde;o dos <a href="/ajuda/empresas/criar-lojas-online/produtos/filtros-de-produtos">filtros de produtos</a>.</p>
<h3>Dimens&otilde;es das imagens</h3>
<p>As dimens&otilde;es ideais das imagens podem variar consoante o template utilizado. Para o template Learts, estas s&atilde;o as dimens&otilde;es recomendadas.</p>
<p><strong>Galeria de imagens</strong></p>
<p>A largura do slideshow varia consoante o tamanho do ecr&atilde; a partir do qual a loja &eacute; acedida. A altura &eacute; fixa e a dimens&atilde;o varia consoante o tipo de ecr&atilde;:</p>
<ul>
<li><strong>Vista em desktop:</strong> 600px (450px em ecr&atilde;s de menor dimens&atilde;o)</li>
<li><strong>Vista em tablet:</strong> 350px</li>
</ul>
<p>O slideshow n&atilde;o &eacute; apresentado em smartphones.</p>
<p><strong>Imagens de produtos e categorias</strong></p>
<p>Na listagens de produtos e categorias as imagens utilizadas s&atilde;o redimensionadas para surgirem com as dimens&otilde;es <strong>400x400px</strong>.</p>
<p>Na p&aacute;gina de detalhes de um produto e no produto em destaque na homepage, as imagens mant&ecirc;m a propor&ccedil;&atilde;o original, com a largura m&aacute;xima fixa em <strong>570px</strong>.</p>
<p><strong>Imagens de marcas</strong></p>
<p>Se a homepage for a p&aacute;gina <strong>Home</strong>, &eacute; apresentada uma sec&ccedil;&atilde;o com as marcas. Nesta sec&ccedil;&atilde;o, a altura da imagem das marcas &eacute; fixa em <strong>40px</strong>. A largura &eacute; vari&aacute;vel, mas nunca superior a <strong>60px</strong>.</p>
<p>Na p&aacute;gina <strong>Todas as marcas</strong>, a altura &eacute; fixa em <strong>60px</strong>. A largura &eacute; vari&aacute;vel, mas nunca superior a <strong>240px</strong>.</p>

<style>

    .tools-layout .entry-list .entry-wrapper .entry-post .entry-content:before{

        display:none;

    }

</style>                                </div>

                            </article>

                        </div><!-- main -->

                        <aside class="sidebar">

                            <section class="sidebar-nav">

                                                                                                                                <h5>Templates</h5>

<ul id="veja_tambem" gtm-menu="5">

                                        <li><a href="/ajuda/empresas/criar-lojas-online/templates/templates">Templates</a></li>

                                                            <li><a href="/ajuda/empresas/criar-lojas-online/templates/template-bold">Template Bold</a></li>

                                                            <li><a href="/ajuda/empresas/criar-lojas-online/templates/template-boxie">Template Boxie</a></li>

                                                            <li><a href="/ajuda/empresas/criar-lojas-online/templates/template-default">Template Default</a></li>

                                                            <li><a href="/ajuda/empresas/criar-lojas-online/templates/template-eve">Template Eve</a></li>

                                                            <li class="active"><a href="/ajuda/empresas/criar-lojas-online/templates/template-learts">Template Learts</a></li>

                                                            <li><a href="/ajuda/empresas/criar-lojas-online/templates/template-minimal">Template Minimal</a></li>

                                                            <li><a href="/ajuda/empresas/criar-lojas-online/templates/template-mosaic">Template Mosaic</a></li>

                                                            <li><a href="/ajuda/empresas/criar-lojas-online/templates/template-parallax">Template Parallax</a></li>

                                                            <li><a href="/ajuda/empresas/criar-lojas-online/templates/template-shopper">Template Shopper</a></li>

                        </ul>                            </section>

                        </aside>

                    </div><!--row-->

                </div><!-- page-content-->







<style>
	#footer-container-language a {
		color:#3b3b3b;
		font-size:1rem;
		cursor:pointer;
		margin-right:25px;
	}

	#footer-container-language a:hover {
		cursor:pointer;
	}

	#footer-container-language a.active {
		font-weight:bold;
		text-decoration: underline;
	}

	#footer-container-language {
		border-left:3px solid #eaeaea;
		padding-left:30px;
		padding-top:10px;
		padding-bottom:10px;
		left:122px;
		top:104px;
		position:relative;
		z-index:10;
	}

	.footer-app .badge-huawei {
		display: inline-block;
		text-indent: -9999px;
		height: 44px;
		margin-right: 8px;
	}

	.footer-app .badge-huawei-en,
	.footer-app .badge-google-en,
	.footer-app .badge-apple-en {
		display: inline-block;
		text-indent: -9999px;
		height: 44px;
		margin-right: 8px;
	}

	.footer-app .badge-huawei-en {
		background: url(https://www.ctt.pt/application/themes/images/badge_huawei.png) top left no-repeat;
		background-size: 100%;
		width: 141px;
	}
	.footer-app .badge-apple-en {
        background: url(https://www.ctt.pt/application/themes/images/badge-app-store-en.png) top left no-repeat;
        background-size: 100%;
        width: 141px;
    }
    .footer-app .badge-google-en {
        background: url(https://www.ctt.pt/application/themes/images/badge-google-play-en.png) top left no-repeat;
        background-size: 100%;
        width: 141px;
    }

	.footer-app .badge-huawei {
		background: url(https://www.ctt.pt/application/themes/images/badge_huawei.png) top left no-repeat;
		background-size: 100%;
		width: 141px;
	}
	.footer-app .badge-apple {
        background: url(https://www.ctt.pt/application/themes/images/badge-app-store.png) top left no-repeat;
        background-size: 100%;
    }
    .footer-app .badge-google {
        background: url(https://www.ctt.pt/application/themes/images/badge-google-play.png) top left no-repeat;
        background-size: 100%;
    }

    /** Inicio do ajuste para funcionar o facebook em mobile**/
    a.footer-fb {
    	display: none;
    }

    a.footer-fb-mobile {
    	display: block;
    }

    @media only screen and (min-width: 48.063em) {
    	a.footer-fb {
    		display: block;
    	}

    	a.footer-fb-mobile {
    		display: none;
    	}
    }
    /** Fim do ajuste para funcionar o facebook em mobile**/

@media only screen and (min-width: 61.313em) {
	.footer-app {
	    width: 50%;
	}
	.footer-container-end {
        padding-top: 246px;
    }

    /** Inicio do ajuste para funcionar o facebook em mobile**/
    a.footer-fb {
		display: block;
	}

	a.footer-fb-mobile {
		display: none;
	}
    /** Fim do ajuste para funcionar o facebook em mobile**/
}
</style>

<footer id="page-footer" class="small-container">
	<div class="horse">

	<div class="footer-container-logo">
		<div id="footer-container-language">
																																																													<a class="active" href="/ajuda/empresas/criar-lojas-online/templates/template-learts?com.dotmarketing.htmlpage.language=3">PT</a>
					<a href="/ajuda/empresas/criar-lojas-online/templates/template-learts?language_id=1">EN</a>

					</div>

		<div class="column">
		    <a href="https://www.ctt.pt/particulares/index?com.dotmarketing.htmlpage.language=3"><img src="https://www.ctt.pt/application/themes/images/logo-ctt.svg" /></a>
		</div>
	</div>

	<div class="footer-container-nav">
	    <!-- Footer Links for Mobile -->
		<dl class="accordion column show-for-small" data-accordion>
	    	    	    	    	    			<dd class="accordion-navigation">
		        		                		<a href="#panel1b">Marcas</a>
            		<div id="panel1b" class="content">
                		<ul gtm-menu="6">
            				                			                    			                        			            <li><a target="_blank" href="https://www.bancoctt.pt">Banco CTT</a></li>
                			                        				            				                			                    			                        			            <li><a target="_blank" href="https://www.cttexpress.com/">CTT Express</a></li>
                			                        				            				                			                    			                        			            <li><a href="https://www.ctt.pt/particulares/pagamentos/">Payshop</a></li>
                			                        				            				                			                    			                        			            <li><a target="_blank" href="https://locky.pt/">Locky</a></li>
                			                        				            				            			</ul>
            		</div>
            			    </dd>
			<dd class="accordion-navigation">
		        		                		<a href="#panel2b">Informação</a>
            		<div id="panel2b" class="content">
                		<ul gtm-menu="6">
            				            				                    			                        			            <li><a href="https://www.ctt.pt/home/<USER>/index">Política de Privacidade</a></li>
                			                        			                				            				                    			                        			            <li><a href="https://www.ctt.pt/home/<USER>">Política de Cookies</a></li>
                			                        			                				            				                    			                        			            <li><a href="https://www.ctt.pt/home/<USER>/index">Termos e Condições</a></li>
                			                        			                				            				                    			                        			            <li><a href="https://www.ctt.pt/home/<USER>">Resolução Alternativa de Litígios de Consumo</a></li>
                			                        			                				            				                    			                        			            <li><a href="https://www.ctt.pt/home/<USER>/">Oferta de acesso</a></li>
                			                        			                				            				                    			                        			            <li><a href="https://www.ctt.pt/transversais/lista-de-precos-de-correio">Lista de preços de correio</a></li>
                			                        			                				            				                    			                        			            <li><a href="https://www.ctt.pt/home/<USER>/condicoes-gerais-de-acesso-e-utilizacao-do-servico-universal">Serviço Universal</a></li>
                			                        			                				            			</ul>
            			            		</div>
		    </dd>
		    		    		    		    			<dd class="accordion-navigation footer-app">
			<a href="#panel3b">APP CTT</a>
			<div id="panel3b" class="content">
			            	            	            	            	    			    	<p>Faça já o download da nova versão da app CTT e aceda aos nossos diversos serviços!</p>

									<a class="badge-apple" target="_blank" href='https://apps.apple.com/pt/app/ctt-correios-de-portugal/id6443922891'>Descarregar na App Store</a>
					<a class="badge-google" target="_blank" href='https://play.google.com/store/apps/details?id=pt.ctt.outsystems.CTT'>Disponível no Google Play</a>
					<a class="badge-huawei" target="_blank" href='https://appgallery.huawei.com/#/app/C107245241'>Disponível na Huawei App Gallery</a>
							</div>
			</dd>
		</dl>
		<!-- Footer Links for Large Screens -->

		<ul class="footer-nav">

			<li class="footer-brands">
    			        	            	            	            	            		                			<h6>Marcas</h6>
            			<ul gtm-menu="6">
            				                			                    			                        			            <li><a href="https://www.bancoctt.pt" target="_blank" target="_blank">Banco CTT</a></li>
                			                        			                				                			                    			                        			            <li><a href="https://www.cttexpress.com/" target="_blank" target="_blank">CTT Express</a></li>
                			                        			                				                			                    			                        			            <li><a href="https://www.ctt.pt/particulares/pagamentos/">Payshop</a></li>
                			                        			                				                			                    			                        			            <li><a href="https://locky.pt/" target="_blank" target="_blank">Locky</a></li>
                			                        			                				            			</ul>
        		        		        	            	            	        			</li>


			<li class="footer-legal">
			            	            	            	            	            		                			<h6>Informação</h6>
            			<ul gtm-menu="6">
            			                			                        			                        			            <li><a href="https://www.ctt.pt/home/<USER>/index">Política de Privacidade</a></li>
                			                        			                				            			                        			                        			            <li><a href="https://www.ctt.pt/home/<USER>">Política de Cookies</a></li>
                			                        			                				            			                        			                        			            <li><a href="https://www.ctt.pt/home/<USER>/index">Termos e Condições</a></li>
                			                        			                				            			                        			                        			            <li><a href="https://www.ctt.pt/home/<USER>">Resolução Alternativa de Litígios de Consumo</a></li>
                			                        			                				            			                        			                        			            <li><a href="https://www.ctt.pt/home/<USER>/">Oferta de acesso</a></li>
                			                        			                				            			                        			                        			            <li><a href="https://www.ctt.pt/transversais/lista-de-precos-de-correio">Lista de preços de correio</a></li>
                			                        			                				            			                        			                        			            <li><a href="https://www.ctt.pt/home/<USER>/condicoes-gerais-de-acesso-e-utilizacao-do-servico-universal">Serviço Universal</a></li>
                			                        			                				            			</ul>
            		            	    	            	            	        			</li>


			<li class="footer-app">
			<h6>App CTT</h6>
			            	            	            	            	        				<p>Faça já o download da nova versão da app CTT e aceda aos nossos diversos serviços!</p>

				    				<a class="badge-apple" target="_blank" href='https://apps.apple.com/pt/app/ctt-correios-de-portugal/id6443922891'>Descarregar na App Store</a>
    				<a class="badge-google" target="_blank" href='https://play.google.com/store/apps/details?id=pt.ctt.outsystems.CTT'>Disponível no Google Play</a>
					<a class="badge-huawei" target="_blank" href='https://appgallery.huawei.com/#/app/C107245241'>Disponível na Huawei App Gallery</a>
							</li>
		</ul>
	</div>

	<div class="footer-container-end">
		<div class="footer-footnotes column">
			<ul class="footer-social-links">
				<li>
					<a class="footer-fb">
						<i class="ctt-icon-facebook"></i>
					</a>
					<ul class="footer-social-hover">
						<li><a href="https://www.facebook.com/CTTCorreiosdePortugal" target="_blank">CTT</a></li>
						<li><a href="https://www.facebook.com/CTTEmpresas/" target="_blank">CTT Empresas</a></li>
						<li><a href="https://www.facebook.com/FilateliaCTT" target="_blank">Filatelia CTT</a></li>
						<li><a href="https://www.facebook.com/opainatalsolidario/" target="_blank">Pai Natal Solidário</a></li>
					</ul>
																				<a class="footer-fb-mobile" href="https://www.facebook.com/CTTCorreiosdePortugal" target="_blank">
                    	<i class="ctt-icon-facebook"></i>
                    </a>
                                                            				</li>
				<li>
					<a href="https://www.instagram.com/cttportugal/" class="footer-ig" target="_blank">
						<i class="ctt-icon-instagram"></i>
					</a>
				</li>
				<li>
					<a href="https://www.linkedin.com/company/ctt---correios-de-portugal-s-a" class="footer-li" target="_blank">
						<i class="ctt-icon-linkedin"></i>
					</a>
				</li>
				<li>
					<a href="https://www.youtube.com/channel/UC3NSecyrfNUUZ0XEI4OwU0A" class="footer-yt" target="_blank">
						<i class="ctt-icon-youtube"></i>
					</a>
				</li>
			</ul>
			<div class="copyright">© CTT 2021</div>

            <div class="logos logos-right"  style="margin-left: -8px;">
			   <a href="https://elogiar.livrodeelogios.com/elogiar/ctt-correios-de-portugal" target="_blank">
				   <img src="https://www.ctt.pt/application/themes/images/footer_logos/LE-BT-Preto.png" alt="Livro Elogios" class="bw">
				   <img src="https://www.ctt.pt/application/themes/images/footer_logos/LE-BT-Branco2x.png" alt="Livro Elogios" class="hover">
			   </a>
		    </div>

			<div class="logos logos-right">
				<a href="https://livroreclamacoes.pt/" target="_blank">
					<img src="https://www.ctt.pt/application/themes/images/footer_logos/livro_reclamacoes_bw.png" alt="livroreclamacoes" class="logo-complaints bw">
					<img src="https://www.ctt.pt/application/themes/images/footer_logos/livro_reclamacoes.png" alt="livroreclamacoes" class="logo-complaints hover">
				</a>
			</div>

		</div>
			    	    	    	    		<div class="footer-help">
			<h4><span class="text-block">Tem alguma questão?</span>Fale connosco, estamos disponíveis para o ajudar.</h4>
			<a href='https://www.ctt.pt/ajuda/index' class="button outline-button button-white">Ajuda e Contactos</a>
		</div>


	</div>

</div>

</footer>

                                            </div><!-- inner-wrap -->

        </div><!-- off-canvas -->




<script type="text/javascript" src="https://www.ctt.pt/application/themes/js/general.js?t=171126072021"></script>
<script type="text/javascript" src="https://www.ctt.pt/application/themes/js/ctt-general.js?t=145606212024"></script>
<script type="text/javascript" src="https://www.ctt.pt/application/themes/js/ctt-onetrust.js?t=154507072023"></script>

<!--[if lte IE 9]>
  <script type="text/javascript" src="https://www.ctt.pt/application/themes/js/jquery.placeholder.min.js" ></script>
<![endif]-->



<script>
	$(document).ready(function(){
		window.dataLayer = window.dataLayer || [];
		$("[gtm-menu] a").click(function(event){
		    //gets value from element in order to identify menu type
			var menuType = $(this).parents('[gtm-menu]').attr('gtm-menu');
			//variables to store globalParameters about the page
			var navLevel1, navLevel2, productGroupName, hostOwner, customerSegment, pageType;
			pageType = $("[gtm-ctt-info='page']").val() || '';
			customerSegment = $("[gtm-ctt-info='customer']").val() || '';
			hostOwner = $("[gtm-ctt-info='host']").val() || '';
			productGroupName = $("[gtm-ctt-info='product']").val() || '';

			var dataLayerObject = {
				event: 'cttMenuInteraction',
				navigationLevels: {},
				cttProductGroupName : productGroupName,
				cttHostOwner : hostOwner,
				cttCustomerSegment : customerSegment,
				cttPageType : pageType,
			}

			/*
			Menu !== 1 3 4 5 6
			*/
			if(menuType == 2){
				//in menuType 2, we need to interact differently in order to extract the information we need
				navLevel1 = $(this).parents('li').find('a').html().trim(); //gets navigation level 1 text
				navLevel2 = $(this).text().trim(); //gets navigation level 2 from clicked item
				dataLayerObject.navigationLevels = {
				    menu_type : menuType,
				    navigation_level1 : navLevel1,
				    navigation_level2 : navLevel2
				};
			}else{
				//if menuType != 2 then we interact in a different way
			    navLevel1 = $(this).text().trim(); //gets navigation level 1 text from clicked item
			    dataLayerObject.navigationLevels = {
			        menu_type : menuType,
				    navigation_level1 : navLevel1
				};
			}

			//console.log(dataLayerObject);
			event.stopPropagation(); // prevents click event from being propagated to parent elements and triggering this function twice

			window.dataLayer.push(dataLayerObject);

		});
	});
</script>
<script>
	$(document).ready(function(){
		window.dataLayer = window.dataLayer || [];
		$("ul.list-links.has-icons li a").click(function(){
			var cttLabelText = $(this).text(); //gets text from clicked item
			var cttCategory =  $(this).attr('href'); //gets category from clicked item

			var pageType = $("[gtm-ctt-info='page']").val() || '';
			var customerSegment = $("[gtm-ctt-info='customer']").val() || '';
			var hostOwner = $("[gtm-ctt-info='host']").val() || '';
			var productGroupName = $("[gtm-ctt-info='product']").val() || '';

			var dataLayerObject = {
				event: "cttReadFaq",
				cttAction: cttCategory,
				cttLabel: cttLabelText,
				cttProductGroupName: productGroupName,
				cttHostOwner: hostOwner,
				cttCustomerSegment: customerSegment,
				cttPageType: pageType,
			};

			console.log(dataLayerObject);

			window.dataLayer.push(dataLayerObject);

		});
	});
</script>
<script>
	$(document).ready(function(){
		window.dataLayer = window.dataLayer || [];
		$("[gtm-categories] a").click(function(){
			var cttLabelText = $(this).find('h3').text(); //gets text from clicked item
			var cttCategory =  $(this).attr('href'); // gets category from href in clicked item

			var pageType = $("[gtm-ctt-info='page']").val() || '';
			var customerSegment = $("[gtm-ctt-info='customer']").val() || '';
			var hostOwner = $("[gtm-ctt-info='host']").val() || '';
			var productGroupName = $("[gtm-ctt-info='product']").val() || '';

			var dataLayerObject = {
				event: "cttOpenCategoryFaq",
				cttAction: cttCategory,
				cttLabel: cttLabelText,
				cttProductGroupName: productGroupName,
				cttHostOwner: hostOwner,
				cttCustomerSegment: customerSegment,
				cttPageType: pageType,
			};
			//console.log(dataLayerObject);
			window.dataLayer.push(dataLayerObject);
			/*	window.dataLayer.push({
					cttProductGroupName : "Nenhum",
					cttHostOwner : "CTT",
					cttCustomerSegment : "Particular & Empresas",
					cttPageType : "Página de Ajuda / Contactos",
					event: "cttOpenCategoryFaq",
					cttAction: cttCategory,
					cttLabel: cttLabelText
				});
			*/
		});
	});
</script>

<script>
	$(document).ready(function(){
		window.dataLayer = window.dataLayer || [];

		// Code to handle  banners and banners-side-by-side
		$("[gtm-block-click=banner] a").click(function(event){
			// Retrieves page information
			// TODO : centralize code
			var pageType = $("[gtm-ctt-info='page']").val() || '';
			var customerSegment = $("[gtm-ctt-info='customer']").val() || '';
			var hostOwner = $("[gtm-ctt-info='host']").val() || '';
			var productGroupName = $("[gtm-ctt-info='product']").val() || '';

			var containerCSS, containerTitle, clickText;

			//code for banner
			if($(this).parents('section').attr('class').indexOf('side-by-side') !== -1){
				containerTitle = $(this).parent().find('h2').text().trim(); //gets container title
			}else{
				containerTitle = $(this).parents('section').find('h2').text().trim(); //gets container title
			}
			clickText = $(this).text().trim(); //gets text from clicked item

			var dataLayerObject = {
				event: 'cttBlockClick',
				cttContainerClass: getContainerCSS($(this).parents('section')),
				cttContainerTitle: containerTitle,
				cttClickText: clickText,
				cttProductGroupName : productGroupName,
				cttHostOwner : hostOwner,
				cttCustomerSegment : customerSegment,
				cttPageType : pageType,
			};

			//console.log(dataLayerObject);
			window.dataLayer.push(dataLayerObject);

			function getContainerCSS(parentWrapper){
				var containerClass = $(parentWrapper).attr('class').trim(); //grabs class from parent
				var containerClasses = containerClass.split(" "); //splits classes into an array
				var hasCardLinks = parentWrapper.find('ul li').length; //checks if banner has links
				var finalClass = '';

				for(var i=0; i < containerClasses.length; i++){
					if(containerClasses[i] == "") continue;
					//since first iteration has banner in the name, we remove the word banner inside de next classes we iterate banner (6 characters)
					if(containerClasses[i].indexOf('banner') !== -1 && i > 0) containerClasses[i] = containerClasses[i].substr(6,containerClasses[i].length)
					finalClass += containerClasses[i];
				}
				// Some versions of banner despite having list of links dont contain "-card-links" class
				if(finalClass.indexOf('-links') == -1 && hasCardLinks) finalClass += "-links";

				return finalClass;
			}
		});

		// Code to handle hub-list
		//$("[gtm-block-click=destaque] a").click(function(event){
		$("[gtm-block-click=hub-list] a").click(function(event){
			var pageType = $("[gtm-ctt-info='page']").val() || '';
			var customerSegment = $("[gtm-ctt-info='customer']").val() || '';
			var hostOwner = $("[gtm-ctt-info='host']").val() || '';
			var productGroupName = $("[gtm-ctt-info='product']").val() || '';

			var containerCSS, containerTitle, clickText;

			//code valid for .card-container ( hub-list, destaques, slider de destaques )
			containerTitle = $(this).parents('div.card-container').find('h3').text().trim();
			//special verification when its a "destaque" from hub-list and has 2 anchor tag, one inside <object> and <a> wrapping the component
			if( $(this).parent()[0].tagName.toLowerCase() == "object"){
				clickText = $(this).text().trim();
			}else{
				//if user isn't clicking in an <a> inside an object, it means we need to extract the text in the footer ( when its a destaque from hub list )
				//if .card-container doesn't have footer, then we assign containerTitle value to clickText
				clickText = $(this).parents('div.card-container').find('.card-footer h6').text().trim() || containerTitle;
			}

			var dataLayerObject = {
				event: 'cttBlockClick',
				cttContainerClass: getContainerCSS($(this).parents('div.card-container')),
				cttContainerTitle: containerTitle,
				cttClickText: clickText,
				cttProductGroupName : productGroupName,
				cttHostOwner : hostOwner,
				cttCustomerSegment : customerSegment,
				cttPageType : pageType,
			};

			//console.log(dataLayerObject);
			window.dataLayer.push(dataLayerObject);

			function getContainerCSS(parentWrapper, containerType){
				//since destaques dont have specific classes, we created several variables to store the class name accordingly to which type of container
				var HUB_LIST_VANTAGENS = "-hub-list-vantagens";
				var HUB_LIST_NORMAL = "-hub-list";
				var HAS_IMAGE = "-imagem"
				var HAS_BUTTON = "-botao";
				var DESTAQUE_BG_IMAGE = "-fundo";
				var DESTAQUE_SLIDER = "-slider";
				var finalClass = "destaque"; //default class is always "destaque", we just add complementary classes depending on the component

				//checks for different html elements in order to help identify which type of destaque was clicked
				var hasAdvantages = parentWrapper.find("ul li").length; //checks if destaque has advantages
				var hasImage = parentWrapper.find('img').length; //checks if destaque has image
				var hasButton = parentWrapper.find('.button').length; //checks for button elements
				var isDestaque = parentWrapper.find("figure").length; //means its a destaque with BG image
				var isSlider = parentWrapper.hasClass("tns-item"); //means its a destaque used in a slider

				// in priority order with are first handling the following cases
				/*
				destaque slider
				destaque bg image
				hub-list
				*/
				if(isSlider){
					finalClass += DESTAQUE_SLIDER ; //DESTAQUE-SLIDER
				}else if( isDestaque > 0 ){
					finalClass += DESTAQUE_BG_IMAGE ; //DESTAQUE-BG-IMAGE
				}else if( hasAdvantages > 0 ){
					finalClass += HUB_LIST_VANTAGENS; //DESTAQUE-HUB-LIST-VANTAGENS
				}else{
					finalClass += HUB_LIST_NORMAL; //DESTAQUE-HUB-LIST
				}
				if(hasImage) finalClass += HAS_IMAGE; // DESTAQUE-SLIDER-IMAGE || DESTAQUE-HUB-LIST-IMAGE || DESTAQUE-HUB-LIST-VANTAGENS-IMAGE
				if(hasButton) finalClass += HAS_BUTTON;
				//
				return finalClass;
			}
		});
	});
</script>

<script>
	$(document).ready(function(){
		window.dataLayer = window.dataLayer || [];
		$("[gtm-app-box] a, [gtm-app-box] button").click(function(event){
			var pageType = $("[gtm-ctt-info='page']").val() || '';
			var customerSegment = $("[gtm-ctt-info='customer']").val() || '';
			var hostOwner = $("[gtm-ctt-info='host']").val() || '';
			var productGroupName = $("[gtm-ctt-info='product']").val() || '';

			var clickText;
			// Checks if its a <button> or <a> tag, in order to extract the text accordingly
			if(event.target.tagName.toLowerCase() == "button"){
				clickText = $(this).parent().parent().find('legend').text();
			}else{
				clickText = $(this).text();
			}

			var dataLayerObject = {
				event: 'cttAppBoxClick',
				cttContainerClass: 'quicktools',
				cttContainerTitle: 'Quicktool',
				cttClickText: clickText,
				cttProductGroupName : productGroupName,
				cttHostOwner : hostOwner,
				cttCustomerSegment : customerSegment,
				cttPageType : pageType,
			};

			//console.log(dataLayerObject);
			window.dataLayer.push(dataLayerObject);
		});
	});
</script>



<script>

    $(document).ready(function(){



		/*

		@name: sendGtmPromotionClick

		@type: function

		@description: function called when when a slider is clicked

		*/

		function sendGtmPromotionClick(event){

			window.dataLayer = window.dataLayer || [];



			var containerClass, containerTitle, clickText;

			var activeSlider = $(this).parents('li'); //identifies which slider is selected

			var pageType = $("[gtm-ctt-info='page']").val() || '';

			var customerSegment = $("[gtm-ctt-info='customer']").val() || '';

			var hostOwner = $("[gtm-ctt-info='host']").val() || '';

			var productGroupName = $("[gtm-ctt-info='product']").val() || '';



			var promotionName = $(activeSlider).find('h1').text(); //grabs slider title

			var creativeName = $(activeSlider).find('img').attr('alt'); // grabs image name

			var positionIndex = $(activeSlider).index() + 1; // grabs selected slider index

			var redirectUrl = event.target.href;



			//clickText = $(event.target).text() || creativeName;

			clickText = $(this).text() || creativeName; //grabs text clicked, if doesn't exist, uses "creativeName" instead

			containerTitle = promotionName;

			containerClass = getCointainerClassName(activeSlider);



			var dataLayerObject = {

				event: 'cttPromotionClick',

				cttProductGroupName : productGroupName,

				cttHostOwner : hostOwner,

				cttCustomerSegment : customerSegment,

				cttPageType : pageType,

				cttContainerClass: containerClass,

				cttContainerTitle: containerTitle,

				cttClickText: clickText,

				ecommerce: {

					promotion_name: promotionName,

					creative_name: creativeName,

					creative_slot: positionIndex,

					redirect_url: redirectUrl,

				}

			};



			//console.log(dataLayerObject);

			window.dataLayer.push(dataLayerObject);



			function getCointainerClassName(activeSlider) {

				var BANNER_TEXT_BACKGROUND_CLASS = "banner-text-background"; //used to validate if background is being used

				var CAPTION_BUTTON_WRAP_CLASS = "caption-button-wrap";//used to validate if slider has any buttons

				var containerClass = "slider";



				if($(activeSlider).hasClass(BANNER_TEXT_BACKGROUND_CLASS)) containerClass += "-caixa"; //SLIDER-CAIXA

				if($(activeSlider).find("."+CAPTION_BUTTON_WRAP_CLASS)) containerClass += "-link"; //SLIDER-LINKS



				return containerClass;

			}

		}

		/*

		@name: sendGtmPromotionImpression

		@type: function

		@description: function called when slider is ready ( initialized ) and after each slide change

		*/

		function sendGtmPromotionImpression(){

			window.dataLayer = window.dataLayer || [];



			var pageType = $("[gtm-ctt-info='page']").val() || '';

			var customerSegment = $("[gtm-ctt-info='customer']").val() || '';

			var hostOwner = $("[gtm-ctt-info='host']").val() || '';

			var productGroupName = $("[gtm-ctt-info='product']").val() || '';



			var promotionName = $(this).find('.active').find('h1').text(); //gets active slider title

			var creativeName = $(this).find('.active').find('img').attr('alt'); //gets active slider image name

			var positionIndex = $(this).find('.active').index() + 1; //gets active slider index



            //Slider not ready, gets first slide

			if (positionIndex < 1) {

				positionIndex = 1;

			    promotionName = $("[data-orbit] li:first").find('h1').text(); //gets first slider title

			    creativeName = $("[data-orbit] li:first").find('img').attr('alt'); //gets first slider image name

			}



			var dataLayerObject = {

				event: 'cttPromotionImpression',

				cttProductGroupName : productGroupName,

				cttHostOwner : hostOwner,

				cttCustomerSegment : customerSegment,

				cttPageType : pageType,

				ecommerce: {

					promotion_name: promotionName,

					creative_name: creativeName,

					creative_slot: positionIndex,

				}

			};



			//console.log(dataLayerObject);

			window.dataLayer.push(dataLayerObject);



		}

		if($("[data-orbit]").length > 0){

			$("[data-orbit]").on("after-slide-change.fndtn.orbit ready.fndtn.orbit", sendGtmPromotionImpression);

			$("[data-orbit] a").click(sendGtmPromotionClick);



			sendGtmPromotionImpression();

		}

    });

</script>

<input type="hidden" gtm-ctt-info="host" value='' />
<input type="hidden" gtm-ctt-info="page" value='' />
<input type="hidden" gtm-ctt-info="customer" value='' />
<input type="hidden" gtm-ctt-info="product" value='' />    </body>

</html>
