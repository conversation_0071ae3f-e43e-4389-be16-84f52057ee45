"""Tests for ingestion operations functionality."""

import os
import shutil
import uuid
from datetime import datetime, timedelta

import pytest

from gen_os_am_knowledge.sdk.filesystem_connectors.local_fs_connector import (
    LocalFSConnector,
)
from gen_os_am_knowledge.sdk.models.common import _DEFAULT_UUID
from gen_os_am_knowledge.sdk.operations.folder_ops import FolderService
from gen_os_am_knowledge.sdk.operations.ingestion_ops import IngestionService
from gen_os_am_knowledge.sdk.operations.source_ops import SourceService

pytest_plugins = "pytest_asyncio"


@pytest.fixture(scope="module")
def create_root_folder():
    """Create root folder for testing."""
    storage_path = "path-to-local-storage"
    root_folder = "root-folder"
    os.makedirs(f"{storage_path}/{root_folder}", exist_ok=True)
    yield root_folder
    shutil.rmtree(storage_path)


@pytest.fixture(scope="module")
def local_fs_connector(create_root_folder):
    """Create local filesystem connector."""
    yield LocalFSConnector(root_folder=create_root_folder)


@pytest.mark.asyncio
async def test_create_and_get_ingestion(local_fs_connector):
    """Test creating, retrieving, updating, and deleting an ingestion."""
    folder_service = FolderService(
        fs_connector=local_fs_connector,
    )
    ingestion_folder = await folder_service.create_folder(
        folder_name="ingestion_sdk", user_id=_DEFAULT_UUID
    )
    ingestion_service = IngestionService()
    # Test variables
    description = "pytest_ingestion"

    # create source
    source_service = SourceService()
    source_name = "pytest_source"
    source_type = "website"
    created_source = await source_service.create_source(
        source_name=source_name, source_type=source_type
    )
    assert created_source is not None, "Source creation failed"

    # Create a ingestion
    created_ingestion = await ingestion_service.create_ingestion(
        source_id=created_source.id,
        description=description,
        folder_id=ingestion_folder.id,
    )
    assert created_ingestion is not None, "Ingestion creation failed"
    assert (
        created_ingestion.description == description
    ), "Created ingestion description mismatch"
    assert (
        created_ingestion.source_id == created_source.id
    ), "Created ingestion source_id mismatch"
    assert created_ingestion.folder_id == ingestion_folder.id

    # Get the ingestion
    retrieved_ingestion = await ingestion_service.get_ingestion(
        ingestion_id=created_ingestion.id
    )
    assert (
        retrieved_ingestion.description == description
    ), "Created ingestion description mismatch"
    assert (
        retrieved_ingestion.source_id == created_source.id
    ), "Created ingestion source_id mismatch"

    retrieved_ingestions = await ingestion_service.search_ingestions(
        description=description, source_id=created_source.id, limit=1, offset=0
    )

    assert retrieved_ingestions[0].description == description
    assert retrieved_ingestions[0].source_id == created_source.id

    # Update the ingestion
    updated_description = "pytest_ingestion_updated"
    updated_meta = {"metafield1": "test1", "metafield2": "test2"}
    updated_status = "Done"
    last_run_date = datetime.now()
    last_success_date = datetime.now() - timedelta(days=1)
    error = "Error"

    update_result = await ingestion_service.update_ingestion(
        ingestion_id=created_ingestion.id,
        description=updated_description,
        meta=updated_meta,
        status=updated_status,
        last_run_date=last_run_date,
        last_success_date=last_success_date,
        error=error,
    )

    assert update_result.success
    assert update_result.updated_instance.description == updated_description
    assert update_result.updated_instance.status == updated_status
    assert update_result.updated_instance.last_run_date == last_run_date
    assert update_result.updated_instance.last_success_date == last_success_date
    assert update_result.updated_instance.error == error
    assert update_result.updated_instance.meta == updated_meta
    assert update_result.error is None
    assert update_result.updated_values == {
        "description": (description, updated_description),
        "status": (created_ingestion.status, updated_status),
        "last_run_date": (created_ingestion.last_run_date, last_run_date),
        "last_success_date": (created_ingestion.last_success_date, last_success_date),
        "error": (created_ingestion.error, error),
        "meta": (None, updated_meta),
    }

    # Clean up by deleting the ingestion
    deletion_result = await ingestion_service.delete_ingestion(
        ingestion_id=created_ingestion.id
    )

    assert deletion_result.success
    assert deletion_result.deleted_instance == update_result.updated_instance
    assert deletion_result.error is None

    # Test deletion of non-existent ingestion
    ingestion_uuid = uuid.uuid4()
    deletion_result = await ingestion_service.delete_ingestion(
        ingestion_id=ingestion_uuid
    )

    assert not deletion_result.success
    assert deletion_result.deleted_instance is None
    assert deletion_result.error == f"Ingestion with id {ingestion_uuid} not found."

    # Clean up by deleting the source
    deletion_result = await source_service.delete_source(source_id=created_source.id)
    assert deletion_result.success
