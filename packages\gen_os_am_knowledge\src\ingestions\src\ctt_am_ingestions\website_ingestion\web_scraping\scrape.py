"""URLs web scraping script."""

import asyncio
import io
import logging
import os
from asyncio import TimeoutError as AsyncTimeoutError
from datetime import datetime, timezone

from aiohttp import ClientError, ClientSession, ClientTimeout
from dotenv import load_dotenv

from ctt_am_ingestions.website_ingestion.web_scraping.parse import parse_page
from ctt_am_ingestions.website_ingestion.web_scraping.read_and_save import (
    read_urls,
    save_to_json,
)
from ctt_am_ingestions.website_ingestion.web_scraping.results import Page, Results
from ctt_am_ingestions.website_ingestion.web_scraping.utils import normalize_url
from gen_os_am_knowledge.sdk.ingestion.ingestion import IngestionDocument
from gen_os_am_knowledge.utils.ctt.ctt_utils.logging.ctt_log_formatter import (
    CTTFormatter,
)
from gen_os_am_knowledge.utils.ctt.ctt_utils.logging.ctt_log_models import (
    <PERSON><PERSON><PERSON>,
    <PERSON>rrorLog,
    EventModel,
)
from gen_os_am_knowledge.utils.logging.log_init import init_logger

load_dotenv()
ENVIRONMENT = os.getenv("ENVIRONMENT", "DEV")

logger = init_logger(
    name="website_scraping", formatter=CTTFormatter(), destination="stdout"
)


def log_scrape_error(
    logger: logging.Logger, error: Exception, start: datetime, msg: str
) -> None:
    """Scraping error logging.

    Args:
        logger: Logger instance.
        error: Exception object.
        start: Start time of the scraping.
        msg: Message to log.

    """
    base_log = BaseLog(
        environment=ENVIRONMENT,
        event=EventModel(
            category=["web"],
            type=["error"],
            outcome="failure",
            start=start,
            end=datetime.now(timezone.utc),
        ),
        error=ErrorLog(
            code=0, message=error, type=type(error).__name__, stack_trace=None
        ),
    )

    logger.error(msg=msg, extra=base_log.model_dump(mode="json"))


def page_to_ingestion_document(page: Page) -> IngestionDocument:
    """Convert a page to an ingestion document.

    Args:
        page: The page to convert.

    Returns:
        The ingestion document.

    """
    name = (
        page.url.removeprefix("https://www.ctt.pt/").lower().replace("/", "_") + ".json"
    )
    file = io.BytesIO(page.model_dump_json(indent=4).encode("utf-8"))
    file.name = name
    doc = IngestionDocument(
        file=file,
        storage_folder_path="",
        original_path=page.url,
    )
    return doc


async def scrape_url(
    url: str,
    visited_urls: set[str],
    results: Results,
    session: ClientSession,
    logger: logging.Logger = logger,
) -> None | IngestionDocument:
    """Scrape a single URL and update results asynchronously."""
    start = datetime.now(timezone.utc)
    try:
        async with session.get(url, timeout=ClientTimeout(total=90)) as response:
            url = normalize_url(str(response.url))
            if url in visited_urls:
                return
            visited_urls.add(url)

            if response.status != 200:
                results.not_found.urls.append(url)
                return

            text = await response.text()
            page = parse_page(url, text)

            if not page:
                results.skipped.urls.append(url)
                return

            results.scraped.pages.append(page)
            ing_doc = page_to_ingestion_document(page)

            base_log = BaseLog(
                environment=ENVIRONMENT,
                event=EventModel(
                    category=["web"],
                    type=["access"],
                    start=start,
                    end=datetime.now(timezone.utc),
                ),
            )
            logger.debug(
                msg=f"Successful scrape of {url}",
                extra=base_log.model_dump(mode="json"),
            )
            return ing_doc

    except ClientError as e:
        log_scrape_error(logger, str(e), start, f"Error fetching URL {url}: {e}")
        results.skipped.urls.append(url)
    except AsyncTimeoutError as e:
        log_scrape_error(
            logger, str(e), start, f"Timeout error fetching URL {url}: {e}"
        )
        results.skipped.urls.append(url)
    except UnicodeDecodeError as e:
        log_scrape_error(
            logger, str(e), start, f"Encoding error fetching URL {url}: {e}"
        )
        results.skipped.urls.append(url)
    except Exception as e:
        log_scrape_error(logger, str(e), start, f"Error scraping URL {url}: {e}")
        results.skipped.urls.append(url)


async def main():
    """Main function."""  # noqa: D401
    urls = read_urls()
    visited_urls = set()
    results = Results()

    async with ClientSession() as session:
        tasks = [scrape_url(url, visited_urls, results, session) for url in urls]
        await asyncio.gather(*tasks)

    save_to_json(results)


if __name__ == "__main__":
    asyncio.run(main())
