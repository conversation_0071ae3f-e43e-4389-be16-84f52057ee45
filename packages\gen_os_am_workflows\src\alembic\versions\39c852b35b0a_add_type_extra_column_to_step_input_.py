"""Add type_extra column to step input/output.

Revision ID: 39c852b35b0a
Revises: 3c39c8a5970c
Create Date: 2025-06-24 07:23:23.567387

"""

from collections.abc import Sequence

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "39c852b35b0a"
down_revision: str | None = "3c39c8a5970c"
branch_labels: str | Sequence[str] | None = None
depends_on: str | Sequence[str] | None = None


def upgrade() -> None:
    """See ab7673f32403_fix_type_extra_column_null_values.py for this migration."""
    # Check if columns exist and update if they do
    conn = op.get_bind()
    inspector = sa.inspect(conn)

    if "type_extra" not in [c["name"] for c in inspector.get_columns("step_input")]:
        with op.batch_alter_table("step_input", schema=None) as batch_op:
            batch_op.add_column(
                sa.Column("type_extra", postgresql.JSON(), nullable=True, default={})
            )
    else:
        op.execute("UPDATE step_input SET type_extra = '{}'::json")

    if "type_extra" not in [c["name"] for c in inspector.get_columns("step_output")]:
        with op.batch_alter_table("step_output", schema=None) as batch_op:
            batch_op.add_column(
                sa.Column("type_extra", postgresql.JSON(), nullable=True, default={})
            )
    else:
        op.execute("UPDATE step_output SET type_extra = '{}'::json")

    # Set default values for any NULL values
    op.execute("UPDATE step_input SET type_extra = '{}'::json WHERE type_extra IS NULL")
    op.execute("UPDATE step_output SET type_extra = '{}'::json WHERE type_extra IS NULL")

    # Make columns not nullable
    with op.batch_alter_table("step_input", schema=None) as batch_op:
        batch_op.alter_column("type_extra", nullable=False)

    with op.batch_alter_table("step_output", schema=None) as batch_op:
        batch_op.alter_column("type_extra", nullable=False)


def downgrade() -> None:
    """See ab7673f32403_fix_type_extra_column_null_values.py for this migration."""
    with op.batch_alter_table("step_output", schema=None) as batch_op:
        batch_op.drop_column("type_extra")

    with op.batch_alter_table("step_input", schema=None) as batch_op:
        batch_op.drop_column("type_extra")
