"""Module for the logger configuration."""

from functools import cache
from logging import Logger

from gen_os_am_knowledge.config.settings import LogSettings
from gen_os_am_knowledge.utils.ctt.ctt_utils.logging.ctt_log_formatter import (
    CTTFormatter,
)
from gen_os_am_knowledge.utils.logging.log_init import init_logger


@cache
def get_sdk_logger() -> Logger:
    """Get the core logger."""
    return init_logger(
        name="knowledge_management_sdk",
        formatter=CTTFormatter(),
        destination="stdout",
        log_level=LogSettings.get_settings().LOG_LEVEL,
    )


@cache
def get_api_logger() -> Logger:
    """Get the API logger."""
    return init_logger(
        name="knowledge_management_api",
        formatter=CTTFormatter(),
        destination="stdout",
        log_level=LogSettings.get_settings().LOG_LEVEL,
    )
