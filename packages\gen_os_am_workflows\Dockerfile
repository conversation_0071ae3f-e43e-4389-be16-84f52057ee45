FROM python:3.12-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1

# Set work directory
WORKDIR /app

# TODO(Henrique): Check if there's a better way of handling this.
# i.e., removing gcc.
# Install PostgreSQL development packages
RUN apt-get update && apt-get install -y libpq-dev gcc && rm -rf /var/lib/apt/lists/*

# TODO(Henrique): Try to copy just the files needed.
# Copy project
COPY . /app/

# Install dependencies
RUN pip install shared/lib/gen_os_sdk_emulator-0.1.0-py3-none-any.whl
RUN pip install .

WORKDIR /app/src

# Expose the port the app runs on
EXPOSE 8000

# Make sure the entrypoint script has execute permissions
RUN chmod +x /app/entrypoint.sh

# Run the application
CMD ["/app/entrypoint.sh"]
