"""FastAPI router for agent conversation management endpoints."""

import logging
import uuid

from fastapi import (
    APIRouter,
    Depends,
    HTTPException,
    Path,
    Query,
    Request,
    Response,
)
from opentelemetry.proto.collector.trace.v1.trace_service_pb2 import (
    ExportTraceServiceRequest,
)
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.sql import func

from gen_os_am_conversational.api.schemas import (
    AgentConversationFilters,
    ConversationMetadataResponse,
    ConversationsSchema,
    MessageFilters,
    MessageTurnSchema,
    PaginatedConversationsResponse,
    PaginatedMessageTurnResponse,
    PaginationDetails,
)
from gen_os_am_conversational.database.session import SessionManager
from gen_os_am_conversational.models.models import Span
from gen_os_am_conversational.utils import (
    get_conversation_metadata,
    get_conversation_summaries_paginated,
    list_message_turns_paginated,
    load_span_data,
    process_conversation_span,
)

# Add logging
logger = logging.getLogger(__name__)


def agent_id_dependency(agent_id: str = Path(...)):
    """Extract agent_id from the path.

    This allows us to add agent_id to the router prefix without
    adding it to every endpoint signature.
    """
    return agent_id


# Router for agent conversation endpoints
router = APIRouter(
    prefix="/agents/{agent_id}/conv/v1",
    dependencies=[Depends(agent_id_dependency)],
    tags=["Conversational Agents Functionalities"],
)

# Separate router for telemetry endpoints (not agent-specific)
telemetry_router = APIRouter(
    tags=["Telemetry"],
)


@telemetry_router.post("/otel/traces")
async def receive_otel(request: Request):
    """Handle incoming OpenTelemetry trace data.

    Processes OTLP trace data, storing spans and conversation messages in the database.
    Filters and processes spans for conversation data extraction.

    Args:
        request: The FastAPI request object containing OTLP data

    Returns:
        dict: Status confirmation of the operation

    """
    body = await request.body()

    try:
        otlp_request = ExportTraceServiceRequest()
        otlp_request.ParseFromString(body)
    except Exception as e:
        logger.error(f"Failed to parse OTLP protobuf data: {e}")
        raise HTTPException(status_code=400, detail=f"Invalid protobuf data: {str(e)}") from None

    async with SessionManager.get_session() as db:
        for resource_span in otlp_request.resource_spans:
            for scope_span in resource_span.scope_spans:
                for span_data in scope_span.spans:
                    try:
                        span_clean = load_span_data(span_data)
                    except ValueError as e:
                        logger.error(f"Failed to process span data: {e}")
                        continue

                    db_span = Span(
                        trace_id=span_clean.trace_id,
                        id=span_clean.span_id,
                        parent_span_id=span_clean.parent_span_id,
                        name=span_data.name,
                        start_time=span_clean.start_time,
                        end_time=span_clean.end_time,
                        attributes=span_clean.attributes,
                        events=span_clean.events,
                    )
                    db.add(db_span)

                    if (
                        span_data.name == "gen_os.activity.conversation"
                    ):  # TODO change for conversational
                        await process_conversation_span(db, span_clean, span_data.name)

        await db.commit()
    return {"status": "ok"}


@router.get(
    "/conversations",
    response_model=PaginatedConversationsResponse,
    summary="List Conversations for a Specific Agent",
    description=("Retrieve a paginated list of conversation for an agent with search."),
    response_model_by_alias=False,
)
async def list_agent_conversations_by_agent_id(
    request: Request,
    response: Response,
    filters: AgentConversationFilters = Query(),
):
    """Get a list of an agent's conversations with pagination, sorting, and search."""
    agent_id = request.path_params["agent_id"]
    try:
        async with SessionManager.get_session() as db:
            (
                summary_records,
                pagination_details_obj,
            ) = await get_conversation_summaries_paginated(
                db=db,
                offset=filters.offset,
                limit=filters.limit,
                sort_field=filters.sort_field,
                sort_order=filters.sort_order,
                agent_id=agent_id,
                search=filters.search,
                search_column=filters.search_column,
            )

            conversation_objects = [ConversationsSchema(**record) for record in summary_records]

            response_data = PaginatedConversationsResponse(
                items=conversation_objects, pagination=pagination_details_obj
            )

            response.headers["X-Total-Count"] = str(pagination_details_obj.total_items)
            return response_data

    except HTTPException:
        raise  # Re-raise HTTP exceptions
    except Exception as e:
        logger.error(
            f"Unexpected error in list_agent_conversations_by_agent_id: {e}",
            exc_info=True,
        )
        raise HTTPException(
            status_code=500, detail=f"An unexpected error occurred: {str(e)}"
        ) from e


@router.get(
    "/conversations/{conversation_id}",
    response_model=ConversationMetadataResponse,
    summary="Get Conversation Metadata for a Specific Agent",
    description=(
        "Retrieve the metadata for a specific past conversation, ensuring it belongs "
        "to the specified agent. Does NOT include individual messages."
    ),
    response_model_by_alias=False,
)
async def get_agent_conversation_metadata_endpoint(
    request: Request,
    conversation_id: uuid.UUID = Path(..., description="The ID of the conversation."),
):
    """Get metadata for a specific conversation belonging to an agent."""
    agent_id = request.path_params["agent_id"]
    try:
        async with SessionManager.get_session() as db:
            conversation_metadata = await get_conversation_metadata(
                db=db, conversation_id=conversation_id, agent_id=agent_id
            )

            if not conversation_metadata:
                logger.warning(f"Conversation {conversation_id} not found for agent {agent_id}")
                raise HTTPException(
                    status_code=404,
                    detail=(
                        f"Conversation with ID {conversation_id} not found " f"for agent {agent_id}"
                    ),
                )

            return ConversationMetadataResponse(
                id=conversation_metadata["id"],
                agent_id=conversation_metadata["agent_id"],
                start_time=conversation_metadata["start_time"],
                end_time=conversation_metadata["end_time"],
                message_count=conversation_metadata["message_count"],
                custom_fields=conversation_metadata["custom_fields"] or {},
            )
    except HTTPException:
        raise  # Re-raise HTTP exceptions without logging as error
    except Exception as e:
        logger.error(
            f"Unexpected error in get_agent_conversation_metadata_endpoint: {e}",
            exc_info=True,
        )
        raise HTTPException(
            status_code=500, detail=f"An unexpected error occurred: {str(e)}"
        ) from e


@router.get(
    "/conversations/{conversation_id}/messages",
    response_model=PaginatedMessageTurnResponse,
    summary="List Messages for a Specific Agent's Conversation (Paginated)",
    description=("Retrieve a paginated list of detailed messages for a specific conversation."),
    response_model_by_alias=False,
)
async def list_agent_conversation_messages_endpoint(
    request: Request,
    response: Response,
    conversation_id: uuid.UUID = Path(..., description="The ID of the conversation."),
    filters: MessageFilters = Query(),
):
    """Get a paginated list of messages for a specific agent's conversation."""
    agent_id = request.path_params["agent_id"]
    try:
        async with SessionManager.get_session() as db:
            conversation_meta = await get_conversation_metadata(
                db=db, conversation_id=conversation_id, agent_id=agent_id
            )
            if not conversation_meta:
                raise HTTPException(
                    status_code=404,
                    detail=(
                        f"Conversation with ID {conversation_id} not found "
                        f"for agent {agent_id}."
                    ),
                )

            message_turn_records, total_items = await list_message_turns_paginated(
                db=db,
                conversation_id=conversation_id,
                offset=filters.offset,
                limit=filters.limit,
                agent_id=agent_id,
            )

            message_turn_responses: list[MessageTurnSchema] = []
            for mt_db in message_turn_records:
                message_turn_responses.append(MessageTurnSchema.model_validate(mt_db))

            pagination_details = PaginationDetails(
                offset=filters.offset,
                limit=filters.limit,
                total_items=total_items,
            )

            response_data = PaginatedMessageTurnResponse(
                items=message_turn_responses,
                pagination=pagination_details,
            )

            response.headers["X-Total-Count"] = str(pagination_details.total_items)
            return response_data
    except HTTPException:
        raise  # Re-raise HTTP exceptions
    except Exception as e:
        logger.error(
            f"Unexpected error in list_agent_conversation_messages_endpoint: {e}",
            exc_info=True,
        )
        raise HTTPException(
            status_code=500, detail=f"An unexpected error occurred: {str(e)}"
        ) from e
