"""Tests for source operations functionality."""

import uuid

import pytest

from gen_os_am_knowledge.sdk.operations.source_ops import SourceService

pytest_plugins = "pytest_asyncio"


@pytest.mark.asyncio
async def test_create_and_get_source():
    """Test creating, retrieving, updating, and deleting a source."""
    source_service = SourceService()
    # Test variables
    source_name = "pytest_source"
    source_type = "website"

    # Create a source
    created_source = await source_service.create_source(
        source_name=source_name, source_type=source_type
    )
    assert created_source is not None, "Source creation failed"
    assert created_source.name == source_name, "Created source name mismatch"

    # Get the source
    retrieved_source = await source_service.get_source(source_id=created_source.id)
    assert retrieved_source.name == source_name, "Retrieved source mismatch"
    assert retrieved_source.type == source_type, "Retrieved source type mismatch"

    retrieved_sources = await source_service.search_sources(
        source_type=source_type, source_name=source_name, limit=1, offset=0
    )
    assert retrieved_sources == [created_source], "Retrieved sources mismatch"

    # Update the source
    updated_source_name = "pytest_source_updated"
    updated_meta = {"metafield1": "test1", "metafield2": "test2"}
    update_result = await source_service.update_source(
        source_id=created_source.id,
        source_name=updated_source_name,
        meta=updated_meta,
    )

    assert update_result.success
    assert update_result.updated_instance.name == updated_source_name
    assert update_result.updated_instance.meta == updated_meta
    assert update_result.error is None
    assert update_result.updated_values == {
        "name": (source_name, updated_source_name),
        # type is not present since not changed
        "meta": (None, updated_meta),
    }

    # Clean up by deleting the source
    deletion_result = await source_service.delete_source(source_id=created_source.id)

    assert deletion_result.success
    assert deletion_result.deleted_instance == update_result.updated_instance
    assert deletion_result.error is None

    # Test deletion of non-existent source
    source_uuid = uuid.uuid4()
    deletion_result = await source_service.delete_source(source_id=source_uuid)

    assert not deletion_result.success
    assert deletion_result.deleted_instance is None
    assert deletion_result.error == f"Source with id {source_uuid} not found."
