"""Test the ingestion API."""

import pytest

from gen_os_am_knowledge.sdk.models import Ingestion
from gen_os_am_knowledge.sdk.models.common import DEFAULT_FOLDER, DEFAULT_SOURCE


def test_ingestion(client):  # Changed fixture from setup_api to client
    """Test the ingestion API."""
    # Removed: app = setup_api
    # Removed: client = TestClient(app)
    description = "test"
    response = client.post(
        "/ingestion",
        json={
            "source_id": str(DEFAULT_SOURCE.id),
            "description": description,
            "folder_id": str(DEFAULT_FOLDER.id),
        },
    )

    assert response.status_code == 201
    response_json = response.json()
    assert response_json["description"] == description
    assert response_json["source_id"] == str(DEFAULT_SOURCE.id)
    assert response_json["status"] == Ingestion.ING_STATUS_PENDING
    ingestion_id = response_json["id"]

    # get
    response = client.get(f"/ingestion/{ingestion_id}")
    assert response.status_code == 200
    response_json = response.json()
    assert response_json["description"] == description
    assert response_json["source_id"] == str(DEFAULT_SOURCE.id)

    # update
    updated_description = "test_updated"
    last_run_date = "2021-01-01T00:00:00"

    response = client.patch(
        f"/ingestion/{ingestion_id}",
        json={
            "description": updated_description,
            "status": Ingestion.ING_STATUS_DONE,
            "last_run_date": last_run_date,
        },
    )
    assert response.status_code == 200
    response_json = response.json()
    assert response_json["success"]
    assert response_json["updated_instance"]["description"] == updated_description
    assert response_json["updated_instance"]["status"] == Ingestion.ING_STATUS_DONE
    assert response_json["error"] is None
    assert response_json["updated_values"] == {
        "description": [description, updated_description],
        "status": [Ingestion.ING_STATUS_PENDING, Ingestion.ING_STATUS_DONE],
        "last_run_date": [None, last_run_date],
    }

    # search
    response = client.get(
        f"/ingestion?description={updated_description}&status={Ingestion.ING_STATUS_DONE}&source_id={DEFAULT_SOURCE.id}"
    )

    assert response.status_code == 200
    response_json = response.json()
    assert len(response_json) == 1
    assert response_json[0]["description"] == updated_description
    assert response_json[0]["status"] == Ingestion.ING_STATUS_DONE
    assert response_json[0]["source_id"] == str(DEFAULT_SOURCE.id)

    # delete
    response = client.delete(f"/ingestion/{ingestion_id}")
    assert response.status_code == 200
    response_json = response.json()
    assert response_json["description"] == updated_description
    assert response_json["source_id"] == str(DEFAULT_SOURCE.id)
    assert response_json["status"] == Ingestion.ING_STATUS_DONE
