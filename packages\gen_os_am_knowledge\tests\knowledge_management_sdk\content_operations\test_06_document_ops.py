"""Tests for document operations and storage interactions."""

import os
import shutil

import pytest
import pytest_asyncio

from gen_os_am_knowledge.sdk.filesystem_connectors.local_fs_connector import (
    LocalFSConnector,
)
from gen_os_am_knowledge.sdk.models.common import _DEFAULT_UUID
from gen_os_am_knowledge.sdk.operations import (
    DocumentService,
    FolderService,
    IngestionService,
    SourceService,
    TagService,
    UserService,
)

pytest_plugins = "pytest_asyncio"


@pytest.fixture(scope="module")
def sample_file():
    """Create sample test files for document operations tests."""
    with open("test.txt", "w") as f:
        f.write("test")
    with open("test2.txt", "w") as f:
        f.write("test2")
    with open("test3.txt", "w") as f:
        f.write("test3")
    with open("new.txt", "w") as f:
        f.write("test4")
    yield
    os.remove("test.txt")
    os.remove("test2.txt")
    os.remove("test3.txt")
    os.remove("new.txt")


@pytest.fixture(scope="module")
def create_storage_path():
    """Create storage path for testing."""
    storage_path = "path-to-local-storage"
    os.makedirs(f"{storage_path}", exist_ok=True)
    yield storage_path
    shutil.rmtree(storage_path)


@pytest.fixture(scope="module")
def create_root_folder(create_storage_path):
    """Create root folder for testing."""
    storage_path = create_storage_path
    root_folder = "root-folder"
    os.makedirs(f"{storage_path}/{root_folder}", exist_ok=True)
    yield root_folder


@pytest.fixture(scope="module")
def local_fs_connector(create_root_folder, create_storage_path):
    """Create local filesystem connector for testing."""
    yield LocalFSConnector(
        root_folder=create_root_folder, local_storage_path=create_storage_path
    )


@pytest_asyncio.fixture(scope="module")
async def tags():
    """Create tags for testing."""
    tag_names = ["water", "fire", "earth", "air"]
    tag_ids = []
    for tag_name in tag_names:
        created_tag = await TagService().create_tag(tag_name=tag_name)
        tag_ids.append(created_tag.id)
        assert created_tag is not None
    yield tag_names
    for tag_id in tag_ids:
        await TagService().delete_tag(tag_id)


@pytest_asyncio.fixture(scope="module")
async def folders(local_fs_connector, create_root_folder):
    """Create folders for testing."""
    folder_name = "secret_sdk"
    created_folder = await FolderService(
        fs_connector=local_fs_connector,
    ).create_folder(folder_name=folder_name, user_id=_DEFAULT_UUID)
    assert created_folder is not None
    created_folders = await FolderService(
        fs_connector=local_fs_connector
    ).create_folders_from_path(path="1/2/3", user_id=_DEFAULT_UUID)
    assert created_folders is not None

    created_folders.extend([created_folder])

    yield created_folders

    for folder in created_folders[::-1]:
        await FolderService(
            fs_connector=local_fs_connector,
        ).delete_folder(folder.id, user_id=_DEFAULT_UUID)


@pytest_asyncio.fixture(scope="module")
async def ingestion_folder(local_fs_connector):
    """Create ingestion folder for testing."""
    ingestion_folder = await FolderService(
        fs_connector=local_fs_connector,
    ).create_folder(folder_name="ingestion_folder", user_id=_DEFAULT_UUID)
    yield ingestion_folder
    await FolderService(
        fs_connector=local_fs_connector,
    ).delete_folder(ingestion_folder.id, user_id=_DEFAULT_UUID)


@pytest_asyncio.fixture(scope="module")
async def user():
    """Create user for testing."""
    user_name = "master"
    created_user = await UserService().create_user(
        user_name=user_name, user_external_id="master"
    )
    yield created_user.id
    await UserService().delete_user(created_user.id)


@pytest_asyncio.fixture(scope="module")
async def source():
    """Create source for testing."""
    source_name = "test_source"
    created_source = await SourceService().create_source(
        source_name=source_name, source_type="website"
    )

    yield created_source.id

    await SourceService().delete_source(created_source.id)


@pytest_asyncio.fixture(scope="module")
async def ingestion(source, ingestion_folder):
    """Create ingestion for testing."""
    description = "test_ingestion"
    created_ingestion = await IngestionService().create_ingestion(
        source_id=source, description=description, folder_id=ingestion_folder.id
    )
    yield created_ingestion
    await IngestionService().delete_ingestion(created_ingestion.id)


# TESTS - mostly to check storage interactions and special methods,
# the rest is tested in the API tests


@pytest.mark.asyncio
async def test_document(
    sample_file,
    folders,
    tags,
    user,
    ingestion,
    local_fs_connector,
    create_root_folder,
    ingestion_folder,
):
    """Test document creation, update, deletion, and storage interactions."""
    ingestion_id = ingestion.id
    tags = tags
    document_service = DocumentService(
        storage_connector=local_fs_connector,
    )

    # test create slave document
    original_path = "whatever/test.txt"
    with open("test.txt", "rb") as f:
        created_slave_document = await document_service.create_document(
            file=f,
            original_path=original_path,
            ingestion_id=ingestion_id,
            folder_id=ingestion.folder_id,
            tags=tags[:3],
        )

    slave_id = created_slave_document.id

    assert created_slave_document is not None
    assert created_slave_document.kind == "slave"
    assert os.path.exists(
        f"path-to-local-storage/{create_root_folder}/{ingestion_folder.name}/"
        f"{document_service._create_storage_name(
            created_slave_document.filename,
            created_slave_document.hash,
        )}"
    )

    folder_id = folders[-1].id
    folder_name = folders[-1].name
    user_id = user

    # create master document
    original_path = "whatever/test.txt"
    with open("test2.txt", "rb") as f:
        created_document = await document_service.create_document(
            file=f,
            original_path=original_path,
            user_id=user_id,
            folder_id=folder_id,
            tags=tags[1:],
        )

    master_id = created_document.id

    assert created_document is not None
    assert created_document.kind == "master"
    assert os.path.exists(
        f"path-to-local-storage/{create_root_folder}/{folder_name}/"
        f"{document_service._create_storage_name(
            created_document.filename,
            created_document.hash,
        )}"
    )

    # assert error on same doc creation
    with open("test2.txt", "rb") as f:
        with pytest.raises(
            ValueError, match=f"Document with name {f.name} already exists"
        ):
            await document_service.create_document(
                file=f,
                original_path=original_path,
                user_id=user_id,
                folder_id=folder_id,
                tags=tags[1:],
            )

    # get
    doc = await document_service.get_document(master_id)

    assert doc is not None
    assert doc.id == master_id

    # modification_date = doc.modification_date

    # update with file
    with open("test3.txt", "rb") as f:
        update_result = await document_service.update_document(
            document_id=master_id, last_modified_by_id=user_id, file=f
        )

    assert update_result is not None
    updated_instance = update_result.updated_instance
    assert updated_instance is not None
    assert updated_instance.id == master_id
    # for some reason the modification date is not updated in pytest - manually works
    # assert updated_instance.modification_date > modification_date
    assert os.path.exists(
        f"path-to-local-storage/{create_root_folder}/{folder_name}/"
        f"{document_service._create_storage_name(
            created_document.filename,
            created_document.hash,
        )}"
    )
    assert os.path.exists(
        f"path-to-local-storage/{create_root_folder}/{folder_name}/"
        f"{document_service._create_storage_name(
            updated_instance.filename,
            updated_instance.hash,
        )}"
    )
    assert "hash" in update_result.updated_values
    new_hash = update_result.updated_values["hash"]
    assert new_hash != created_document.hash

    # update with filename
    update_result = await document_service.update_document(
        document_id=master_id, last_modified_by_id=user_id, filename="new.txt"
    )

    assert update_result is not None
    updated_instance = update_result.updated_instance
    assert updated_instance is not None
    assert updated_instance.id == master_id
    assert updated_instance.filename == "new.txt"
    assert os.path.exists(
        f"path-to-local-storage/{create_root_folder}/{folder_name}/"
        f"{document_service._create_storage_name(
            updated_instance.filename,
            updated_instance.hash,
        )}"
    )
    assert os.path.exists(
        f"path-to-local-storage/{create_root_folder}/{folder_name}/"
        f"{document_service._create_storage_name(
            created_document.filename,
            created_document.hash,
        )}"
    )

    # update with folder
    update_result = await document_service.update_document(
        document_id=master_id, last_modified_by_id=user_id, folder_id=folders[-2].id
    )

    assert update_result is not None
    updated_instance = update_result.updated_instance
    assert updated_instance is not None
    assert updated_instance.id == master_id
    assert updated_instance.folder_id == folders[-2].id
    assert os.path.exists(
        f"path-to-local-storage/{create_root_folder}/1/2/3/"
        f"{document_service._create_storage_name(
            updated_instance.filename,
            updated_instance.hash,
        )}"
    )
    assert not os.path.exists(
        f"path-to-local-storage/{create_root_folder}/{folder_name}/"
        f"{document_service._create_storage_name(
            updated_instance.filename,
            updated_instance.hash,
        )}"
    )

    # update status
    update_result = await document_service.update_document_status(
        document_id=master_id, user_id=user_id, status="Synced"
    )

    assert update_result is not None
    updated_instance = update_result.updated_instance
    assert updated_instance is not None
    assert updated_instance.document_status == "Synced"

    # delete
    await document_service.delete_document(master_id, user_id)

    await document_service.delete_document(slave_id, user_id)

    # check if soft delete worked
    assert os.path.exists(
        f"path-to-local-storage/{create_root_folder}/1/2/3/"
        f"{document_service._create_storage_name(
            updated_instance.filename,
            updated_instance.hash,
        )}"
    )
    assert os.path.exists(
        f"path-to-local-storage/{create_root_folder}/{ingestion_folder.name}/"
        f"{document_service._create_storage_name(
            created_slave_document.filename,
            created_slave_document.hash,
        )}"
    )

    # search all docs
    all_docs = await document_service.search_documents()

    assert len(all_docs) == 0

    # recreate document with same name, but different content
    with open("new.txt", "rb") as f:
        created_document = await document_service.create_document(
            file=f,
            original_path=original_path,
            user_id=user_id,
            folder_id=folders[-2].id,
            tags=tags[1:],
        )
    assert created_document is not None
    assert created_document.deleted_date is None
    assert created_document.hash != new_hash
    assert master_id == created_document.id

    await document_service.delete_document(created_document.id, user_id)

    # restore document using function
    await document_service.restore_document(created_document.id, user_id)

    doc = await document_service.get_document(created_document.id)
    assert doc is not None
    assert doc.deleted_date is None
    assert doc.id == created_document.id
    assert doc.hash == created_document.hash

    await document_service.delete_document(created_document.id, user_id)

    # recreate document with same name, same content
    with open("new.txt", "rb") as f:
        recreated_document = await document_service.create_document(
            file=f,
            original_path=original_path,
            user_id=user_id,
            folder_id=folders[-2].id,
            tags=tags[1:],
        )
    assert recreated_document is not None
    assert recreated_document.deleted_date is None
    assert recreated_document.hash == created_document.hash
    assert master_id == recreated_document.id

    await document_service.delete_document(recreated_document.id, user_id)

    # cleanup
    cleaning_info = await document_service.cleanup_deleted_documents()

    assert cleaning_info is not None
    assert len(cleaning_info[0]) == 2  # 2 deleted documents
    assert len(cleaning_info[1]) == 0  # 0 not deleted documents

    assert not os.path.exists(
        f"path-to-local-storage/{create_root_folder}/1/2/3/"
        f"{document_service._create_storage_name(
            recreated_document.filename,
            recreated_document.hash,
        )}"
    )
    assert not os.path.exists(
        f"path-to-local-storage/{create_root_folder}/{folder_name}/"
        f"{document_service._create_storage_name(
            created_slave_document.filename,
            created_slave_document.hash,
        )}"
    )
